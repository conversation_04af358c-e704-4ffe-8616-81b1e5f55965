# Admin Platform

A comprehensive admin platform with multi-database support (SQLite, PostgreSQL, MySQL) using Prisma ORM.

## Features

- Multi-database support (SQLite, PostgreSQL, MySQL)
- User authentication and authorization
- Role-based access control (Super Admin, Admin, Moderator)
- Session management
- Admin activity logging
- System settings management
- User profile management
- Manual task management

## Prerequisites

- Node.js 18+ and npm
- One of the following databases:
  - SQLite (no additional setup required)
  - PostgreSQL 12+
  - MySQL 8+

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Configure your environment:
   - Copy `.env.example` to `.env`
   - Edit `.env` with your database and super admin configuration

## Database Setup

This project supports multiple database providers. Use the database switching script to change providers:

### SQLite (Default - Development)

```bash
npm run db:switch sqlite
```

```env
DATABASE_URL=file:./dev.db
```

### PostgreSQL

```bash
npm run db:switch postgresql
```

```env
DATABASE_URL=postgresql://user:password@localhost:5432/database_name
```

### MySQL

```bash
npm run db:switch mysql
```

```env
DATABASE_URL=mysql://user:password@localhost:3306/database_name
```

After switching database providers, remember to:
1. Update your `DATABASE_URL` in `.env`
2. Run `npm run db:generate` to regenerate the Prisma client
3. Run `npm run db:push` or `npm run db:migrate` to apply the schema

## Super Admin Configuration

Edit the following in your `.env` file:

```env
SUPERADMIN_FIRST_NAME=Your
SUPERADMIN_LAST_NAME=Name
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_USERNAME=yourusername
SUPERADMIN_PASSWORD=YourSecurePassword123!
```

**Important:** Change these credentials after your first login!

## Running the Application

### Development Setup

This application uses a **separate frontend and backend process architecture** for development:

- **Frontend**: React application running on port 5173 (Vite dev server)
- **Backend**: Express.js API server running on port 3001

#### Quick Start (Recommended)

1. Generate Prisma client:
   ```bash
   npm run db:generate
   ```

2. Push schema to database:
   ```bash
   npm run db:push
   ```

3. Seed the database with super admin:
   ```bash
   npm run db:seed
   ```

4. Start both frontend and backend concurrently:
   ```bash
   npm run dev:full
   ```

This will start both processes simultaneously and configure the frontend to proxy API calls to the backend server.

#### Manual Setup (Advanced)

If you need to run processes separately:

1. Start the backend API server (Terminal 1):
   ```bash
   npm run server
   ```

2. Start the frontend development server (Terminal 2):
   ```bash
   npm run dev
   ```

The frontend will automatically proxy `/api/v1/*` requests to the backend server running on port 3001.

## Available Scripts

### Development Scripts
- `npm run dev:full` - **Recommended**: Start both frontend and backend concurrently
- `npm run dev` - Start frontend development server only (port 5173)
- `npm run server` - Start backend API server only (port 3001)

### Build Scripts
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Database Scripts
- `npm run db:generate` - Generate Prisma Client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Create and run migrations
- `npm run db:studio` - Open Prisma Studio
- `npm run db:seed` - Seed database with super admin
- `npm run db:switch <provider>` - Switch between database providers (sqlite, postgresql, mysql)

## First Login

After setting up the database and seeding:

1. Navigate to `http://localhost:3000` (frontend development server)
2. Login with your configured super admin credentials
3. **Immediately change your password** from the user profile page

The frontend will automatically communicate with the backend API server running on port 3001.

## Database Migrations

When making schema changes:

1. Update `prisma/schema.prisma`
2. Run `npm run db:migrate` to create a migration
3. The migration will be applied automatically

## Troubleshooting

### Common Development Issues

#### Login Returns 404 Error
**Problem**: Frontend shows 404 error when trying to login.
**Solution**: 
- Ensure the backend server is running on port 3001 (`npm run server`)
- Use `npm run dev:full` to start both processes automatically
- Check that no other process is using port 3001

#### API Calls Fail with Network Errors
**Problem**: Frontend cannot communicate with backend API.
**Solution**:
- Verify both frontend (port 5173) and backend (port 3001) are running
- Check browser console for CORS errors
- Ensure Vite proxy configuration is working (should see proxy logs in terminal)
- Try accessing `http://localhost:3001/api/v1/auth/login` directly to test backend

#### Frontend Build Errors with Server Modules
**Problem**: Build fails with errors about server-side modules.
**Solution**:
- Ensure you're not importing server modules directly in frontend code
- Use the API client (`src/services/api.ts`) for all backend communication
- Check that Vite configuration properly excludes server dependencies

#### Port Already in Use
**Problem**: Cannot start development servers due to port conflicts.
**Solution**:
- Kill processes using ports 3000 or 3001: `lsof -ti:3000 | xargs kill -9`
- Or use different ports by setting environment variables:
  ```bash
  PORT=3002 npm run dev        # Frontend on port 3002
  API_PORT=3003 npm run server # Backend on port 3003
  ```

#### Database Connection Issues
**Problem**: Backend fails to connect to database.
**Solution**:
- Verify your `.env` file has correct `DATABASE_URL`
- Run `npm run db:generate` after changing database configuration
- For SQLite, ensure the database file exists: `npm run db:push`
- Check database provider matches your schema: `npm run db:switch <provider>`

#### Hot Reload Not Working
**Problem**: Changes to frontend code don't automatically refresh.
**Solution**:
- Ensure you're using `npm run dev:full` or `npm run dev` for frontend
- Check that no browser extensions are blocking hot reload
- Clear browser cache and restart development server

#### Session/Authentication Issues
**Problem**: Login works but session doesn't persist or user gets logged out.
**Solution**:
- Verify backend session management is working
- Check browser cookies are being set correctly
- Ensure frontend and backend are on same domain (localhost)
- Clear browser cookies and try again

### Getting Help

If you encounter issues not covered here:
1. Check the browser console for error messages
2. Check terminal output for both frontend and backend processes
3. Verify all environment variables are set correctly
4. Try restarting both development servers

## Security Notes

- Never commit `.env` to version control
- Change default super admin credentials immediately after first login
- Use strong passwords
- Enable 2FA for all admin accounts (when available)
- Regularly review admin activity logs

## Tech Stack

- **Frontend:** React 18, TypeScript, Vite, TailwindCSS
- **Icons:** Lucide React, Heroicons
- **Database:** Prisma ORM with SQLite/PostgreSQL/MySQL support
- **Authentication:** bcryptjs for password hashing
- **HTTP Client:** Axios
- **Date Handling:** date-fns

## License

Proprietay.
