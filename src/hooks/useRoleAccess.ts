import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';

// GuardGeo Platform roles
export enum UserRole {
    SUPER_ADMIN = 'SUPER_ADMIN',
    DEV = 'DEV',
    MARKETING = 'MARKETING',
    SALES = 'SALES'
}

// GuardGeo Platform permissions
export enum Permission {
    // User management permissions
    CREATE_ADMIN_USER = 'CREATE_ADMIN_USER',
    UPDATE_USER = 'UPDATE_USER',
    DELETE_USER = 'DELETE_USER',
    VIEW_USER = 'VIEW_USER',
    MANAGE_USER_ROLES = 'MANAGE_USER_ROLES',

    // System permissions
    VIEW_SYSTEM_SETTINGS = 'VIEW_SYSTEM_SETTINGS',
    UPDATE_SYSTEM_SETTINGS = 'UPDATE_SYSTEM_SETTINGS',
    VIEW_AUDIT_LOGS = 'VIEW_AUDIT_LOGS',
    MANAGE_SESSIONS = '<PERSON>NA<PERSON>_SESSIONS',

    // Security permissions
    VIEW_SECURITY_METRICS = 'VIEW_SECURITY_METRICS',
    MANAGE_SECURITY_SETTINGS = 'MANAGE_SECURITY_SETTINGS',
    VIEW_FAILED_LOGINS = 'VIEW_FAILED_LOGINS',

    // API permissions
    ACCESS_ADMIN_API = 'ACCESS_ADMIN_API',
    MANAGE_API_KEYS = 'MANAGE_API_KEYS',

    // GuardGeo Platform specific permissions
    // Freemius management
    VIEW_FREEMIUS_DATA = 'VIEW_FREEMIUS_DATA',
    MANAGE_FREEMIUS_SYNC = 'MANAGE_FREEMIUS_SYNC',
    VIEW_FREEMIUS_ANALYTICS = 'VIEW_FREEMIUS_ANALYTICS',

    // IP Intelligence
    VIEW_IP_DATA = 'VIEW_IP_DATA',
    PERFORM_IP_LOOKUP = 'PERFORM_IP_LOOKUP',
    MANAGE_IP_DATA = 'MANAGE_IP_DATA',
    VIEW_IP_ANALYTICS = 'VIEW_IP_ANALYTICS',

    // WordPress Plugin API
    ACCESS_PLUGIN_API = 'ACCESS_PLUGIN_API',
    VIEW_API_LOGS = 'VIEW_API_LOGS',

    // Analytics and Reporting
    VIEW_DASHBOARD = 'VIEW_DASHBOARD',
    VIEW_ANALYTICS = 'VIEW_ANALYTICS',
    EXPORT_DATA = 'EXPORT_DATA',

    // System monitoring
    VIEW_SYSTEM_HEALTH = 'VIEW_SYSTEM_HEALTH',
    MANAGE_SYSTEM_MAINTENANCE = 'MANAGE_SYSTEM_MAINTENANCE'
}

// Role-based permissions mapping for GuardGeo Platform
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
    [UserRole.SUPER_ADMIN]: [
        // Super admin has all permissions
        Permission.CREATE_ADMIN_USER,
        Permission.UPDATE_USER,
        Permission.DELETE_USER,
        Permission.VIEW_USER,
        Permission.MANAGE_USER_ROLES,
        Permission.VIEW_SYSTEM_SETTINGS,
        Permission.UPDATE_SYSTEM_SETTINGS,
        Permission.VIEW_AUDIT_LOGS,
        Permission.MANAGE_SESSIONS,
        Permission.VIEW_SECURITY_METRICS,
        Permission.MANAGE_SECURITY_SETTINGS,
        Permission.VIEW_FAILED_LOGINS,
        Permission.ACCESS_ADMIN_API,
        Permission.MANAGE_API_KEYS,

        // GuardGeo Platform permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.MANAGE_FREEMIUS_SYNC,
        Permission.VIEW_FREEMIUS_ANALYTICS,
        Permission.VIEW_IP_DATA,
        Permission.PERFORM_IP_LOOKUP,
        Permission.MANAGE_IP_DATA,
        Permission.VIEW_IP_ANALYTICS,
        Permission.ACCESS_PLUGIN_API,
        Permission.VIEW_API_LOGS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA,
        Permission.VIEW_SYSTEM_HEALTH,
        Permission.MANAGE_SYSTEM_MAINTENANCE
    ],

    [UserRole.DEV]: [
        // Dev role has technical and development-related permissions
        Permission.VIEW_USER,
        Permission.VIEW_SYSTEM_SETTINGS,
        Permission.UPDATE_SYSTEM_SETTINGS,
        Permission.VIEW_AUDIT_LOGS,
        Permission.VIEW_SECURITY_METRICS,
        Permission.ACCESS_ADMIN_API,
        Permission.MANAGE_API_KEYS,

        // Technical GuardGeo permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.MANAGE_FREEMIUS_SYNC,
        Permission.VIEW_IP_DATA,
        Permission.PERFORM_IP_LOOKUP,
        Permission.MANAGE_IP_DATA,
        Permission.ACCESS_PLUGIN_API,
        Permission.VIEW_API_LOGS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA,
        Permission.VIEW_SYSTEM_HEALTH,
        Permission.MANAGE_SYSTEM_MAINTENANCE
    ],

    [UserRole.MARKETING]: [
        // Marketing role has analytics and reporting permissions
        Permission.VIEW_USER,
        Permission.ACCESS_ADMIN_API,

        // Marketing-focused GuardGeo permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.VIEW_FREEMIUS_ANALYTICS,
        Permission.VIEW_IP_ANALYTICS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA
    ],

    [UserRole.SALES]: [
        // Sales role has customer and revenue-focused permissions
        Permission.VIEW_USER,
        Permission.ACCESS_ADMIN_API,

        // Sales-focused GuardGeo permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.VIEW_FREEMIUS_ANALYTICS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA
    ]
};

/**
 * Role hierarchy for GuardGeo Platform
 */
const ROLE_HIERARCHY = {
    [UserRole.SUPER_ADMIN]: 4,
    [UserRole.DEV]: 3,
    [UserRole.MARKETING]: 2,
    [UserRole.SALES]: 1
};

/**
 * Hook for role-based access control
 */
export function useRoleAccess() {
    const { user, isAuthenticated } = useAuth();

    const roleAccess = useMemo(() => {
        if (!isAuthenticated || !user) {
            return {
                hasRole: () => false,
                hasPermission: () => false,
                hasAnyPermission: () => false,
                hasAllPermissions: () => false,
                hasRoleOrHigher: () => false,
                canAccess: () => false,
                userRole: null,
                userPermissions: [],
                roleLevel: 0
            };
        }

        const userRole = user.role as UserRole;
        const userPermissions = ROLE_PERMISSIONS[userRole] || [];
        const roleLevel = ROLE_HIERARCHY[userRole] || 0;

        /**
         * Check if user has specific role
         */
        const hasRole = (role: UserRole): boolean => {
            return userRole === role;
        };

        /**
         * Check if user has specific permission
         */
        const hasPermission = (permission: Permission): boolean => {
            return userPermissions.includes(permission);
        };

        /**
         * Check if user has any of the specified permissions
         */
        const hasAnyPermission = (permissions: Permission[]): boolean => {
            return permissions.some(permission => userPermissions.includes(permission));
        };

        /**
         * Check if user has all specified permissions
         */
        const hasAllPermissions = (permissions: Permission[]): boolean => {
            return permissions.every(permission => userPermissions.includes(permission));
        };

        /**
         * Check if user has role or higher in hierarchy
         */
        const hasRoleOrHigher = (minimumRole: UserRole): boolean => {
            const requiredLevel = ROLE_HIERARCHY[minimumRole] || 0;
            return roleLevel >= requiredLevel;
        };

        /**
         * Generic access check function
         */
        const canAccess = (requirements: {
            roles?: UserRole[];
            permissions?: Permission[];
            requireAll?: boolean; // If true, requires ALL permissions; if false, requires ANY
        }): boolean => {
            const { roles = [], permissions = [], requireAll = false } = requirements;

            // Check role requirements
            if (roles.length > 0) {
                const hasRequiredRole = roles.includes(userRole);
                if (!hasRequiredRole) {
                    return false;
                }
            }

            // Check permission requirements
            if (permissions.length > 0) {
                if (requireAll) {
                    return hasAllPermissions(permissions);
                } else {
                    return hasAnyPermission(permissions);
                }
            }

            return true;
        };

        return {
            hasRole,
            hasPermission,
            hasAnyPermission,
            hasAllPermissions,
            hasRoleOrHigher,
            canAccess,
            userRole,
            userPermissions,
            roleLevel
        };
    }, [isAuthenticated, user]);

    return roleAccess;
}

/**
 * Hook for checking specific GuardGeo feature access
 */
export function useFeatureAccess() {
    const roleAccess = useRoleAccess();

    return useMemo(() => ({
        // Dashboard access
        canViewDashboard: roleAccess.hasPermission(Permission.VIEW_DASHBOARD),

        // Freemius features
        canViewFreemiusData: roleAccess.hasPermission(Permission.VIEW_FREEMIUS_DATA),
        canManageFreemiusSync: roleAccess.hasPermission(Permission.MANAGE_FREEMIUS_SYNC),
        canViewFreemiusAnalytics: roleAccess.hasPermission(Permission.VIEW_FREEMIUS_ANALYTICS),

        // IP Intelligence features
        canViewIpData: roleAccess.hasPermission(Permission.VIEW_IP_DATA),
        canPerformIpLookup: roleAccess.hasPermission(Permission.PERFORM_IP_LOOKUP),
        canManageIpData: roleAccess.hasPermission(Permission.MANAGE_IP_DATA),
        canViewIpAnalytics: roleAccess.hasPermission(Permission.VIEW_IP_ANALYTICS),

        // API and Logs
        canViewApiLogs: roleAccess.hasPermission(Permission.VIEW_API_LOGS),
        canAccessPluginApi: roleAccess.hasPermission(Permission.ACCESS_PLUGIN_API),

        // Analytics and Reporting
        canViewAnalytics: roleAccess.hasPermission(Permission.VIEW_ANALYTICS),
        canExportData: roleAccess.hasPermission(Permission.EXPORT_DATA),

        // System Management
        canViewSystemHealth: roleAccess.hasPermission(Permission.VIEW_SYSTEM_HEALTH),
        canManageSystemMaintenance: roleAccess.hasPermission(Permission.MANAGE_SYSTEM_MAINTENANCE),
        canViewSystemSettings: roleAccess.hasPermission(Permission.VIEW_SYSTEM_SETTINGS),
        canUpdateSystemSettings: roleAccess.hasPermission(Permission.UPDATE_SYSTEM_SETTINGS),

        // User Management
        canCreateAdminUser: roleAccess.hasPermission(Permission.CREATE_ADMIN_USER),
        canUpdateUser: roleAccess.hasPermission(Permission.UPDATE_USER),
        canDeleteUser: roleAccess.hasPermission(Permission.DELETE_USER),
        canViewUser: roleAccess.hasPermission(Permission.VIEW_USER),
        canManageUserRoles: roleAccess.hasPermission(Permission.MANAGE_USER_ROLES),

        // Security
        canViewSecurityMetrics: roleAccess.hasPermission(Permission.VIEW_SECURITY_METRICS),
        canViewAuditLogs: roleAccess.hasPermission(Permission.VIEW_AUDIT_LOGS),
        canManageSessions: roleAccess.hasPermission(Permission.MANAGE_SESSIONS),

        // Role checks
        isSuperAdmin: roleAccess.hasRole(UserRole.SUPER_ADMIN),
        isDev: roleAccess.hasRole(UserRole.DEV),
        isMarketing: roleAccess.hasRole(UserRole.MARKETING),
        isSales: roleAccess.hasRole(UserRole.SALES),

        // Hierarchical checks
        isDevOrHigher: roleAccess.hasRoleOrHigher(UserRole.DEV),
        isMarketingOrHigher: roleAccess.hasRoleOrHigher(UserRole.MARKETING),
        isSalesOrHigher: roleAccess.hasRoleOrHigher(UserRole.SALES),

        // Generic access check
        canAccess: roleAccess.canAccess,

        // User info
        userRole: roleAccess.userRole,
        roleLevel: roleAccess.roleLevel
    }), [roleAccess]);
}

/**
 * Navigation access helper
 */
export function useNavigationAccess() {
    const featureAccess = useFeatureAccess();

    return useMemo(() => ({
        // Main navigation sections
        showDashboard: featureAccess.canViewDashboard,
        showFreemiusSection: featureAccess.canViewFreemiusData,
        showIpIntelligenceSection: featureAccess.canViewIpData,
        showAnalyticsSection: featureAccess.canViewAnalytics,
        showSystemSection: featureAccess.isDevOrHigher,
        showUserManagement: featureAccess.canViewUser,
        showSecuritySection: featureAccess.canViewSecurityMetrics || featureAccess.canViewAuditLogs,

        // Specific menu items
        showFreemiusProducts: featureAccess.canViewFreemiusData,
        showFreemiusInstallations: featureAccess.canViewFreemiusData,
        showFreemiusSync: featureAccess.canManageFreemiusSync,
        showFreemiusAnalytics: featureAccess.canViewFreemiusAnalytics,

        showIpLookup: featureAccess.canPerformIpLookup,
        showIpHistory: featureAccess.canViewIpData,
        showIpAnalytics: featureAccess.canViewIpAnalytics,

        showApiLogs: featureAccess.canViewApiLogs,
        showSystemHealth: featureAccess.canViewSystemHealth,
        showSystemSettings: featureAccess.canViewSystemSettings,

        showAuditLogs: featureAccess.canViewAuditLogs,
        showSecurityMetrics: featureAccess.canViewSecurityMetrics,
        showSessionManagement: featureAccess.canManageSessions,

        showDataExport: featureAccess.canExportData,

        // Admin functions
        showCreateUser: featureAccess.canCreateAdminUser,
        showUserRoleManagement: featureAccess.canManageUserRoles,

        // Role-based sections
        showTechnicalFeatures: featureAccess.isDevOrHigher,
        showAnalyticsFeatures: featureAccess.isMarketingOrHigher,
        showBasicFeatures: featureAccess.isSalesOrHigher
    }), [featureAccess]);
}