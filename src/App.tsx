import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';
import { AdminLayout } from './components/AdminLayout';
import SessionExpiryWarning from './components/SessionExpiryWarning';

// Import pages (we'll create these next)
import LoginPage from './pages/LoginPage';
import TwoFactorAuthPage from './pages/TwoFactorAuthPage';
import DashboardPage from './pages/DashboardPage';
import ApiLogsPage from './pages/ApiLogsPage';
import IpDataPage from './pages/IpDataPage';
import IpLookupPage from './pages/IpLookupPage';
import IpDetailPage from './pages/IpDetailPage';
import IpAnalysisHistoryPage from './pages/IpAnalysisHistoryPage';
import FreemiusProductsPage from './pages/FreemiusProductsPage';
import FreemiusInstallationsPage from './pages/FreemiusInstallationsPage';
import FreemiusSyncPage from './pages/FreemiusSyncPage';
import ManualTasksPage from './pages/ManualTasksPage';
import UserManagementPage from './pages/UserManagementPage';
import AdminUserManagementPage from './pages/AdminUserManagementPage';
import UserProfilePage from './pages/UserProfilePage';
import AdminActivitiesPage from './pages/AdminActivitiesPage';
import QueueManagementPage from './pages/QueueManagementPage';
import SystemSettingsPage from './pages/SystemSettingsPage';
import SecurityAuditPage from './pages/SecurityAuditPage';
import AuditTrailPage from './pages/AuditTrailPage';

function App() {
  return (
    <AuthProvider>
      <Router>
        <SessionExpiryWarning />
        <Routes>
          {/* Public routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/2fa" element={<TwoFactorAuthPage />} />

          {/* Protected admin routes */}
          <Route path="/admin" element={
            <ProtectedRoute>
              <AdminLayout />
            </ProtectedRoute>
          }>
            <Route index element={<DashboardPage />} />
            <Route path="logs" element={<ApiLogsPage />} />
            <Route path="ip-data" element={<IpDataPage />} />
            <Route path="ip-lookup" element={<IpLookupPage />} />
            <Route path="ip-detail/:ip" element={<IpDetailPage />} />
            <Route path="ip-analysis-history" element={<IpAnalysisHistoryPage />} />
            <Route path="freemius/products" element={<FreemiusProductsPage />} />
            <Route path="freemius/installations" element={<FreemiusInstallationsPage />} />
            <Route path="freemius/sync" element={<FreemiusSyncPage />} />
            <Route path="tasks" element={<ManualTasksPage />} />
            <Route path="users" element={
              <ProtectedRoute requiredRole="super_admin">
                <UserManagementPage />
              </ProtectedRoute>
            } />
            <Route path="admin-users" element={
              <ProtectedRoute requiredRole="super_admin">
                <AdminUserManagementPage />
              </ProtectedRoute>
            } />
            <Route path="activities" element={<AdminActivitiesPage />} />
            <Route path="queue" element={<QueueManagementPage />} />
            <Route path="security-audit" element={<SecurityAuditPage />} />
            <Route path="audit-trail" element={<AuditTrailPage />} />
            <Route path="settings" element={
              <ProtectedRoute requiredRole="super_admin">
                <SystemSettingsPage />
              </ProtectedRoute>
            } />
            <Route path="profile" element={<UserProfilePage />} />
          </Route>

          {/* Redirect root to admin */}
          <Route path="/" element={<Navigate to="/admin" replace />} />

          {/* Catch all - redirect to admin */}
          <Route path="*" element={<Navigate to="/admin" replace />} />
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
