import React, { useState } from 'react';
import { Outlet, NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useNavigationAccess, useFeatureAccess } from '../hooks/useRoleAccess';
import { NavigationItem } from './RoleBasedAccess';
import { UserRole, Permission } from '../hooks/useRoleAccess';
import {
  HomeIcon,
  DocumentTextIcon,
  GlobeAltIcon,
  CogIcon,
  UsersIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon,
  CubeIcon,
  ServerIcon,
  Cog6ToothIcon,
  ShieldCheckIcon,
  ChartBarIcon,
  CloudArrowDownIcon,
  ExclamationTriangleIcon,
  ComputerDesktopIcon,
} from '@heroicons/react/24/outline';

// Navigation structure with role-based access control
const navigationSections = [
  {
    name: 'Main',
    items: [
      {
        name: 'Dashboard',
        href: '/admin',
        icon: HomeIcon,
        exact: true,
        permissions: [Permission.VIEW_DASHBOARD]
      },
    ]
  },
  {
    name: 'Freemius Management',
    items: [
      {
        name: 'Products',
        href: '/admin/freemius/products',
        icon: CubeIcon,
        permissions: [Permission.VIEW_FREEMIUS_DATA]
      },
      {
        name: 'Installations',
        href: '/admin/freemius/installations',
        icon: ServerIcon,
        permissions: [Permission.VIEW_FREEMIUS_DATA]
      },
      {
        name: 'Sync Data',
        href: '/admin/freemius/sync',
        icon: Cog6ToothIcon,
        permissions: [Permission.MANAGE_FREEMIUS_SYNC]
      },
      {
        name: 'Analytics',
        href: '/admin/freemius/analytics',
        icon: ChartBarIcon,
        permissions: [Permission.VIEW_FREEMIUS_ANALYTICS]
      },
    ]
  },
  {
    name: 'IP Intelligence',
    items: [
      {
        name: 'IP Data',
        href: '/admin/ip-data',
        icon: GlobeAltIcon,
        permissions: [Permission.VIEW_IP_DATA]
      },
      {
        name: 'IP Lookup',
        href: '/admin/ip-lookup',
        icon: GlobeAltIcon,
        permissions: [Permission.PERFORM_IP_LOOKUP]
      },
      {
        name: 'Request History',
        href: '/admin/ip-analysis-history',
        icon: DocumentTextIcon,
        permissions: [Permission.VIEW_IP_DATA]
      },
      {
        name: 'IP Analytics',
        href: '/admin/ip-analytics',
        icon: ChartBarIcon,
        permissions: [Permission.VIEW_IP_ANALYTICS]
      },
    ]
  },
  {
    name: 'System & Monitoring',
    items: [
      {
        name: 'API Logs',
        href: '/admin/logs',
        icon: DocumentTextIcon,
        permissions: [Permission.VIEW_API_LOGS]
      },
      {
        name: 'System Health',
        href: '/admin/system-health',
        icon: ComputerDesktopIcon,
        permissions: [Permission.VIEW_SYSTEM_HEALTH]
      },
      {
        name: 'System Settings',
        href: '/admin/settings',
        icon: CogIcon,
        permissions: [Permission.VIEW_SYSTEM_SETTINGS]
      },
      {
        name: 'Manual Tasks',
        href: '/admin/tasks',
        icon: CogIcon,
        roles: [UserRole.SUPER_ADMIN, UserRole.DEV]
      },
    ]
  },
  {
    name: 'Security',
    items: [
      {
        name: 'Security Audit',
        href: '/admin/security-audit',
        icon: ShieldCheckIcon,
        permissions: [Permission.VIEW_SECURITY_METRICS]
      },
      {
        name: 'Audit Trail',
        href: '/admin/audit-trail',
        icon: ShieldCheckIcon,
        permissions: [Permission.VIEW_AUDIT_LOGS]
      },
      {
        name: 'Security Monitoring',
        href: '/admin/security-monitoring',
        icon: ExclamationTriangleIcon,
        permissions: [Permission.VIEW_SECURITY_METRICS]
      },
    ]
  },
  {
    name: 'User Management',
    items: [
      {
        name: 'Users',
        href: '/admin/users',
        icon: UsersIcon,
        permissions: [Permission.VIEW_USER]
      },
      {
        name: 'Admin Users',
        href: '/admin/admin-users',
        icon: UsersIcon,
        permissions: [Permission.CREATE_ADMIN_USER]
      },
      {
        name: 'Admin Activities',
        href: '/admin/activities',
        icon: DocumentTextIcon,
        permissions: [Permission.VIEW_AUDIT_LOGS]
      },
    ]
  },
  {
    name: 'Data & Export',
    items: [
      {
        name: 'Data Export',
        href: '/admin/export',
        icon: CloudArrowDownIcon,
        permissions: [Permission.EXPORT_DATA]
      },
      {
        name: 'Queue Management',
        href: '/admin/queue',
        icon: CogIcon,
        roles: [UserRole.SUPER_ADMIN, UserRole.DEV]
      },
    ]
  }
];

export function AdminLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const navigationAccess = useNavigationAccess();
  const featureAccess = useFeatureAccess();

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
      navigate('/login');
    }
  };

  // Filter navigation sections and items based on user permissions
  const filteredNavigationSections = navigationSections.map(section => ({
    ...section,
    items: section.items.filter(item => {
      // Check if user has required roles or permissions
      if (item.roles && item.roles.length > 0) {
        return item.roles.includes(featureAccess.userRole as UserRole);
      }
      if (item.permissions && item.permissions.length > 0) {
        return item.permissions.some(permission =>
          featureAccess.canAccess({ permissions: [permission] })
        );
      }
      return true; // Show item if no specific requirements
    })
  })).filter(section => section.items.length > 0); // Only show sections with visible items

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Mobile sidebar */}
      <div className={`fixed inset-0 flex z-40 md:hidden ${sidebarOpen ? '' : 'pointer-events-none'}`}>
        <div className={`fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity ease-linear duration-300 ${sidebarOpen ? 'opacity-100' : 'opacity-0'}`} onClick={() => setSidebarOpen(false)} />

        <div className={`relative flex-1 flex flex-col max-w-xs w-full bg-white transform transition ease-in-out duration-300 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              type="button"
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>

          <div className="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
            <div className="flex-shrink-0 flex items-center px-4">
              <div className="flex items-center">
                <div className="flex-shrink-0 h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">GG</span>
                </div>
                <span className="ml-2 text-xl font-bold text-gray-900">GuardGeo</span>
              </div>
            </div>
            <nav className="mt-5 px-2 space-y-1">
              {filteredNavigationSections.map((section) => (
                <div key={section.name} className="space-y-1">
                  {section.name !== 'Main' && (
                    <div className="px-2 py-2">
                      <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                        {section.name}
                      </h3>
                    </div>
                  )}
                  {section.items.map((item) => (
                    <NavLink
                      key={item.name}
                      to={item.href}
                      end={item.exact}
                      className={({ isActive }) =>
                        `group flex items-center px-2 py-2 text-base font-medium rounded-md transition-colors duration-150 ${isActive
                          ? 'bg-indigo-100 text-indigo-900'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                        }`
                      }
                    >
                      <item.icon className="mr-4 h-6 w-6" />
                      {item.name}
                    </NavLink>
                  ))}
                </div>
              ))}
            </nav>
          </div>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <div className="flex flex-col h-0 flex-1 border-r border-gray-200 bg-white">
            <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
              <div className="flex items-center flex-shrink-0 px-4">
                <div className="flex items-center">
                  <div className="flex-shrink-0 h-8 w-8 bg-indigo-600 rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">GG</span>
                  </div>
                  <span className="ml-2 text-xl font-bold text-gray-900">GuardGeo</span>
                </div>
              </div>
              <nav className="mt-5 flex-1 px-2 space-y-1">
                {filteredNavigationSections.map((section) => (
                  <div key={section.name} className="space-y-1">
                    {section.name !== 'Main' && (
                      <div className="px-2 py-2">
                        <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider">
                          {section.name}
                        </h3>
                      </div>
                    )}
                    {section.items.map((item) => (
                      <NavLink
                        key={item.name}
                        to={item.href}
                        end={item.exact}
                        className={({ isActive }) =>
                          `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-150 ${isActive
                            ? 'bg-indigo-100 text-indigo-900'
                            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                          }`
                        }
                      >
                        <item.icon className="mr-3 h-5 w-5" />
                        {item.name}
                      </NavLink>
                    ))}
                  </div>
                ))}
              </nav>
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Top navigation */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            type="button"
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Bars3Icon className="h-6 w-6" />
          </button>

          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex">
              <div className="w-full flex md:ml-0">
                <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                  <div className="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                    <span className="text-sm font-medium text-gray-900">
                      GuardGeo IP Intelligence Platform
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="ml-4 flex items-center md:ml-6">
              <div className="relative ml-3">
                <div className="flex items-center space-x-4">
                  <NavLink
                    to="/admin/profile"
                    className="flex items-center text-sm text-gray-700 hover:text-gray-900"
                  >
                    <UserIcon className="h-5 w-5 mr-2" />
                    <div className="flex flex-col">
                      <span>{user?.email}</span>
                      <span className="text-xs text-gray-500">{user?.role}</span>
                    </div>
                  </NavLink>

                  <button
                    onClick={handleLogout}
                    className="flex items-center text-sm text-gray-700 hover:text-gray-900"
                  >
                    <ArrowRightOnRectangleIcon className="h-5 w-5 mr-2" />
                    Logout
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              <Outlet />
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}