import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useRoleAccess, UserRole, Permission } from '../hooks/useRoleAccess';
import UnauthorizedPage from '../pages/UnauthorizedPage';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
  requiredPermissions?: Permission[];
  requireAll?: boolean; // If true, requires ALL permissions; if false, requires ANY
  fallbackPath?: string; // Custom redirect path for unauthorized access
  showUnauthorizedPage?: boolean; // Show unauthorized page instead of redirecting
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
  requiredPermissions = [],
  requireAll = false,
  fallbackPath,
  showUnauthorizedPage = true
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, requires2FA } = useAuth();
  const roleAccess = useRoleAccess();
  const location = useLocation();

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // 2FA required
  if (requires2FA) {
    return <Navigate to="/2fa" state={{ from: location }} replace />;
  }

  // Not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role and permission requirements
  const hasAccess = roleAccess.canAccess({
    roles: requiredRoles,
    permissions: requiredPermissions,
    requireAll
  });

  if (!hasAccess) {
    // Custom redirect path
    if (fallbackPath && !showUnauthorizedPage) {
      return <Navigate to={fallbackPath} state={{ from: location }} replace />;
    }

    // Show unauthorized page
    if (showUnauthorizedPage) {
      return (
        <UnauthorizedPage
          requiredRole={requiredRoles.length > 0 ? requiredRoles.join(' or ') : undefined}
          requiredPermissions={requiredPermissions.map(p => p.toString())}
          message="You don't have the required permissions to access this page."
        />
      );
    }

    // Default unauthorized component (fallback)
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            You don't have permission to access this page.
          </p>
          <div className="mt-4">
            <p className="text-xs text-gray-400">
              Current role: {roleAccess.userRole || 'Unknown'}
            </p>
            {requiredRoles.length > 0 && (
              <p className="text-xs text-gray-400">
                Required roles: {requiredRoles.join(', ')}
              </p>
            )}
            {requiredPermissions.length > 0 && (
              <p className="text-xs text-gray-400">
                Required permissions: {requiredPermissions.join(', ')}
              </p>
            )}
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

/**
 * Convenience components for common role-based route protection
 */

// Super Admin only routes
export function SuperAdminRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRoles={[UserRole.SUPER_ADMIN]}>
      {children}
    </ProtectedRoute>
  );
}

// Dev and above routes
export function DevRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRoles={[UserRole.SUPER_ADMIN, UserRole.DEV]}>
      {children}
    </ProtectedRoute>
  );
}

// Marketing and above routes
export function MarketingRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRoles={[UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING]}>
      {children}
    </ProtectedRoute>
  );
}

// Sales and above routes (all authenticated users)
export function SalesRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredRoles={[UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING, UserRole.SALES]}>
      {children}
    </ProtectedRoute>
  );
}

/**
 * Feature-specific route protection
 */

// Freemius management routes
export function FreemiusRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_FREEMIUS_DATA]}>
      {children}
    </ProtectedRoute>
  );
}

// IP Intelligence routes
export function IpIntelligenceRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_IP_DATA]}>
      {children}
    </ProtectedRoute>
  );
}

// Analytics routes
export function AnalyticsRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_ANALYTICS]}>
      {children}
    </ProtectedRoute>
  );
}

// System management routes
export function SystemRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_SYSTEM_SETTINGS, Permission.VIEW_SYSTEM_HEALTH]}>
      {children}
    </ProtectedRoute>
  );
}

// Security monitoring routes
export function SecurityRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_SECURITY_METRICS, Permission.VIEW_AUDIT_LOGS]}>
      {children}
    </ProtectedRoute>
  );
}

// User management routes
export function UserManagementRoute({ children }: { children: React.ReactNode }) {
  return (
    <ProtectedRoute requiredPermissions={[Permission.VIEW_USER]}>
      {children}
    </ProtectedRoute>
  );
}