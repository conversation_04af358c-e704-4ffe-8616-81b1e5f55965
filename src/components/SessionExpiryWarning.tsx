import { useState, useEffect } from 'react';
import { ExclamationTriangleIcon, ClockIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';

export default function SessionExpiryWarning() {
    const { sessionExpiryWarning, dismissSessionWarning, refreshSession } = useAuth();
    const [timeRemaining, setTimeRemaining] = useState<string>('');
    const [isRefreshing, setIsRefreshing] = useState(false);

    useEffect(() => {
        if (!sessionExpiryWarning?.show || !sessionExpiryWarning.expiresAt) {
            return;
        }

        const updateTimer = () => {
            const now = Date.now();
            const expiresAt = sessionExpiryWarning.expiresAt.getTime();
            const remaining = expiresAt - now;

            if (remaining <= 0) {
                setTimeRemaining('Expired');
                return;
            }

            const minutes = Math.floor(remaining / 60000);
            const seconds = Math.floor((remaining % 60000) / 1000);
            setTimeRemaining(`${minutes}:${seconds.toString().padStart(2, '0')}`);
        };

        updateTimer();
        const interval = setInterval(updateTimer, 1000);

        return () => clearInterval(interval);
    }, [sessionExpiryWarning]);

    const handleRefreshSession = async () => {
        setIsRefreshing(true);
        try {
            await refreshSession();
        } catch (error) {
            console.error('Failed to refresh session:', error);
        } finally {
            setIsRefreshing(false);
        }
    };

    if (!sessionExpiryWarning?.show) {
        return null;
    }

    return (
        <div className="fixed top-4 right-4 z-50 max-w-sm w-full bg-yellow-50 border border-yellow-200 rounded-lg shadow-lg p-4">
            <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400 flex-shrink-0" />
                <div className="ml-3 flex-1">
                    <h3 className="text-sm font-medium text-yellow-800">
                        Session Expiring Soon
                    </h3>
                    <div className="mt-2 text-sm text-yellow-700">
                        <div className="flex items-center mb-2">
                            <ClockIcon className="h-4 w-4 mr-1" />
                            <span className="font-mono">{timeRemaining}</span>
                        </div>
                        <p>Your session will expire soon. Would you like to extend it?</p>
                    </div>
                    <div className="mt-3 flex space-x-2">
                        {sessionExpiryWarning.canRefresh && (
                            <button
                                type="button"
                                onClick={handleRefreshSession}
                                disabled={isRefreshing}
                                className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-yellow-800 bg-yellow-100 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
                            >
                                {isRefreshing ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Refreshing...
                                    </>
                                ) : (
                                    'Extend Session'
                                )}
                            </button>
                        )}
                        <button
                            type="button"
                            onClick={dismissSessionWarning}
                            className="inline-flex items-center px-3 py-1.5 border border-yellow-300 text-xs font-medium rounded text-yellow-800 bg-white hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                        >
                            Dismiss
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}