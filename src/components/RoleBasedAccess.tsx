import React, { ReactNode } from 'react';
import { useRoleAccess, useFeatureAccess, UserRole, Permission } from '../hooks/useRoleAccess';

interface RoleBasedAccessProps {
    children: ReactNode;
    roles?: UserRole[];
    permissions?: Permission[];
    requireAll?: boolean; // If true, requires ALL permissions; if false, requires ANY
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}

/**
 * Component for role-based conditional rendering
 */
export function RoleBasedAccess({
    children,
    roles = [],
    permissions = [],
    requireAll = false,
    fallback = null,
    showUnauthorized = false
}: RoleBasedAccessProps) {
    const roleAccess = useRoleAccess();

    const hasAccess = roleAccess.canAccess({
        roles,
        permissions,
        requireAll
    });

    if (hasAccess) {
        return <>{children}</>;
    }

    if (showUnauthorized) {
        return (
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                    <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                    </div>
                    <div className="ml-3">
                        <h3 className="text-sm font-medium text-red-800">
                            Access Denied
                        </h3>
                        <div className="mt-2 text-sm text-red-700">
                            <p>You don't have permission to access this feature.</p>
                            {roleAccess.userRole && (
                                <p className="mt-1">Current role: <span className="font-medium">{roleAccess.userRole}</span></p>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    return <>{fallback}</>;
}

/**
 * Higher-order component for role-based access control
 */
export function withRoleAccess<P extends object>(
    Component: React.ComponentType<P>,
    accessConfig: {
        roles?: UserRole[];
        permissions?: Permission[];
        requireAll?: boolean;
        fallback?: ReactNode;
        showUnauthorized?: boolean;
    }
) {
    return function WrappedComponent(props: P) {
        return (
            <RoleBasedAccess {...accessConfig}>
                <Component {...props} />
            </RoleBasedAccess>
        );
    };
}

/**
 * Specific role-based components for common use cases
 */

// Super Admin only
export function SuperAdminOnly({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            roles={[UserRole.SUPER_ADMIN]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// Dev and above
export function DevAndAbove({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            roles={[UserRole.SUPER_ADMIN, UserRole.DEV]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// Marketing and above
export function MarketingAndAbove({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            roles={[UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// Sales and above (all authenticated users)
export function SalesAndAbove({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            roles={[UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING, UserRole.SALES]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

/**
 * Feature-specific access components
 */

// Freemius management
export function FreemiusAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.VIEW_FREEMIUS_DATA]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

export function FreemiusManagementAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.MANAGE_FREEMIUS_SYNC]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// IP Intelligence
export function IpDataAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.VIEW_IP_DATA]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

export function IpLookupAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.PERFORM_IP_LOOKUP]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// Analytics
export function AnalyticsAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.VIEW_ANALYTICS]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// System management
export function SystemAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.VIEW_SYSTEM_SETTINGS, Permission.VIEW_SYSTEM_HEALTH]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// Security monitoring
export function SecurityAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.VIEW_SECURITY_METRICS, Permission.VIEW_AUDIT_LOGS]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

// User management
export function UserManagementAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.VIEW_USER]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

export function AdminUserCreationAccess({ children, fallback, showUnauthorized }: {
    children: ReactNode;
    fallback?: ReactNode;
    showUnauthorized?: boolean;
}) {
    return (
        <RoleBasedAccess
            permissions={[Permission.CREATE_ADMIN_USER]}
            fallback={fallback}
            showUnauthorized={showUnauthorized}
        >
            {children}
        </RoleBasedAccess>
    );
}

/**
 * Navigation item component with role-based visibility
 */
interface NavigationItemProps {
    children: ReactNode;
    roles?: UserRole[];
    permissions?: Permission[];
    requireAll?: boolean;
    className?: string;
    onClick?: () => void;
}

export function NavigationItem({
    children,
    roles,
    permissions,
    requireAll,
    className,
    onClick
}: NavigationItemProps) {
    const roleAccess = useRoleAccess();

    const hasAccess = roleAccess.canAccess({
        roles: roles || [],
        permissions: permissions || [],
        requireAll
    });

    if (!hasAccess) {
        return null;
    }

    return (
        <div className={className} onClick={onClick}>
            {children}
        </div>
    );
}

/**
 * Button component with role-based enabling/disabling
 */
interface RoleBasedButtonProps {
    children: ReactNode;
    roles?: UserRole[];
    permissions?: Permission[];
    requireAll?: boolean;
    className?: string;
    disabledClassName?: string;
    onClick?: () => void;
    disabled?: boolean;
    showTooltip?: boolean;
    tooltipMessage?: string;
}

export function RoleBasedButton({
    children,
    roles,
    permissions,
    requireAll,
    className = '',
    disabledClassName = 'opacity-50 cursor-not-allowed',
    onClick,
    disabled = false,
    showTooltip = true,
    tooltipMessage = 'You don\'t have permission to perform this action'
}: RoleBasedButtonProps) {
    const roleAccess = useRoleAccess();

    const hasAccess = roleAccess.canAccess({
        roles: roles || [],
        permissions: permissions || [],
        requireAll
    });

    const isDisabled = disabled || !hasAccess;
    const buttonClassName = isDisabled ? `${className} ${disabledClassName}` : className;

    const handleClick = () => {
        if (!isDisabled && onClick) {
            onClick();
        }
    };

    const button = (
        <button
            className={buttonClassName}
            onClick={handleClick}
            disabled={isDisabled}
        >
            {children}
        </button>
    );

    if (isDisabled && showTooltip && !hasAccess) {
        return (
            <div className="relative group">
                {button}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
                    {tooltipMessage}
                </div>
            </div>
        );
    }

    return button;
}