import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { CreateAdminRequest } from '../types';
import { UserPlusIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

interface AdminUserCreationFormProps {
    onSuccess?: (result: any) => void;
    onCancel?: () => void;
}

export default function AdminUserCreationForm({ onSuccess, onCancel }: AdminUserCreationFormProps) {
    const [formData, setFormData] = useState<CreateAdminRequest>({
        email: '',
        firstName: '',
        lastName: '',
        role: 'ADMIN',
        temporaryPassword: '',
        requirePasswordChange: true,
    });
    const [errors, setErrors] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [showPassword, setShowPassword] = useState(false);
    const [generatePassword, setGeneratePassword] = useState(true);

    const { createAdminUser } = useAuth();



    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        const checked = (e.target as HTMLInputElement).checked;

        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleGeneratePasswordToggle = (generate: boolean) => {
        setGeneratePassword(generate);
        if (generate) {
            setFormData(prev => ({ ...prev, temporaryPassword: '' }));
        }
    };

    const validateForm = (): string[] => {
        const errors: string[] = [];

        if (!formData.email.trim()) {
            errors.push('Email is required');
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            errors.push('Please enter a valid email address');
        }

        if (!formData.firstName.trim()) {
            errors.push('First name is required');
        }

        if (!formData.lastName.trim()) {
            errors.push('Last name is required');
        }

        if (!generatePassword && !formData.temporaryPassword) {
            errors.push('Temporary password is required when not auto-generating');
        }

        if (!generatePassword && formData.temporaryPassword && formData.temporaryPassword.length < 12) {
            errors.push('Password must be at least 12 characters long');
        }

        return errors;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        const validationErrors = validateForm();
        if (validationErrors.length > 0) {
            setErrors(validationErrors);
            return;
        }

        setIsLoading(true);
        setErrors([]);

        try {
            const submitData: CreateAdminRequest = {
                ...formData,
                temporaryPassword: generatePassword ? undefined : formData.temporaryPassword,
            };

            const result = await createAdminUser(submitData);

            if (result.success) {
                onSuccess?.(result);
            } else {
                setErrors([result.error || 'Failed to create admin user']);
            }
        } catch (error: any) {
            setErrors([error.message || 'Failed to create admin user']);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="bg-white shadow sm:rounded-lg">
            <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center mb-4">
                    <UserPlusIcon className="h-6 w-6 text-indigo-600 mr-2" />
                    <h3 className="text-lg leading-6 font-medium text-gray-900">
                        Create Admin User
                    </h3>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    {errors.length > 0 && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-4">
                            <div className="flex">
                                <div className="ml-3">
                                    <h3 className="text-sm font-medium text-red-800">
                                        {errors.length === 1 ? 'Error' : 'Errors'}
                                    </h3>
                                    <div className="mt-2 text-sm text-red-700">
                                        <ul className="list-disc pl-5 space-y-1">
                                            {errors.map((error, index) => (
                                                <li key={index}>{error}</li>
                                            ))}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}

                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label htmlFor="firstName" className="block text-sm font-medium text-gray-700">
                                First Name
                            </label>
                            <input
                                type="text"
                                name="firstName"
                                id="firstName"
                                required
                                value={formData.firstName}
                                onChange={handleInputChange}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="Enter first name"
                            />
                        </div>

                        <div>
                            <label htmlFor="lastName" className="block text-sm font-medium text-gray-700">
                                Last Name
                            </label>
                            <input
                                type="text"
                                name="lastName"
                                id="lastName"
                                required
                                value={formData.lastName}
                                onChange={handleInputChange}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="Enter last name"
                            />
                        </div>
                    </div>

                    <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                            Email Address
                        </label>
                        <input
                            type="email"
                            name="email"
                            id="email"
                            required
                            value={formData.email}
                            onChange={handleInputChange}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            placeholder="Enter email address"
                        />
                    </div>

                    <div>
                        <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                            Role
                        </label>
                        <select
                            name="role"
                            id="role"
                            value={formData.role}
                            onChange={handleInputChange}
                            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        >
                            <option value="ADMIN">Admin</option>
                            <option value="MODERATOR">Moderator</option>
                        </select>
                        <p className="mt-1 text-sm text-gray-500">
                            Admin: Full administrative access. Moderator: Limited administrative access.
                        </p>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-3">
                            Password Setup
                        </label>
                        <div className="space-y-3">
                            <div className="flex items-center">
                                <input
                                    id="generate-password"
                                    name="generate-password"
                                    type="radio"
                                    checked={generatePassword}
                                    onChange={() => handleGeneratePasswordToggle(true)}
                                    className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                                />
                                <label htmlFor="generate-password" className="ml-3 block text-sm text-gray-700">
                                    Auto-generate secure password (recommended)
                                </label>
                            </div>
                            <div className="flex items-center">
                                <input
                                    id="custom-password"
                                    name="custom-password"
                                    type="radio"
                                    checked={!generatePassword}
                                    onChange={() => handleGeneratePasswordToggle(false)}
                                    className="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300"
                                />
                                <label htmlFor="custom-password" className="ml-3 block text-sm text-gray-700">
                                    Set custom temporary password
                                </label>
                            </div>
                        </div>

                        {!generatePassword && (
                            <div className="mt-3">
                                <div className="relative">
                                    <input
                                        type={showPassword ? 'text' : 'password'}
                                        name="temporaryPassword"
                                        value={formData.temporaryPassword}
                                        onChange={handleInputChange}
                                        className="block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                        placeholder="Enter temporary password (min 12 characters)"
                                    />
                                    <button
                                        type="button"
                                        onClick={() => setShowPassword(!showPassword)}
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                                    >
                                        {showPassword ? (
                                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                                        ) : (
                                            <EyeIcon className="h-5 w-5 text-gray-400" />
                                        )}
                                    </button>
                                </div>
                                <p className="mt-1 text-sm text-gray-500">
                                    Password must be at least 12 characters with uppercase, lowercase, numbers, and special characters.
                                </p>
                            </div>
                        )}
                    </div>

                    <div className="flex items-center">
                        <input
                            id="requirePasswordChange"
                            name="requirePasswordChange"
                            type="checkbox"
                            checked={formData.requirePasswordChange}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label htmlFor="requirePasswordChange" className="ml-2 block text-sm text-gray-700">
                            Require password change on first login (recommended)
                        </label>
                    </div>

                    <div className="flex justify-end space-x-3">
                        {onCancel && (
                            <button
                                type="button"
                                onClick={onCancel}
                                className="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Cancel
                            </button>
                        )}
                        <button
                            type="submit"
                            disabled={isLoading}
                            className="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            {isLoading ? (
                                <>
                                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    Creating...
                                </>
                            ) : (
                                'Create Admin User'
                            )}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    );
}