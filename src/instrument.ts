// Import with `import * as Sentry from "@sentry/node"` if you are using ESM
import * as Sentry from "@sentry/node";
import { nodeProfilingIntegration } from "@sentry/profiling-node";

Sentry.init({
    dsn: process.env.SENTRY_DSN || "https://<EMAIL>/4510143522209872",
    integrations: [
        nodeProfilingIntegration(),
    ],
    // Send structured logs to Sentry
    enableLogs: true,
    // Tracing
    tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0, // Capture 100% of the transactions in dev, 10% in prod
    // Set sampling rate for profiling - this is evaluated only once per SDK.init call
    profileSessionSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
    // Trace lifecycle automatically enables profiling during active traces
    profileLifecycle: 'trace',
    // Setting this option to true will send default PII data to Sentry.
    // For example, automatic IP address collection on events
    sendDefaultPii: true,
    // Environment
    environment: process.env.NODE_ENV || 'development',
    // Filter sensitive data
    beforeSend(event) {
        // Filter sensitive data from request data
        if (event.request?.data) {
            event.request.data = filterSensitiveData(event.request.data);
        }
        // Filter sensitive data from extra context
        if (event.extra) {
            event.extra = filterSensitiveData(event.extra);
        }
        return event;
    }
});

/**
 * Filter sensitive data from logs
 */
function filterSensitiveData(data: any): any {
    if (typeof data !== 'object' || data === null) {
        return data;
    }

    const sensitiveFields = [
        'password', 'token', 'secret', 'key', 'authorization',
        'cookie', 'session', 'api_key', 'apiKey', 'secretKey',
        'Authorization', 'Cookie', 'Set-Cookie'
    ];

    const filtered = { ...data };

    for (const field of sensitiveFields) {
        if (field in filtered) {
            filtered[field] = '[REDACTED]';
        }
    }

    // Also filter nested objects
    for (const key in filtered) {
        if (typeof filtered[key] === 'object' && filtered[key] !== null) {
            filtered[key] = filterSensitiveData(filtered[key]);
        }
    }

    return filtered;
}

// Profiling happens automatically after setting it up with `Sentry.init()`.
// All spans (unless those discarded by sampling) will have profiling data attached to them.
export { Sentry };