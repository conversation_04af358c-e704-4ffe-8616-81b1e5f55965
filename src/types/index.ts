// Core API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Authentication Types
export interface User {
  id: string;
  email: string;
  role: 'super_admin' | 'dev' | 'marketing' | 'sales';
  created_at: string;
  updated_at: string;
  is_2fa_enabled: boolean;
  last_login?: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  requires2FA: boolean;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface TwoFactorCredentials {
  token: string;
}

// Freemius API Types (Based on official API documentation)
export interface FreemiusProduct {
  secret_key: string;
  public_key: string;
  id: string;
  created: string;
  updated: string | null;
  parent_plugin_id: string | null;
  developer_id: string;
  store_id: string;
  slug: string;
  title: string;
  environment: 0 | 1;
  icon: string | null;
  default_plan_id: string;
  plans: string;
  features: string;
  money_back_period: number;
  refund_policy: 'flexible' | 'moderate' | 'strict';
  annual_renewals_discount: number | null;
  renewals_discount_type: 'percentage' | 'dollar';
  is_released: boolean;
  is_sdk_required: boolean;
  is_pricing_visible: boolean;
  is_wp_org_compliant: boolean;
  installs_count: number;
  active_installs_count: number;
  free_releases_count: number;
  premium_releases_count: number;
  total_purchases: number;
  total_subscriptions: number;
  total_renewals: number;
  total_failed_purchases: string;
  earnings: string;
  type: 'plugin' | 'theme' | 'widget' | 'template';
  is_static: boolean;
}

export interface FreemiusInstallation {
  secret_key: string;
  public_key: string;
  id: string;
  created: string;
  updated: string | null;
  site_id: string;
  plugin_id: string;
  user_id: string;
  url: string | null;
  title: string | null;
  version: string;
  plan_id: string;
  license_id: string | null;
  trial_plan_id: string;
  trial_ends: string | null;
  subscription_id: string | null;
  gross: number;
  country_code: string | null;
  language: string | null;
  platform_version: string | null;
  sdk_version: string | null;
  programming_language_version: string | null;
  is_active: boolean;
  is_disconnected: boolean;
  is_premium: boolean;
  is_uninstalled: boolean;
  is_locked: boolean;
  source: 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11;
  upgraded: string | null;
  last_seen_at: string | null;
  last_served_update_version: string | null;
  is_beta: boolean;
}

export interface FreemiusEvent {
  id: string;
  created: string;
  updated: string | null;
  type: string;
  developer_id: string;
  plugin_id: string | null;
  user_id: string | null;
  install_id: string | null;
  data: any | null;
  event_trigger: 'system' | 'developer' | 'plugin' | 'user' | 'install';
  process_time: string | null;
  objects: {
    user?: any;
    install?: any;
    license?: any;
    payment?: any;
    [key: string]: any;
  };
}

// ipRegistry API Types (Based on official API response structure)
export interface IpRegistryCarrier {
  name: string | null;
  mcc: string | null;
  mnc: string | null;
}

export interface IpRegistryCompany {
  domain: string;
  name: string;
  type: string;
}

export interface IpRegistryConnection {
  asn: number;
  domain: string;
  organization: string;
  route: string;
  type: string;
}

export interface IpRegistryCurrencyFormat {
  decimal_separator: string;
  group_separator: string;
  negative: {
    prefix: string;
    suffix: string;
  };
  positive: {
    prefix: string;
    suffix: string;
  };
}

export interface IpRegistryCurrency {
  code: string;
  name: string;
  name_native: string;
  plural: string;
  plural_native: string;
  symbol: string;
  symbol_native: string;
  format: IpRegistryCurrencyFormat;
}

export interface IpRegistryFlag {
  emoji: string;
  emoji_unicode: string;
  emojitwo: string;
  noto: string;
  twemoji: string;
  wikimedia: string;
}

export interface IpRegistryLanguage {
  code: string;
  name: string;
  native: string;
}

export interface IpRegistryContinent {
  code: string;
  name: string;
}

export interface IpRegistryCountry {
  area: number;
  borders: string[];
  calling_code: string;
  capital: string;
  code: string;
  name: string;
  population: number;
  population_density: number;
  flag: IpRegistryFlag;
  languages: IpRegistryLanguage[];
  tld: string;
}

export interface IpRegistryRegion {
  code: string;
  name: string;
}

export interface IpRegistryLocation {
  continent: IpRegistryContinent;
  country: IpRegistryCountry;
  region: IpRegistryRegion;
  city: string;
  postal: string;
  latitude: number;
  longitude: number;
  language: IpRegistryLanguage;
  in_eu: boolean;
}

export interface IpRegistrySecurity {
  is_abuser: boolean;
  is_attacker: boolean;
  is_bogon: boolean;
  is_cloud_provider: boolean;
  is_proxy: boolean;
  is_relay: boolean;
  is_tor: boolean;
  is_tor_exit: boolean;
  is_vpn: boolean;
  is_anonymous: boolean;
  is_threat: boolean;
}

export interface IpRegistryTimeZone {
  id: string;
  abbreviation: string;
  current_time: string;
  name: string;
  offset: number;
  in_daylight_saving: boolean;
}

export interface IpRegistryData {
  ip: string;
  type: 'IPv4' | 'IPv6';
  hostname: string | null;
  carrier: IpRegistryCarrier;
  company: IpRegistryCompany;
  connection: IpRegistryConnection;
  currency: IpRegistryCurrency;
  location: IpRegistryLocation;
  security: IpRegistrySecurity;
  time_zone: IpRegistryTimeZone;
}

// Database Models
export interface IpData {
  id: string;
  ip: string;
  data: IpRegistryData;
  created_at: string;
  updated_at: string;
  last_refreshed_at: string;
}

export interface ApiRequestLog {
  id: string;
  ip: string;
  visitor_hash: string;
  plugin_id: string;
  install_id: string;
  url: string;
  request_payload: any;
  response_status: number;
  response_data: any;
  validation_result: 'success' | 'failed';
  validation_errors: string[] | null;
  processing_time_ms: number;
  created_at: string;
}

export interface WebhookLog {
  id: string;
  event_type: string;
  event_id: string;
  payload: FreemiusEvent;
  signature: string;
  processing_status: 'pending' | 'processed' | 'failed';
  processing_error: string | null;
  processed_at: string | null;
  created_at: string;
}

// API Request/Response Types
export interface AnalyzeIpRequest {
  ip: string;
  visitor_hash: string;
  plugin_id: number;
  install_id: number;
  url: string;
}

export interface AnalyzeIpResponse {
  ip_data: IpRegistryData;
  cached: boolean;
  cache_age_hours: number;
}

// Dashboard Statistics
export interface DashboardStats {
  total_api_requests_today: number;
  total_api_requests_this_month: number;
  unique_ips_today: number;
  unique_ips_this_month: number;
  active_installations: number;
  total_products: number;
  cache_hit_rate_percentage: number;
  average_response_time_ms: number;
  recent_requests: ApiRequestLog[];
  top_countries: Array<{
    country: string;
    count: number;
  }>;
  threat_detections_today: number;
}

// Search and Filter Types
export interface ApiLogFilters {
  ip?: string;
  plugin_id?: string;
  install_id?: string;
  url?: string;
  date_from?: string;
  date_to?: string;
  validation_result?: 'success' | 'failed';
  page?: number;
  limit?: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
}

// Manual Task Types
export interface ManualTask {
  id: string;
  type: 'refresh_all_ips' | 'sync_freemius_data' | 'cleanup_old_logs';
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress_percentage: number;
  started_at: string | null;
  completed_at: string | null;
  error_message: string | null;
  created_by: string;
  created_at: string;
}

// Profile and Settings Types
export interface UserProfileSettings {
  id: string;
  user_id: string;
  display_name: string | null;
  avatar_url: string | null;
  timezone: string;
  date_format: string;
  time_format: string;
  language: string;
  theme: string;
  email_notifications: boolean;
  security_alerts: boolean;
  system_updates: boolean;
  activity_digest: string;
  show_activity: boolean;
  session_timeout: number;
  created_at: string;
  updated_at: string;
}

export interface UserProfileUpdate {
  display_name?: string;
  avatar_url?: string;
  timezone?: string;
  date_format?: string;
  time_format?: string;
  language?: string;
  theme?: string;
  email_notifications?: boolean;
  security_alerts?: boolean;
  system_updates?: boolean;
  activity_digest?: string;
  show_activity?: boolean;
  session_timeout?: number;
}

export interface SystemSetting {
  id: string;
  key: string;
  value: any;
  category: string;
  description: string | null;
  data_type: string;
  is_public: boolean;
  requires_restart: boolean;
  validation_rules: any;
  created_by: string;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
  created_by_user?: { email: string };
  updated_by_user?: { email: string };
}

export interface SettingsChangeLog {
  id: string;
  setting_key: string;
  old_value: any;
  new_value: any;
  changed_by: string;
  changed_by_user: { email: string; role: string };
  change_reason: string | null;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}

export interface UserSession {
  id: string;
  user_id: string;
  session_token: string;
  refresh_token: string | null;
  ip_address: string;
  user_agent: string | null;
  location: string | null;
  device_fingerprint: string | null;
  is_active: boolean;
  last_activity: string;
  expires_at: string;
  created_at: string;
}

// User Management Types
export interface CreateUserRequest {
  email: string;
  password: string;
  role: 'dev' | 'marketing' | 'sales';
}

export interface UpdateUserRequest {
  email?: string;
  role?: 'dev' | 'marketing' | 'sales';
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

// Admin Management Types
export interface CreateAdminRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: 'ADMIN' | 'MODERATOR';
  temporaryPassword?: string;
  requirePasswordChange?: boolean;
}

export interface UpdateAdminRequest {
  email?: string;
  firstName?: string;
  lastName?: string;
  role?: 'ADMIN' | 'MODERATOR';
  isActive?: boolean;
}

export interface AdminUser {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  role: 'SUPER_ADMIN' | 'ADMIN' | 'MODERATOR';
  is_active: boolean;
  requires_password_change: boolean;
  last_login_ip?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminCreationResult {
  success: boolean;
  user?: AdminUser;
  temporaryPassword?: string;
  error?: string;
}

export interface AdminListResponse {
  users: AdminUser[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface AdminListFilters {
  role?: 'ADMIN' | 'MODERATOR';
  isActive?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

// Admin Activity Types
export enum AdminAction {
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  SEARCH_IP = 'SEARCH_IP',
  LOOKUP_IP = 'LOOKUP_IP',
  CREATE_USER = 'CREATE_USER',
  UPDATE_USER = 'UPDATE_USER',
  DELETE_USER = 'DELETE_USER',
  UPDATE_SETTINGS = 'UPDATE_SETTINGS',
  VIEW_API_LOGS = 'VIEW_API_LOGS',
  EXPORT_DATA = 'EXPORT_DATA',
}

export interface AdminActivity {
  id: string;
  userId: string;
  action: AdminAction;
  resource: string;
  resourceId?: string;
  details?: any;
  ipAddress: string;
  userAgent?: string;
  success: boolean;
  errorMessage?: string;
  createdAt: string;
  user?: {
    email: string;
    role: string;
  };
}