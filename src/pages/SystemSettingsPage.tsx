import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { api } from '../services/api';
import { formatDate } from '../utils/formatters';
import {
  CogIcon,
  ShieldCheckIcon,
  ServerIcon,
  BellIcon,
  WrenchScrewdriverIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  EyeIcon,
  EyeSlashIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface SystemSetting {
  id: string;
  key: string;
  value: any;
  category: string;
  description: string | null;
  data_type: string;
  is_public: boolean;
  requires_restart: boolean;
  validation_rules: any;
  created_by: string;
  updated_by: string | null;
  created_at: string;
  updated_at: string;
  created_by_user?: { email: string };
  updated_by_user?: { email: string };
}

interface SettingsChangeLog {
  id: string;
  setting_key: string;
  old_value: any;
  new_value: any;
  changed_by: string;
  changed_by_user: { email: string; role: string };
  change_reason: string | null;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}

interface SettingEditorProps {
  setting: SystemSetting;
  onSave: (key: string, value: any, reason?: string) => void;
  isLoading: boolean;
}

function SettingEditor({ setting, onSave, isLoading }: SettingEditorProps) {
  const [value, setValue] = useState(setting.value);
  const [reason, setReason] = useState('');
  const [showValue, setShowValue] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  const handleSave = () => {
    onSave(setting.key, value, reason);
    setIsEditing(false);
    setReason('');
  };

  const handleCancel = () => {
    setValue(setting.value);
    setReason('');
    setIsEditing(false);
  };

  const renderValueInput = () => {
    switch (setting.data_type) {
      case 'boolean':
        return (
          <select
            value={value.toString()}
            onChange={(e) => setValue(e.target.value === 'true')}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={!isEditing}
          >
            <option value="true">True</option>
            <option value="false">False</option>
          </select>
        );
      
      case 'number':
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => setValue(Number(e.target.value))}
            min={setting.validation_rules?.min}
            max={setting.validation_rules?.max}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            disabled={!isEditing}
          />
        );
      
      case 'array':
        return (
          <textarea
            value={Array.isArray(value) ? value.join('\n') : ''}
            onChange={(e) => setValue(e.target.value.split('\n').filter(v => v.trim()))}
            rows={4}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
            placeholder="One item per line"
            disabled={!isEditing}
          />
        );
      
      case 'json':
        return (
          <textarea
            value={typeof value === 'object' ? JSON.stringify(value, null, 2) : value}
            onChange={(e) => {
              try {
                setValue(JSON.parse(e.target.value));
              } catch {
                setValue(e.target.value);
              }
            }}
            rows={6}
            className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm font-mono"
            disabled={!isEditing}
          />
        );
      
      default:
        const isSecret = setting.key.includes('key') || setting.key.includes('secret') || setting.key.includes('password');
        return (
          <div className="relative">
            <input
              type={isSecret && !showValue ? 'password' : 'text'}
              value={value}
              onChange={(e) => setValue(e.target.value)}
              className="mt-1 block w-full pr-10 border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              disabled={!isEditing}
            />
            {isSecret && (
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowValue(!showValue)}
              >
                {showValue ? (
                  <EyeSlashIcon className="h-4 w-4 text-gray-400" />
                ) : (
                  <EyeIcon className="h-4 w-4 text-gray-400" />
                )}
              </button>
            )}
          </div>
        );
    }
  };

  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <h4 className="text-sm font-medium text-gray-900">{setting.key}</h4>
          {setting.requires_restart && (
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
              Requires Restart
            </span>
          )}
          {!setting.is_public && (
            <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
              Super Admin Only
            </span>
          )}
        </div>
        
        <div className="flex space-x-2">
          {!isEditing ? (
            <button
              onClick={() => setIsEditing(true)}
              className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Edit
            </button>
          ) : (
            <>
              <button
                onClick={handleCancel}
                className="inline-flex items-center px-3 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                disabled={isLoading}
                className="inline-flex items-center px-3 py-1 border border-transparent shadow-sm text-xs font-medium rounded text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
              >
                {isLoading ? 'Saving...' : 'Save'}
              </button>
            </>
          )}
        </div>
      </div>
      
      {setting.description && (
        <p className="text-sm text-gray-500 mb-3">{setting.description}</p>
      )}
      
      <div className="space-y-3">
        <div>
          <label className="block text-sm font-medium text-gray-700">Value</label>
          {renderValueInput()}
        </div>
        
        {isEditing && (
          <div>
            <label className="block text-sm font-medium text-gray-700">Change Reason (Optional)</label>
            <input
              type="text"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              placeholder="Why are you making this change?"
            />
          </div>
        )}
        
        {setting.validation_rules && (
          <div className="text-xs text-gray-500">
            <strong>Validation:</strong> {JSON.stringify(setting.validation_rules)}
          </div>
        )}
        
        <div className="text-xs text-gray-500">
          <div>Type: {setting.data_type}</div>
          <div>Last updated: {formatDate(setting.updated_at)}</div>
          {setting.updated_by_user && (
            <div>Updated by: {setting.updated_by_user.email}</div>
          )}
        </div>
      </div>
    </div>
  );
}

interface SettingsCategoryProps {
  title: string;
  icon: React.ComponentType<any>;
  settings: SystemSetting[];
  onUpdateSetting: (key: string, value: any, reason?: string) => void;
  isLoading: boolean;
}

function SettingsCategory({ title, icon: Icon, settings, onUpdateSetting, isLoading }: SettingsCategoryProps) {
  const [isExpanded, setIsExpanded] = useState(true);

  if (settings.length === 0) return null;

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center justify-between w-full text-left"
        >
          <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
            <Icon className="h-5 w-5 mr-2" />
            {title}
          </h3>
          <span className="text-sm text-gray-500">
            {isExpanded ? 'Collapse' : 'Expand'} ({settings.length})
          </span>
        </button>
        
        {isExpanded && (
          <div className="mt-6 space-y-4">
            {settings.map((setting) => (
              <SettingEditor
                key={setting.id}
                setting={setting}
                onSave={onUpdateSetting}
                isLoading={isLoading}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

interface ChangeLogProps {
  logs: SettingsChangeLog[];
  onRefresh: () => void;
  isLoading: boolean;
}

function ChangeLog({ logs, onRefresh, isLoading }: ChangeLogProps) {
  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900 flex items-center">
            <ClockIcon className="h-5 w-5 mr-2" />
            Recent Changes
          </h3>
          <button
            onClick={onRefresh}
            disabled={isLoading}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            Refresh
          </button>
        </div>

        {logs.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No changes found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Settings changes will appear here.
            </p>
          </div>
        ) : (
          <div className="flow-root">
            <ul className="-my-5 divide-y divide-gray-200">
              {logs.map((log) => (
                <li key={log.id} className="py-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex-shrink-0">
                      <div className="flex items-center justify-center h-8 w-8 rounded-full bg-indigo-100">
                        <CogIcon className="h-4 w-4 text-indigo-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {log.setting_key}
                      </p>
                      <div className="flex items-center space-x-2 text-sm text-gray-500">
                        <span>Changed by {log.changed_by_user.email}</span>
                        <span>•</span>
                        <span>{formatDate(log.created_at)}</span>
                        {log.ip_address && (
                          <>
                            <span>•</span>
                            <span>{log.ip_address}</span>
                          </>
                        )}
                      </div>
                      {log.change_reason && (
                        <p className="text-sm text-gray-600 mt-1">
                          Reason: {log.change_reason}
                        </p>
                      )}
                      <div className="mt-2 text-xs text-gray-500">
                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <strong>From:</strong> {JSON.stringify(log.old_value)}
                          </div>
                          <div>
                            <strong>To:</strong> {JSON.stringify(log.new_value)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

export default function SystemSettingsPage() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<SystemSetting[]>([]);
  const [changeLogs, setChangeLogs] = useState<SettingsChangeLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  const isSuperAdmin = user?.role === 'super_admin';

  useEffect(() => {
    if (!isSuperAdmin) {
      setError('Access denied. Super Admin privileges required.');
      setIsLoading(false);
      return;
    }
    
    loadSettings();
    loadChangeLogs();
  }, [isSuperAdmin]);

  const loadSettings = async () => {
    try {
      setError(null);
      const response = await api.getSystemSettings();
      
      if (response.success && response.data) {
        setSettings(response.data);
      } else {
        setError(response.error || 'Failed to load system settings');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const loadChangeLogs = async () => {
    try {
      const response = await api.getSettingsChangeLog();
      
      if (response.success && response.data) {
        setChangeLogs(response.data);
      }
    } catch (error: any) {
      console.error('Failed to load change logs:', error);
    }
  };

  const handleUpdateSetting = async (key: string, value: any, reason?: string) => {
    setIsUpdating(true);
    setError(null);

    try {
      const response = await api.updateSystemSetting(key, value, reason);
      
      if (response.success) {
        setSuccessMessage(`Setting "${key}" updated successfully`);
        setTimeout(() => setSuccessMessage(null), 5000);
        
        // Update the setting in the local state
        setSettings(prev => prev.map(setting => 
          setting.key === key 
            ? { ...setting, value, updated_at: new Date().toISOString() }
            : setting
        ));
        
        // Reload change logs
        loadChangeLogs();
      } else {
        setError(response.error || 'Failed to update setting');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsUpdating(false);
    }
  };

  if (!isSuperAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            Super Admin privileges are required to access system settings.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  const categorizedSettings = {
    security: settings.filter(s => s.category === 'security'),
    api: settings.filter(s => s.category === 'api'),
    notifications: settings.filter(s => s.category === 'notifications'),
    maintenance: settings.filter(s => s.category === 'maintenance'),
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
        <p className="mt-1 text-sm text-gray-600">
          Configure system-wide settings and parameters (Super Admin only)
        </p>
      </div>

      {/* Warning Notice */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">Important Notice</h3>
            <div className="mt-2 text-sm text-yellow-700">
              <p>
                Changes to system settings can affect the entire platform. Some settings require a system restart to take effect.
                All changes are logged and can be audited.
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircleIcon className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm font-medium text-green-800">{successMessage}</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      {/* Settings Categories */}
      <div className="space-y-6">
        <SettingsCategory
          title="Security Settings"
          icon={ShieldCheckIcon}
          settings={categorizedSettings.security}
          onUpdateSetting={handleUpdateSetting}
          isLoading={isUpdating}
        />

        <SettingsCategory
          title="API Configuration"
          icon={ServerIcon}
          settings={categorizedSettings.api}
          onUpdateSetting={handleUpdateSetting}
          isLoading={isUpdating}
        />

        <SettingsCategory
          title="Notification Settings"
          icon={BellIcon}
          settings={categorizedSettings.notifications}
          onUpdateSetting={handleUpdateSetting}
          isLoading={isUpdating}
        />

        <SettingsCategory
          title="Maintenance Settings"
          icon={WrenchScrewdriverIcon}
          settings={categorizedSettings.maintenance}
          onUpdateSetting={handleUpdateSetting}
          isLoading={isUpdating}
        />
      </div>

      {/* Change Log */}
      <ChangeLog
        logs={changeLogs}
        onRefresh={loadChangeLogs}
        isLoading={isLoading}
      />
    </div>
  );
}