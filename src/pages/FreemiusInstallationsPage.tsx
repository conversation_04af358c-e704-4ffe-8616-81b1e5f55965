import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { FreemiusInstallation, FreemiusProduct } from '../types';
import { formatDate, formatTimeAgo } from '../utils/formatters';
import {
    ArrowPathIcon,
    EyeIcon,
    FunnelIcon,
    MagnifyingGlassIcon,
    ServerIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    XCircleIcon,
    ClockIcon,
    GlobeAltIcon,
} from '@heroicons/react/24/outline';

interface InstallationFilters {
    product_id?: string;
    status?: 'active' | 'inactive' | 'premium' | 'trial';
    search?: string;
    page?: number;
    limit?: number;
    is_active?: boolean;
}

interface PaginationInfo {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
}

export default function FreemiusInstallationsPage() {
    const [installations, setInstallations] = useState<FreemiusInstallation[]>([]);
    const [products, setProducts] = useState<FreemiusProduct[]>([]);
    const [pagination, setPagination] = useState<PaginationInfo | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [isSyncing, setIsSyncing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedInstallation, setSelectedInstallation] = useState<FreemiusInstallation | null>(null);
    const [filters, setFilters] = useState<InstallationFilters>({ page: 1, limit: 20 });
    const [showFilters, setShowFilters] = useState(false);
    const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

    useEffect(() => {
        loadData();
    }, []);

    useEffect(() => {
        loadInstallations();
    }, [filters]);

    const loadData = async () => {
        try {
            setIsLoading(true);
            setError(null);

            // Load products and installations in parallel
            const [productsResponse, installationsResponse, statsResponse] = await Promise.allSettled([
                api.getProducts(),
                api.getInstallations(filters),
                api.getFreemiusStats()
            ]);

            // Handle products response
            if (productsResponse.status === 'fulfilled' && productsResponse.value.success && productsResponse.value.data) {
                setProducts(productsResponse.value.data);
            } else if (productsResponse.status === 'rejected' || !productsResponse.value.success) {
                console.error('Failed to load products:',
                    productsResponse.status === 'rejected' ? productsResponse.reason : productsResponse.value.error);
            }

            // Handle installations response
            if (installationsResponse.status === 'fulfilled' && installationsResponse.value.success && installationsResponse.value.data) {
                setInstallations(installationsResponse.value.data.data || []);
                setPagination(installationsResponse.value.data.pagination || null);
            } else if (installationsResponse.status === 'rejected' || !installationsResponse.value.success) {
                const errorMsg = installationsResponse.status === 'rejected'
                    ? installationsResponse.reason?.message || 'Network error'
                    : installationsResponse.value.error || 'Failed to load installations';
                setError(errorMsg);
            }

            // Handle stats response for last sync time
            if (statsResponse.status === 'fulfilled' && statsResponse.value.success && statsResponse.value.data?.lastSyncAt) {
                setLastSyncTime(new Date(statsResponse.value.data.lastSyncAt));
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
            console.error('Failed to load Freemius data:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const loadInstallations = async () => {
        try {
            setError(null);

            // Convert status filter to is_active boolean if needed
            const apiFilters = { ...filters };
            if (filters.status === 'active') {
                apiFilters.is_active = true;
                delete apiFilters.status;
            } else if (filters.status === 'inactive') {
                apiFilters.is_active = false;
                delete apiFilters.status;
            }

            const response = await api.getInstallations(apiFilters);

            if (response.success && response.data) {
                setInstallations(response.data.data || []);
                setPagination(response.data.pagination || null);
            } else {
                setError(response.error || 'Failed to load installations');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
            console.error('Failed to load installations:', error);
        }
    };

    const handleRefreshInstallations = async () => {
        try {
            setIsSyncing(true);
            setError(null);
            const response = await api.syncFreemiusData();

            if (response.success) {
                setLastSyncTime(new Date());
                // Reload installations after sync
                await loadInstallations();
            } else {
                setError(response.error || 'Failed to refresh installation data');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred during refresh');
        } finally {
            setIsSyncing(false);
        }
    };

    const handlePageChange = (newPage: number) => {
        setFilters({ ...filters, page: newPage });
    };

    const handleFilterChange = (newFilters: Partial<InstallationFilters>) => {
        setFilters({ ...filters, ...newFilters, page: 1 }); // Reset to first page when filters change
    };

    const getStatusBadge = (installation: FreemiusInstallation) => {
        if (installation.is_uninstalled) {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <XCircleIcon className="w-3 h-3 mr-1" />
                    Uninstalled
                </span>
            );
        }

        if (!installation.is_active) {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    <XCircleIcon className="w-3 h-3 mr-1" />
                    Inactive
                </span>
            );
        }

        if (installation.is_premium) {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Premium
                </span>
            );
        }

        if (installation.trial_plan_id && installation.trial_ends) {
            const trialEnds = new Date(installation.trial_ends);
            const isExpired = trialEnds < new Date();

            return (
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${isExpired ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                    <ClockIcon className="w-3 h-3 mr-1" />
                    {isExpired ? 'Trial Expired' : 'Trial'}
                </span>
            );
        }

        return (
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <CheckCircleIcon className="w-3 h-3 mr-1" />
                Active
            </span>
        );
    };

    const getProductName = (pluginId: string) => {
        const product = products.find(p => p.id === pluginId);
        return product?.title || `Product ${pluginId}`;
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Freemius Installations</h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Monitor and manage Freemius plugin installations across all sites
                    </p>
                    {lastSyncTime && (
                        <p className="mt-1 text-xs text-gray-500">
                            Last synced: {lastSyncTime.toLocaleString()}
                        </p>
                    )}
                    {pagination && (
                        <p className="mt-1 text-xs text-gray-500">
                            Showing {pagination.items_per_page * (pagination.current_page - 1) + 1} to{' '}
                            {Math.min(pagination.items_per_page * pagination.current_page, pagination.total_items)} of{' '}
                            {pagination.total_items} installations
                        </p>
                    )}
                </div>
                <div className="flex space-x-3">
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <FunnelIcon className="-ml-1 mr-2 h-4 w-4" />
                        Filters
                    </button>
                    <button
                        onClick={handleRefreshInstallations}
                        disabled={isSyncing}
                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        <ArrowPathIcon className={`-ml-1 mr-2 h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
                        {isSyncing ? 'Syncing...' : 'Sync Data'}
                    </button>
                </div>
            </div>

            {/* Error Alert */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error</h3>
                            <div className="mt-2 text-sm text-red-700">{error}</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Filters Panel */}
            {showFilters && (
                <div className="bg-white shadow rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-4">Filter Installations</h3>
                    <div className="grid grid-cols-1 gap-4 sm:grid-cols-3">
                        <div>
                            <label htmlFor="product-filter" className="block text-sm font-medium text-gray-700">
                                Product
                            </label>
                            <select
                                id="product-filter"
                                value={filters.product_id || ''}
                                onChange={(e) => handleFilterChange({ product_id: e.target.value || undefined })}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                                <option value="">All Products</option>
                                {products.map((product) => (
                                    <option key={product.id} value={product.id}>
                                        {product.title}
                                    </option>
                                ))}
                            </select>
                        </div>

                        <div>
                            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700">
                                Status
                            </label>
                            <select
                                id="status-filter"
                                value={filters.status || ''}
                                onChange={(e) => handleFilterChange({ status: e.target.value as any || undefined })}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                                <option value="">All Statuses</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                                <option value="premium">Premium</option>
                                <option value="trial">Trial</option>
                            </select>
                        </div>

                        <div>
                            <label htmlFor="search-filter" className="block text-sm font-medium text-gray-700">
                                Search
                            </label>
                            <div className="mt-1 relative rounded-md shadow-sm">
                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                                </div>
                                <input
                                    type="text"
                                    id="search-filter"
                                    value={filters.search || ''}
                                    onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                                    className="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 sm:text-sm border-gray-300 rounded-md"
                                    placeholder="Search by title, URL, or ID..."
                                />
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Installations List */}
            {installations.length > 0 ? (
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <ul className="divide-y divide-gray-200">
                        {installations.map((installation) => (
                            <li key={installation.id}>
                                <div className="px-4 py-4 flex items-center justify-between">
                                    <div className="flex items-center min-w-0 flex-1">
                                        <div className="flex-shrink-0">
                                            <ServerIcon className="h-10 w-10 text-gray-400" />
                                        </div>
                                        <div className="ml-4 min-w-0 flex-1">
                                            <div className="flex items-center justify-between">
                                                <div className="min-w-0 flex-1">
                                                    <p className="text-sm font-medium text-gray-900 truncate">
                                                        {installation.title || 'Untitled Site'}
                                                    </p>
                                                    <p className="text-sm text-gray-500 truncate">
                                                        {installation.url || 'No URL'}
                                                    </p>
                                                </div>
                                                <div className="ml-2 flex-shrink-0">
                                                    {getStatusBadge(installation)}
                                                </div>
                                            </div>
                                            <div className="mt-2 flex items-center text-sm text-gray-500">
                                                <div className="flex items-center">
                                                    <span className="truncate">
                                                        {getProductName(installation.plugin_id)}
                                                    </span>
                                                    <span className="mx-2">•</span>
                                                    <span>v{installation.version}</span>
                                                    {installation.country_code && (
                                                        <>
                                                            <span className="mx-2">•</span>
                                                            <GlobeAltIcon className="h-4 w-4 mr-1" />
                                                            <span>{installation.country_code}</span>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="mt-1 text-xs text-gray-400">
                                                Last seen: {installation.last_seen_at ? formatTimeAgo(installation.last_seen_at) : 'Never'}
                                            </div>
                                        </div>
                                    </div>
                                    <div className="ml-5 flex-shrink-0">
                                        <button
                                            onClick={() => setSelectedInstallation(installation)}
                                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                        >
                                            <EyeIcon className="h-4 w-4 mr-2" />
                                            Details
                                        </button>
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                </div>
            ) : (
                <div className="text-center py-12">
                    <ServerIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No installations found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        {Object.keys(filters).length > 0
                            ? 'No installations match your current filters.'
                            : 'No Freemius installations have been synchronized yet.'
                        }
                    </p>
                    {Object.keys(filters).length === 0 && (
                        <div className="mt-6">
                            <button
                                onClick={handleRefreshInstallations}
                                disabled={isSyncing}
                                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                            >
                                <ArrowPathIcon className={`-ml-1 mr-2 h-4 w-4 ${isSyncing ? 'animate-spin' : ''}`} />
                                Sync Installations Now
                            </button>
                        </div>
                    )}
                </div>
            )}

            {/* Pagination */}
            {pagination && pagination.total_pages > 1 && (
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                    <div className="flex-1 flex justify-between sm:hidden">
                        <button
                            onClick={() => handlePageChange(pagination.current_page - 1)}
                            disabled={pagination.current_page <= 1}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Previous
                        </button>
                        <button
                            onClick={() => handlePageChange(pagination.current_page + 1)}
                            disabled={pagination.current_page >= pagination.total_pages}
                            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Next
                        </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p className="text-sm text-gray-700">
                                Showing <span className="font-medium">{(pagination.current_page - 1) * pagination.items_per_page + 1}</span> to{' '}
                                <span className="font-medium">
                                    {Math.min(pagination.current_page * pagination.items_per_page, pagination.total_items)}
                                </span>{' '}
                                of <span className="font-medium">{pagination.total_items}</span> results
                            </p>
                        </div>
                        <div>
                            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                <button
                                    onClick={() => handlePageChange(pagination.current_page - 1)}
                                    disabled={pagination.current_page <= 1}
                                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span className="sr-only">Previous</span>
                                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                                    </svg>
                                </button>

                                {/* Page numbers */}
                                {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                                    const pageNum = Math.max(1, Math.min(
                                        pagination.current_page - 2 + i,
                                        pagination.total_pages - 4 + i
                                    ));
                                    if (pageNum > pagination.total_pages) return null;

                                    return (
                                        <button
                                            key={pageNum}
                                            onClick={() => handlePageChange(pageNum)}
                                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${pageNum === pagination.current_page
                                                    ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                }`}
                                        >
                                            {pageNum}
                                        </button>
                                    );
                                })}

                                <button
                                    onClick={() => handlePageChange(pagination.current_page + 1)}
                                    disabled={pagination.current_page >= pagination.total_pages}
                                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <span className="sr-only">Next</span>
                                    <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                    </svg>
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            )}

            {/* Installation Details Modal */}
            {selectedInstallation && (
                <InstallationDetailsModal
                    installation={selectedInstallation}
                    productName={getProductName(selectedInstallation.plugin_id)}
                    onClose={() => setSelectedInstallation(null)}
                />
            )}
        </div>
    );
}

// Installation Details Modal Component
interface InstallationDetailsModalProps {
    installation: FreemiusInstallation;
    productName: string;
    onClose: () => void;
}

function InstallationDetailsModal({ installation, productName, onClose }: InstallationDetailsModalProps) {
    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                    <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div className="flex justify-between items-start mb-6">
                            <div className="flex items-center">
                                <ServerIcon className="h-12 w-12 text-gray-400" />
                                <div className="ml-4">
                                    <h3 className="text-lg font-medium text-gray-900">
                                        {installation.title || 'Untitled Site'}
                                    </h3>
                                    <p className="text-sm text-gray-500">Installation ID: {installation.id}</p>
                                </div>
                            </div>
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <span className="sr-only">Close</span>
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        {/* Installation Overview */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                            <div>
                                <h4 className="text-lg font-medium text-gray-900 mb-4">Site Information</h4>
                                <dl className="space-y-3">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Site URL</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.url ? (
                                                <a
                                                    href={installation.url}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="text-indigo-600 hover:text-indigo-500"
                                                >
                                                    {installation.url}
                                                </a>
                                            ) : (
                                                'Not available'
                                            )}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Product</dt>
                                        <dd className="text-sm text-gray-900">{productName}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Version</dt>
                                        <dd className="text-sm text-gray-900">{installation.version}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Country</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.country_code || 'Unknown'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Language</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.language || 'Unknown'}
                                        </dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h4 className="text-lg font-medium text-gray-900 mb-4">License & Status</h4>
                                <dl className="space-y-3">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Status</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.is_active ? 'Active' : 'Inactive'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Premium</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.is_premium ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">License ID</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.license_id || 'No license'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Plan ID</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.plan_id || 'No plan'}
                                        </dd>
                                    </div>
                                    {installation.trial_ends && (
                                        <div>
                                            <dt className="text-sm font-medium text-gray-500">Trial Ends</dt>
                                            <dd className="text-sm text-gray-900">
                                                {formatDate(installation.trial_ends)}
                                            </dd>
                                        </div>
                                    )}
                                </dl>
                            </div>
                        </div>

                        {/* Technical Details */}
                        <div className="border-t border-gray-200 pt-6">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Technical Details</h4>
                            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <dl className="space-y-3">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Platform Version</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.platform_version || 'Unknown'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">SDK Version</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.sdk_version || 'Unknown'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">PHP Version</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.programming_language_version || 'Unknown'}
                                        </dd>
                                    </div>
                                </dl>
                                <dl className="space-y-3">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Beta Version</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.is_beta ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Disconnected</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.is_disconnected ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Locked</dt>
                                        <dd className="text-sm text-gray-900">
                                            {installation.is_locked ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        {/* Timestamps */}
                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Activity</h4>
                            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                                    <dd className="text-sm text-gray-900">{formatDate(installation.created)}</dd>
                                </div>
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd className="text-sm text-gray-900">
                                        {installation.updated ? formatDate(installation.updated) : 'Never'}
                                    </dd>
                                </div>
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Last Seen</dt>
                                    <dd className="text-sm text-gray-900">
                                        {installation.last_seen_at ? formatDate(installation.last_seen_at) : 'Never'}
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            type="button"
                            onClick={onClose}
                            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}