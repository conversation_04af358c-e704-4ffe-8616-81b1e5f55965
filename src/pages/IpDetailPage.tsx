import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { api } from '../services/api';
import { formatDate, formatIpAddress } from '../utils/formatters';
import {
    ArrowPathIcon,
    GlobeAltIcon,
    ShieldExclamationIcon,
    ClockIcon,
    BuildingOfficeIcon,
    MapPinIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    ChartBarIcon,
    ServerIcon,
    ArrowLeftIcon,
    CurrencyDollarIcon,
    LanguageIcon,
    CloudIcon,
    WifiIcon,
} from '@heroicons/react/24/outline';

interface IpDetailData {
    ip: string;
    location: {
        continent: string;
        country: string;
        countryCode: string;
        region: string;
        city: string;
        postal: string;
        coordinates: [number, number];
        timezone: string;
        inEu: boolean;
        languages: Array<{
            code: string;
            name: string;
            native: string;
        }>;
    };
    network: {
        isp: string;
        organization: string;
        asn: string;
        domain: string;
        route: string;
        type: string;
    };
    company: {
        name: string;
        domain: string;
        type: string;
    };
    currency: {
        code: string;
        name: string;
        symbol: string;
        nativeSymbol: string;
    };
    security: {
        riskScore: number;
        recommendation: 'allow' | 'block' | 'review';
        threats: string[];
        flags: {
            isAbuser: boolean;
            isAttacker: boolean;
            isBogon: boolean;
            isCloudProvider: boolean;
            isProxy: boolean;
            isRelay: boolean;
            isTor: boolean;
            isTorExit: boolean;
            isVpn: boolean;
            isAnonymous: boolean;
            isThreat: boolean;
            isMalware: boolean;
        };
    };
    carrier: {
        name: string | null;
        mcc: string | null;
        mnc: string | null;
    };
    metadata: {
        type: 'IPv4' | 'IPv6';
        hostname: string | null;
        usedStoredData: boolean;
        lastUpdated: string;
        processingTime: number;
        dataExpiresAt: string;
        refreshCount: number;
    };
}

interface SecurityBadgeProps {
    label: string;
    value: boolean;
    type: 'threat' | 'warning' | 'info';
}

function SecurityBadge({ label, value, type }: SecurityBadgeProps) {
    if (!value) return null;

    const colors = {
        threat: 'bg-red-100 text-red-800 border-red-200',
        warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
        info: 'bg-blue-100 text-blue-800 border-blue-200',
    };

    const icons = {
        threat: ShieldExclamationIcon,
        warning: ExclamationTriangleIcon,
        info: InformationCircleIcon,
    };

    const Icon = icons[type];

    return (
        <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${colors[type]}`}>
            <Icon className="h-4 w-4 mr-1" />
            {label}
        </span>
    );
}

interface RiskScoreDisplayProps {
    score: number;
    recommendation: string;
}

function RiskScoreDisplay({ score, recommendation }: RiskScoreDisplayProps) {
    const getScoreColor = (score: number) => {
        if (score >= 80) return 'text-red-600 bg-red-100 border-red-200';
        if (score >= 60) return 'text-orange-600 bg-orange-100 border-orange-200';
        if (score >= 40) return 'text-yellow-600 bg-yellow-100 border-yellow-200';
        return 'text-green-600 bg-green-100 border-green-200';
    };

    const getRecommendationColor = (rec: string) => {
        switch (rec) {
            case 'block': return 'text-red-600 bg-red-100 border-red-200';
            case 'review': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
            case 'allow': return 'text-green-600 bg-green-100 border-green-200';
            default: return 'text-gray-600 bg-gray-100 border-gray-200';
        }
    };

    return (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="text-center">
                <div className="text-sm font-medium text-gray-500 mb-2">Risk Score</div>
                <div className={`inline-flex items-center px-6 py-3 rounded-lg text-2xl font-bold border ${getScoreColor(score)}`}>
                    {score}/100
                </div>
            </div>
            <div className="text-center">
                <div className="text-sm font-medium text-gray-500 mb-2">Recommendation</div>
                <div className={`inline-flex items-center px-6 py-3 rounded-lg text-lg font-semibold capitalize border ${getRecommendationColor(recommendation)}`}>
                    {recommendation}
                </div>
            </div>
        </div>
    );
}

interface MapDisplayProps {
    coordinates: [number, number];
    city: string;
    country: string;
}

function MapDisplay({ coordinates, city, country }: MapDisplayProps) {
    const [lat, lng] = coordinates;

    // Simple map placeholder - in a real implementation, you'd integrate with Google Maps, Mapbox, etc.
    const mapUrl = `https://www.openstreetmap.org/export/embed.html?bbox=${lng - 0.1},${lat - 0.1},${lng + 0.1},${lat + 0.1}&layer=mapnik&marker=${lat},${lng}`;

    return (
        <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
                <h5 className="text-sm font-medium text-gray-900">Location Map</h5>
                <span className="text-xs text-gray-500">{city}, {country}</span>
            </div>
            <div className="aspect-w-16 aspect-h-9 bg-gray-200 rounded-lg overflow-hidden">
                <iframe
                    src={mapUrl}
                    className="w-full h-48 border-0"
                    title={`Map showing ${city}, ${country}`}
                />
            </div>
            <div className="mt-2 text-xs text-gray-500 text-center">
                Coordinates: {lat.toFixed(6)}, {lng.toFixed(6)}
            </div>
        </div>
    );
}

export default function IpDetailPage() {
    const { ip } = useParams<{ ip: string }>();
    const navigate = useNavigate();
    const [ipData, setIpData] = useState<IpDetailData | null>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (ip) {
            loadIpData();
        }
    }, [ip]);

    const loadIpData = async () => {
        if (!ip) return;

        setIsLoading(true);
        setError(null);

        try {
            const response = await api.lookupIp(ip);

            if (response.success && response.data) {
                setIpData(response.data);
            } else {
                setError(response.error || 'Failed to load IP data');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefresh = async () => {
        if (!ip) return;

        setIsRefreshing(true);
        setError(null);

        try {
            const response = await api.refreshIpIntelligence(ip);

            if (response.success && response.data) {
                setIpData(response.data);
            } else {
                setError(response.error || 'Failed to refresh IP data');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsRefreshing(false);
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="space-y-6">
                <div className="flex items-center">
                    <button
                        onClick={() => navigate(-1)}
                        className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                    >
                        <ArrowLeftIcon className="h-5 w-5" />
                    </button>
                    <h1 className="text-2xl font-bold text-gray-900">IP Details</h1>
                </div>

                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error Loading IP Data</h3>
                            <div className="mt-2 text-sm text-red-700">{error}</div>
                        </div>
                    </div>
                </div>
            </div>
        );
    }

    if (!ipData) {
        return (
            <div className="space-y-6">
                <div className="flex items-center">
                    <button
                        onClick={() => navigate(-1)}
                        className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                    >
                        <ArrowLeftIcon className="h-5 w-5" />
                    </button>
                    <h1 className="text-2xl font-bold text-gray-900">IP Details</h1>
                </div>

                <div className="text-center py-12">
                    <p className="text-gray-500">No IP data available</p>
                </div>
            </div>
        );
    }

    const isDataFresh = new Date(ipData.metadata.dataExpiresAt) > new Date();

    const securityFlags = [
        { key: 'isThreat', label: 'Threat', type: 'threat' as const },
        { key: 'isMalware', label: 'Malware', type: 'threat' as const },
        { key: 'isAbuser', label: 'Abuser', type: 'threat' as const },
        { key: 'isAttacker', label: 'Attacker', type: 'threat' as const },
        { key: 'isTor', label: 'Tor', type: 'warning' as const },
        { key: 'isTorExit', label: 'Tor Exit', type: 'warning' as const },
        { key: 'isProxy', label: 'Proxy', type: 'warning' as const },
        { key: 'isVpn', label: 'VPN', type: 'warning' as const },
        { key: 'isAnonymous', label: 'Anonymous', type: 'warning' as const },
        { key: 'isCloudProvider', label: 'Cloud Provider', type: 'info' as const },
        { key: 'isRelay', label: 'Relay', type: 'info' as const },
        { key: 'isBogon', label: 'Bogon', type: 'info' as const },
    ];

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center">
                    <button
                        onClick={() => navigate(-1)}
                        className="mr-4 p-2 text-gray-400 hover:text-gray-600"
                    >
                        <ArrowLeftIcon className="h-5 w-5" />
                    </button>
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">IP Intelligence Details</h1>
                        <p className="text-lg text-gray-600 font-mono">{formatIpAddress(ipData.ip)}</p>
                    </div>
                </div>

                <div className="flex items-center space-x-4">
                    <div className="text-sm text-gray-500">
                        <ClockIcon className="h-4 w-4 inline mr-1" />
                        Last updated: {formatDate(ipData.metadata.lastUpdated)}
                    </div>
                    <div className={`text-xs px-3 py-1 rounded-full ${isDataFresh ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                        {isDataFresh ? 'Fresh Data' : 'Stale Data'}
                    </div>
                    <button
                        onClick={handleRefresh}
                        disabled={isRefreshing}
                        className="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                    >
                        <ArrowPathIcon className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                        {isRefreshing ? 'Refreshing...' : 'Refresh'}
                    </button>
                </div>
            </div>

            {/* Risk Assessment */}
            <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                    <ChartBarIcon className="h-6 w-6 mr-2 text-gray-400" />
                    Risk Assessment
                </h2>
                <RiskScoreDisplay
                    score={ipData.security.riskScore}
                    recommendation={ipData.security.recommendation}
                />
                {ipData.security.threats.length > 0 && (
                    <div className="mt-6">
                        <h3 className="text-sm font-medium text-gray-500 mb-2">Detected Threats:</h3>
                        <div className="flex flex-wrap gap-2">
                            {ipData.security.threats.map((threat, index) => (
                                <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800 border border-red-200">
                                    <ShieldExclamationIcon className="h-4 w-4 mr-1" />
                                    {threat}
                                </span>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Location Information */}
                <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                        <MapPinIcon className="h-6 w-6 mr-2 text-gray-400" />
                        Location Information
                    </h2>

                    <div className="space-y-4">
                        <MapDisplay
                            coordinates={ipData.location.coordinates}
                            city={ipData.location.city}
                            country={ipData.location.country}
                        />

                        <div className="grid grid-cols-2 gap-4">
                            <div>
                                <span className="text-sm font-medium text-gray-500">Continent:</span>
                                <p className="text-sm text-gray-900">{ipData.location.continent}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">Country:</span>
                                <p className="text-sm text-gray-900">{ipData.location.country} ({ipData.location.countryCode})</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">Region:</span>
                                <p className="text-sm text-gray-900">{ipData.location.region}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">City:</span>
                                <p className="text-sm text-gray-900">{ipData.location.city}</p>
                            </div>
                            {ipData.location.postal && (
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Postal Code:</span>
                                    <p className="text-sm text-gray-900">{ipData.location.postal}</p>
                                </div>
                            )}
                            <div>
                                <span className="text-sm font-medium text-gray-500">Timezone:</span>
                                <p className="text-sm text-gray-900">{ipData.location.timezone}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">EU Member:</span>
                                <p className="text-sm text-gray-900">{ipData.location.inEu ? 'Yes' : 'No'}</p>
                            </div>
                        </div>

                        {ipData.location.languages.length > 0 && (
                            <div>
                                <span className="text-sm font-medium text-gray-500 block mb-2">Languages:</span>
                                <div className="flex flex-wrap gap-2">
                                    {ipData.location.languages.map((lang, index) => (
                                        <span key={index} className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                                            <LanguageIcon className="h-3 w-3 mr-1" />
                                            {lang.name} ({lang.code})
                                        </span>
                                    ))}
                                </div>
                            </div>
                        )}
                    </div>
                </div>

                {/* Network Information */}
                <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                        <ServerIcon className="h-6 w-6 mr-2 text-gray-400" />
                        Network Information
                    </h2>

                    <div className="space-y-4">
                        <div className="grid grid-cols-1 gap-4">
                            <div>
                                <span className="text-sm font-medium text-gray-500">ISP:</span>
                                <p className="text-sm text-gray-900">{ipData.network.isp}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">Organization:</span>
                                <p className="text-sm text-gray-900">{ipData.network.organization}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">ASN:</span>
                                <p className="text-sm text-gray-900 font-mono">{ipData.network.asn}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">Domain:</span>
                                <p className="text-sm text-gray-900">{ipData.network.domain}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">Route:</span>
                                <p className="text-sm text-gray-900 font-mono">{ipData.network.route}</p>
                            </div>
                            <div>
                                <span className="text-sm font-medium text-gray-500">Connection Type:</span>
                                <p className="text-sm text-gray-900 capitalize">{ipData.network.type}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Company Information */}
                <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                        <BuildingOfficeIcon className="h-6 w-6 mr-2 text-gray-400" />
                        Company Information
                    </h2>

                    <div className="space-y-4">
                        <div>
                            <span className="text-sm font-medium text-gray-500">Company Name:</span>
                            <p className="text-sm text-gray-900">{ipData.company.name}</p>
                        </div>
                        <div>
                            <span className="text-sm font-medium text-gray-500">Domain:</span>
                            <p className="text-sm text-gray-900">{ipData.company.domain}</p>
                        </div>
                        <div>
                            <span className="text-sm font-medium text-gray-500">Type:</span>
                            <p className="text-sm text-gray-900 capitalize">{ipData.company.type}</p>
                        </div>
                    </div>
                </div>

                {/* Currency & Regional Info */}
                <div className="bg-white shadow rounded-lg p-6">
                    <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                        <CurrencyDollarIcon className="h-6 w-6 mr-2 text-gray-400" />
                        Currency & Regional
                    </h2>

                    <div className="space-y-4">
                        <div>
                            <span className="text-sm font-medium text-gray-500">Currency:</span>
                            <p className="text-sm text-gray-900">{ipData.currency.name} ({ipData.currency.code})</p>
                        </div>
                        <div>
                            <span className="text-sm font-medium text-gray-500">Symbol:</span>
                            <p className="text-sm text-gray-900">{ipData.currency.symbol} / {ipData.currency.nativeSymbol}</p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Security Flags */}
            <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                    <ShieldExclamationIcon className="h-6 w-6 mr-2 text-gray-400" />
                    Security Flags
                </h2>
                <div className="flex flex-wrap gap-3">
                    {securityFlags.map(({ key, label, type }) => (
                        <SecurityBadge
                            key={key}
                            label={label}
                            value={ipData.security.flags[key as keyof typeof ipData.security.flags]}
                            type={type}
                        />
                    ))}
                    {securityFlags.every(({ key }) => !ipData.security.flags[key as keyof typeof ipData.security.flags]) && (
                        <span className="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 border border-green-200">
                            <CheckCircleIcon className="h-5 w-5 mr-2" />
                            Clean IP - No security flags detected
                        </span>
                    )}
                </div>
            </div>

            {/* Technical Metadata */}
            <div className="bg-white shadow rounded-lg p-6">
                <h2 className="text-lg font-medium text-gray-900 flex items-center mb-4">
                    <InformationCircleIcon className="h-6 w-6 mr-2 text-gray-400" />
                    Technical Metadata
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <span className="text-sm font-medium text-gray-500">IP Type:</span>
                        <p className="text-sm text-gray-900">{ipData.metadata.type}</p>
                    </div>
                    {ipData.metadata.hostname && (
                        <div>
                            <span className="text-sm font-medium text-gray-500">Hostname:</span>
                            <p className="text-sm text-gray-900 font-mono">{ipData.metadata.hostname}</p>
                        </div>
                    )}
                    <div>
                        <span className="text-sm font-medium text-gray-500">Data Source:</span>
                        <p className="text-sm text-gray-900">
                            {ipData.metadata.usedStoredData ? 'Cached' : 'Fresh API'}
                        </p>
                    </div>
                    <div>
                        <span className="text-sm font-medium text-gray-500">Processing Time:</span>
                        <p className="text-sm text-gray-900">{ipData.metadata.processingTime}ms</p>
                    </div>
                    <div>
                        <span className="text-sm font-medium text-gray-500">Refresh Count:</span>
                        <p className="text-sm text-gray-900">{ipData.metadata.refreshCount}</p>
                    </div>
                    <div>
                        <span className="text-sm font-medium text-gray-500">Data Expires:</span>
                        <p className="text-sm text-gray-900">{formatDate(ipData.metadata.dataExpiresAt)}</p>
                    </div>
                </div>

                {/* Carrier Information (if available) */}
                {(ipData.carrier.name || ipData.carrier.mcc || ipData.carrier.mnc) && (
                    <div className="mt-6 pt-6 border-t border-gray-200">
                        <h3 className="text-md font-medium text-gray-900 flex items-center mb-3">
                            <WifiIcon className="h-5 w-5 mr-2 text-gray-400" />
                            Carrier Information
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            {ipData.carrier.name && (
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Carrier:</span>
                                    <p className="text-sm text-gray-900">{ipData.carrier.name}</p>
                                </div>
                            )}
                            {ipData.carrier.mcc && (
                                <div>
                                    <span className="text-sm font-medium text-gray-500">MCC:</span>
                                    <p className="text-sm text-gray-900">{ipData.carrier.mcc}</p>
                                </div>
                            )}
                            {ipData.carrier.mnc && (
                                <div>
                                    <span className="text-sm font-medium text-gray-500">MNC:</span>
                                    <p className="text-sm text-gray-900">{ipData.carrier.mnc}</p>
                                </div>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
}