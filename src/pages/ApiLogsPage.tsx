import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { ApiRequestLog, ApiLogFilters, PaginatedResponse } from '../types';
import { formatDate, formatDuration, getStatusBadgeColor, formatIpAddress } from '../utils/formatters';
import { isValidIpAddress } from '../utils/validation';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  XMarkIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface LogDetailModalProps {
  log: ApiRequestLog | null;
  isOpen: boolean;
  onClose: () => void;
}

function LogDetailModal({ log, isOpen, onClose }: LogDetailModalProps) {
  if (!isOpen || !log) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">API Request Details</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">IP Address</label>
              <p className="mt-1 text-sm text-gray-900 font-mono">{formatIpAddress(log.ip)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Visitor Hash</label>
              <p className="mt-1 text-sm text-gray-900 font-mono">{log.visitor_hash}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Plugin ID</label>
              <p className="mt-1 text-sm text-gray-900">{log.plugin_id}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Install ID</label>
              <p className="mt-1 text-sm text-gray-900">{log.install_id}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">URL</label>
              <p className="mt-1 text-sm text-gray-900 break-all">{log.url}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Response Status</label>
              <p className="mt-1 text-sm text-gray-900">{log.response_status}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Processing Time</label>
              <p className="mt-1 text-sm text-gray-900">{formatDuration(log.processing_time_ms)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Validation Result</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(log.validation_result)}`}>
                {log.validation_result}
              </span>
            </div>
          </div>

          {/* Request Payload */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Request Payload</label>
            <pre className="bg-gray-50 p-4 rounded-md text-sm overflow-x-auto">
              {JSON.stringify(log.request_payload, null, 2)}
            </pre>
          </div>

          {/* Response Data */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Response Data</label>
            <pre className="bg-gray-50 p-4 rounded-md text-sm overflow-x-auto max-h-64">
              {JSON.stringify(log.response_data, null, 2)}
            </pre>
          </div>

          {/* Validation Errors */}
          {log.validation_errors && log.validation_errors.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Validation Errors</label>
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <ul className="list-disc pl-5 space-y-1">
                  {log.validation_errors.map((error, index) => (
                    <li key={index} className="text-sm text-red-700">{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Timestamp */}
          <div>
            <label className="block text-sm font-medium text-gray-700">Created At</label>
            <p className="mt-1 text-sm text-gray-900">{formatDate(log.created_at)}</p>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}

export default function ApiLogsPage() {
  const [logs, setLogs] = useState<ApiRequestLog[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLog, setSelectedLog] = useState<ApiRequestLog | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [pagination, setPagination] = useState({
    current_page: 1,
    total_pages: 1,
    total_items: 0,
    items_per_page: 20,
  });

  const [filters, setFilters] = useState<ApiLogFilters>({
    page: 1,
    limit: 20,
  });

  const [searchForm, setSearchForm] = useState({
    ip: '',
    plugin_id: '',
    install_id: '',
    url: '',
    date_from: '',
    date_to: '',
    validation_result: '' as '' | 'success' | 'failed',
  });

  useEffect(() => {
    loadLogs();
  }, [filters]);

  const loadLogs = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await api.getApiLogs(filters);
      
      if (response.success && response.data) {
        setLogs(response.data.data);
        setPagination(response.data.pagination);
      } else {
        setError(response.error || 'Failed to load API logs');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    const newFilters: ApiLogFilters = {
      page: 1,
      limit: filters.limit,
    };

    if (searchForm.ip.trim()) newFilters.ip = searchForm.ip.trim();
    if (searchForm.plugin_id.trim()) newFilters.plugin_id = searchForm.plugin_id.trim();
    if (searchForm.install_id.trim()) newFilters.install_id = searchForm.install_id.trim();
    if (searchForm.url.trim()) newFilters.url = searchForm.url.trim();
    if (searchForm.date_from) newFilters.date_from = searchForm.date_from;
    if (searchForm.date_to) newFilters.date_to = searchForm.date_to;
    if (searchForm.validation_result) newFilters.validation_result = searchForm.validation_result;

    setFilters(newFilters);
  };

  const handleClearFilters = () => {
    setSearchForm({
      ip: '',
      plugin_id: '',
      install_id: '',
      url: '',
      date_from: '',
      date_to: '',
      validation_result: '',
    });
    setFilters({ page: 1, limit: 20 });
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  const handleViewDetails = (log: ApiRequestLog) => {
    setSelectedLog(log);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedLog(null);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">API Request Logs</h1>
          <p className="mt-1 text-sm text-gray-600">
            View and search through API request logs and validation results
          </p>
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <FunnelIcon className="h-4 w-4 mr-2" />
          {showFilters ? 'Hide Filters' : 'Show Filters'}
        </button>
      </div>

      {/* Search and Filters */}
      {showFilters && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label htmlFor="ip" className="block text-sm font-medium text-gray-700">
                IP Address
              </label>
              <input
                type="text"
                id="ip"
                value={searchForm.ip}
                onChange={(e) => setSearchForm({ ...searchForm, ip: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="***********"
              />
            </div>

            <div>
              <label htmlFor="plugin_id" className="block text-sm font-medium text-gray-700">
                Plugin ID
              </label>
              <input
                type="text"
                id="plugin_id"
                value={searchForm.plugin_id}
                onChange={(e) => setSearchForm({ ...searchForm, plugin_id: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="12345"
              />
            </div>

            <div>
              <label htmlFor="install_id" className="block text-sm font-medium text-gray-700">
                Install ID
              </label>
              <input
                type="text"
                id="install_id"
                value={searchForm.install_id}
                onChange={(e) => setSearchForm({ ...searchForm, install_id: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="67890"
              />
            </div>

            <div>
              <label htmlFor="url" className="block text-sm font-medium text-gray-700">
                URL
              </label>
              <input
                type="text"
                id="url"
                value={searchForm.url}
                onChange={(e) => setSearchForm({ ...searchForm, url: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="https://guardgeowp.com"
              />
            </div>

            <div>
              <label htmlFor="validation_result" className="block text-sm font-medium text-gray-700">
                Validation Result
              </label>
              <select
                id="validation_result"
                value={searchForm.validation_result}
                onChange={(e) => setSearchForm({ ...searchForm, validation_result: e.target.value as '' | 'success' | 'failed' })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="">All Results</option>
                <option value="success">Success</option>
                <option value="failed">Failed</option>
              </select>
            </div>

            <div>
              <label htmlFor="date_from" className="block text-sm font-medium text-gray-700">
                Date From
              </label>
              <input
                type="date"
                id="date_from"
                value={searchForm.date_from}
                onChange={(e) => setSearchForm({ ...searchForm, date_from: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>

            <div>
              <label htmlFor="date_to" className="block text-sm font-medium text-gray-700">
                Date To
              </label>
              <input
                type="date"
                id="date_to"
                value={searchForm.date_to}
                onChange={(e) => setSearchForm({ ...searchForm, date_to: e.target.value })}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
            </div>
          </div>

          <div className="mt-4 flex space-x-3">
            <button
              onClick={handleSearch}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
              Search
            </button>
            <button
              onClick={handleClearFilters}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              Clear Filters
            </button>
          </div>
        </div>
      )}

      {/* Results */}
      <div className="bg-white shadow rounded-lg">
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
          </div>
        ) : error ? (
          <div className="p-6">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error Loading Logs</h3>
                  <div className="mt-2 text-sm text-red-700">{error}</div>
                </div>
              </div>
            </div>
          </div>
        ) : logs.length === 0 ? (
          <div className="text-center py-12">
            <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No logs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search criteria or check back later.
            </p>
          </div>
        ) : (
          <>
            {/* Table */}
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IP Address
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Plugin/Install
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      URL
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Validation
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {logs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                        {formatIpAddress(log.ip)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>
                          <div>Plugin: {log.plugin_id}</div>
                          <div className="text-gray-500">Install: {log.install_id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        {log.url}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          log.response_status >= 200 && log.response_status < 300
                            ? 'bg-green-100 text-green-800'
                            : log.response_status >= 400
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {log.response_status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(log.validation_result)}`}>
                          {log.validation_result === 'success' ? (
                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                          ) : (
                            <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                          )}
                          {log.validation_result}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {formatDuration(log.processing_time_ms)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(log.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleViewDetails(log)}
                          className="text-indigo-600 hover:text-indigo-900 inline-flex items-center"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.total_pages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => handlePageChange(pagination.current_page - 1)}
                    disabled={pagination.current_page === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => handlePageChange(pagination.current_page + 1)}
                    disabled={pagination.current_page === pagination.total_pages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{' '}
                      <span className="font-medium">
                        {(pagination.current_page - 1) * pagination.items_per_page + 1}
                      </span>{' '}
                      to{' '}
                      <span className="font-medium">
                        {Math.min(pagination.current_page * pagination.items_per_page, pagination.total_items)}
                      </span>{' '}
                      of{' '}
                      <span className="font-medium">{pagination.total_items}</span>{' '}
                      results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() => handlePageChange(pagination.current_page - 1)}
                        disabled={pagination.current_page === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeftIcon className="h-5 w-5" />
                      </button>
                      {Array.from({ length: Math.min(5, pagination.total_pages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <button
                            key={page}
                            onClick={() => handlePageChange(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === pagination.current_page
                                ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                                : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                            }`}
                          >
                            {page}
                          </button>
                        );
                      })}
                      <button
                        onClick={() => handlePageChange(pagination.current_page + 1)}
                        disabled={pagination.current_page === pagination.total_pages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Detail Modal */}
      <LogDetailModal
        log={selectedLog}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </div>
  );
}