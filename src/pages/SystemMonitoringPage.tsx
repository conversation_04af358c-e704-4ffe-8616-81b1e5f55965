import React, { useState, useEffect } from 'react';
import {
    Activity,
    Database,
    Server,
    Clock,
    AlertTriangle,
    CheckCircle,
    XCircle,
    TrendingUp,
    TrendingDown,
    RefreshCw,
    Zap,
    Globe
} from 'lucide-react';

interface HealthStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: string;
    checks: {
        database: {
            status: 'healthy' | 'unhealthy';
            responseTime: number;
            error?: string;
        };
        memory: {
            status: 'healthy' | 'warning' | 'critical';
            usage: {
                heapUsed: number;
                heapTotal: number;
                external: number;
                rss: number;
            };
            percentage: number;
        };
        externalServices: {
            freemius: {
                status: 'healthy' | 'unhealthy';
                responseTime?: number;
                error?: string;
            };
            ipRegistry: {
                status: 'healthy' | 'unhealthy';
                responseTime?: number;
                error?: string;
            };
        };
    };
    uptime: number;
    version: string;
}

interface SystemMetrics {
    systemHealth: string;
    uptime: number;
    totalRequests: number;
    averageResponseTime: number;
    errorRate: number;
    memoryUsage: number;
    databaseStatus: string;
    externalApiStatus: {
        freemius: string;
        ipRegistry: string;
    };
    timestamp: string;
}

interface PerformanceStats {
    endpoint: string;
    totalRequests: number;
    averageResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorRate: number;
    throughput: number;
    lastUpdated: string;
}

interface ExternalApiStatus {
    apiName: string;
    config: {
        name: string;
        maxRequestsPerMinute: number;
        maxRequestsPerHour: number;
        maxRequestsPerDay: number;
    };
    currentUsage: {
        requestsThisMinute: number;
        requestsThisHour: number;
        requestsThisDay: number;
        burstRequests: number;
    };
    status: 'healthy' | 'throttled' | 'backoff';
    backoffMs: number;
    lastRequestTime?: string;
}

const SystemMonitoringPage: React.FC = () => {
    const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
    const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
    const [performanceStats, setPerformanceStats] = useState<PerformanceStats[]>([]);
    const [externalApiStatus, setExternalApiStatus] = useState<ExternalApiStatus[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [autoRefresh, setAutoRefresh] = useState(true);

    const fetchSystemData = async () => {
        try {
            setError(null);

            const [healthRes, metricsRes, performanceRes, apiStatusRes] = await Promise.all([
                fetch('/admin/system/health'),
                fetch('/admin/system/metrics-summary'),
                fetch('/admin/system/performance'),
                fetch('/admin/system/external-api-status')
            ]);

            if (healthRes.ok) {
                const healthData = await healthRes.json();
                setHealthStatus(healthData.data);
            }

            if (metricsRes.ok) {
                const metricsData = await metricsRes.json();
                setSystemMetrics(metricsData.data);
            }

            if (performanceRes.ok) {
                const performanceData = await performanceRes.json();
                setPerformanceStats(performanceData.data.endpointStats || []);
            }

            if (apiStatusRes.ok) {
                const apiData = await apiStatusRes.json();
                setExternalApiStatus(apiData.data.apiStatus || []);
            }

        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch system data');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSystemData();
    }, []);

    useEffect(() => {
        if (!autoRefresh) return;

        const interval = setInterval(fetchSystemData, 30000); // Refresh every 30 seconds
        return () => clearInterval(interval);
    }, [autoRefresh]);

    const formatUptime = (seconds: number): string => {
        const days = Math.floor(seconds / 86400);
        const hours = Math.floor((seconds % 86400) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        if (days > 0) {
            return `${days}d ${hours}h ${minutes}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else {
            return `${minutes}m`;
        }
    };

    const formatBytes = (bytes: number): string => {
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        if (bytes === 0) return '0 Bytes';
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'healthy':
                return <CheckCircle className="w-5 h-5 text-green-500" />;
            case 'degraded':
            case 'warning':
            case 'throttled':
                return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
            case 'unhealthy':
            case 'critical':
            case 'backoff':
                return <XCircle className="w-5 h-5 text-red-500" />;
            default:
                return <Activity className="w-5 h-5 text-gray-500" />;
        }
    };

    const getStatusColor = (status: string): string => {
        switch (status) {
            case 'healthy':
                return 'text-green-600 bg-green-50 border-green-200';
            case 'degraded':
            case 'warning':
            case 'throttled':
                return 'text-yellow-600 bg-yellow-50 border-yellow-200';
            case 'unhealthy':
            case 'critical':
            case 'backoff':
                return 'text-red-600 bg-red-50 border-red-200';
            default:
                return 'text-gray-600 bg-gray-50 border-gray-200';
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center min-h-screen">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <div className="p-6 max-w-7xl mx-auto">
            <div className="flex justify-between items-center mb-6">
                <h1 className="text-3xl font-bold text-gray-900">System Monitoring</h1>
                <div className="flex items-center space-x-4">
                    <label className="flex items-center">
                        <input
                            type="checkbox"
                            checked={autoRefresh}
                            onChange={(e) => setAutoRefresh(e.target.checked)}
                            className="mr-2"
                        />
                        Auto-refresh
                    </label>
                    <button
                        onClick={fetchSystemData}
                        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Refresh
                    </button>
                </div>
            </div>

            {error && (
                <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-red-600">{error}</p>
                </div>
            )}

            {/* System Overview */}
            {systemMetrics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div className="bg-white p-6 rounded-lg shadow border">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">System Health</p>
                                <p className={`text-2xl font-bold capitalize ${getStatusColor(systemMetrics.systemHealth).split(' ')[0]}`}>
                                    {systemMetrics.systemHealth}
                                </p>
                            </div>
                            {getStatusIcon(systemMetrics.systemHealth)}
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow border">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Uptime</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {formatUptime(systemMetrics.uptime)}
                                </p>
                            </div>
                            <Clock className="w-8 h-8 text-blue-500" />
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow border">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Total Requests</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {systemMetrics.totalRequests.toLocaleString()}
                                </p>
                            </div>
                            <Activity className="w-8 h-8 text-green-500" />
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow border">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {systemMetrics.averageResponseTime.toFixed(1)}ms
                                </p>
                            </div>
                            <Zap className="w-8 h-8 text-yellow-500" />
                        </div>
                    </div>
                </div>
            )}

            {/* Health Status Details */}
            {healthStatus && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div className="bg-white p-6 rounded-lg shadow border">
                        <h2 className="text-xl font-semibold mb-4 flex items-center">
                            <Database className="w-5 h-5 mr-2" />
                            Database Status
                        </h2>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between">
                                <span className="text-gray-600">Connection</span>
                                <div className="flex items-center">
                                    {getStatusIcon(healthStatus.checks.database.status)}
                                    <span className={`ml-2 px-2 py-1 rounded text-sm ${getStatusColor(healthStatus.checks.database.status)}`}>
                                        {healthStatus.checks.database.status}
                                    </span>
                                </div>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-gray-600">Response Time</span>
                                <span className="font-medium">{healthStatus.checks.database.responseTime}ms</span>
                            </div>
                        </div>
                    </div>

                    <div className="bg-white p-6 rounded-lg shadow border">
                        <h2 className="text-xl font-semibold mb-4 flex items-center">
                            <Server className="w-5 h-5 mr-2" />
                            Memory Usage
                        </h2>
                        <div className="space-y-3">
                            <div className="flex items-center justify-between">
                                <span className="text-gray-600">Status</span>
                                <div className="flex items-center">
                                    {getStatusIcon(healthStatus.checks.memory.status)}
                                    <span className={`ml-2 px-2 py-1 rounded text-sm ${getStatusColor(healthStatus.checks.memory.status)}`}>
                                        {healthStatus.checks.memory.status}
                                    </span>
                                </div>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-gray-600">Heap Used</span>
                                <span className="font-medium">{formatBytes(healthStatus.checks.memory.usage.heapUsed)}</span>
                            </div>
                            <div className="flex items-center justify-between">
                                <span className="text-gray-600">Usage</span>
                                <span className="font-medium">{healthStatus.checks.memory.percentage.toFixed(1)}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* External API Status */}
            {externalApiStatus.length > 0 && (
                <div className="bg-white p-6 rounded-lg shadow border mb-8">
                    <h2 className="text-xl font-semibold mb-4 flex items-center">
                        <Globe className="w-5 h-5 mr-2" />
                        External API Status
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {externalApiStatus.map((api) => (
                            <div key={api.apiName} className="border rounded-lg p-4">
                                <div className="flex items-center justify-between mb-3">
                                    <h3 className="font-medium">{api.config.name}</h3>
                                    <div className="flex items-center">
                                        {getStatusIcon(api.status)}
                                        <span className={`ml-2 px-2 py-1 rounded text-sm ${getStatusColor(api.status)}`}>
                                            {api.status}
                                        </span>
                                    </div>
                                </div>
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Requests/min:</span>
                                        <span>{api.currentUsage.requestsThisMinute}/{api.config.maxRequestsPerMinute}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-600">Requests/hour:</span>
                                        <span>{api.currentUsage.requestsThisHour}/{api.config.maxRequestsPerHour}</span>
                                    </div>
                                    {api.backoffMs > 0 && (
                                        <div className="flex justify-between">
                                            <span className="text-gray-600">Backoff:</span>
                                            <span className="text-red-600">{(api.backoffMs / 1000).toFixed(1)}s</span>
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Performance Statistics */}
            {performanceStats.length > 0 && (
                <div className="bg-white p-6 rounded-lg shadow border">
                    <h2 className="text-xl font-semibold mb-4 flex items-center">
                        <TrendingUp className="w-5 h-5 mr-2" />
                        Endpoint Performance
                    </h2>
                    <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                                <tr>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Endpoint
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Requests
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Avg Response
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        P95
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Error Rate
                                    </th>
                                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                        Throughput
                                    </th>
                                </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                                {performanceStats.slice(0, 10).map((stat, index) => (
                                    <tr key={index}>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                            {stat.endpoint}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {stat.totalRequests.toLocaleString()}
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {stat.averageResponseTime.toFixed(1)}ms
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {stat.p95ResponseTime.toFixed(1)}ms
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            <span className={`px-2 py-1 rounded text-xs ${stat.errorRate > 5 ? 'bg-red-100 text-red-800' :
                                                    stat.errorRate > 1 ? 'bg-yellow-100 text-yellow-800' :
                                                        'bg-green-100 text-green-800'
                                                }`}>
                                                {stat.errorRate.toFixed(1)}%
                                            </span>
                                        </td>
                                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                            {stat.throughput.toFixed(1)} req/s
                                        </td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SystemMonitoringPage;