import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { ManualTask } from '../types';
import { formatDate, formatTimeAgo, getStatusBadgeColor } from '../utils/formatters';
import {
  PlayIcon,
  ArrowPathIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

interface TaskCardProps {
  title: string;
  description: string;
  type: ManualTask['type'];
  onTrigger: () => void;
  isLoading: boolean;
  disabled?: boolean;
}

function TaskCard({ title, description, type, onTrigger, isLoading, disabled }: TaskCardProps) {
  const getIcon = () => {
    switch (type) {
      case 'refresh_all_ips':
        return ArrowPathIcon;
      case 'sync_freemius_data':
        return Cog6ToothIcon;
      case 'cleanup_old_logs':
        return ClockIcon;
      default:
        return PlayIcon;
    }
  };

  const Icon = getIcon();

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-6">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className="flex items-center justify-center h-12 w-12 rounded-md bg-indigo-500 text-white">
              <Icon className="h-6 w-6" />
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">
                {title}
              </dt>
              <dd className="text-lg font-medium text-gray-900">
                {description}
              </dd>
            </dl>
          </div>
        </div>
        <div className="mt-6">
          <button
            onClick={onTrigger}
            disabled={isLoading || disabled}
            className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Starting...
              </>
            ) : (
              <>
                <PlayIcon className="h-4 w-4 mr-2" />
                Start Task
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

interface TaskHistoryProps {
  tasks: ManualTask[];
  onRefresh: () => void;
  isRefreshing: boolean;
}

function TaskHistory({ tasks, onRefresh, isRefreshing }: TaskHistoryProps) {
  const getStatusIcon = (status: ManualTask['status']) => {
    switch (status) {
      case 'completed':
        return CheckCircleIcon;
      case 'failed':
        return ExclamationTriangleIcon;
      case 'running':
        return ArrowPathIcon;
      case 'pending':
        return ClockIcon;
      default:
        return InformationCircleIcon;
    }
  };

  const getTaskTitle = (type: ManualTask['type']) => {
    switch (type) {
      case 'refresh_all_ips':
        return 'Refresh All IP Data';
      case 'sync_freemius_data':
        return 'Sync Freemius Data';
      case 'cleanup_old_logs':
        return 'Cleanup Old Logs';
      default:
        return type;
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Task History
          </h3>
          <button
            onClick={onRefresh}
            disabled={isRefreshing}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>

        {tasks.length === 0 ? (
          <div className="text-center py-8">
            <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No tasks found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start a manual task to see its progress here.
            </p>
          </div>
        ) : (
          <div className="flow-root">
            <ul className="-my-5 divide-y divide-gray-200">
              {tasks.map((task) => {
                const StatusIcon = getStatusIcon(task.status);
                return (
                  <li key={task.id} className="py-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-shrink-0">
                        <div className={`flex items-center justify-center h-8 w-8 rounded-full ${
                          task.status === 'completed' ? 'bg-green-100' :
                          task.status === 'failed' ? 'bg-red-100' :
                          task.status === 'running' ? 'bg-blue-100' :
                          'bg-yellow-100'
                        }`}>
                          <StatusIcon className={`h-4 w-4 ${
                            task.status === 'completed' ? 'text-green-600' :
                            task.status === 'failed' ? 'text-red-600' :
                            task.status === 'running' ? 'text-blue-600 animate-spin' :
                            'text-yellow-600'
                          }`} />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {getTaskTitle(task.type)}
                        </p>
                        <div className="flex items-center space-x-2 text-sm text-gray-500">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusBadgeColor(task.status)}`}>
                            {task.status}
                          </span>
                          {task.status === 'running' && (
                            <span>• {task.progress_percentage}% complete</span>
                          )}
                        </div>
                      </div>
                      <div className="flex-shrink-0 text-sm text-gray-500">
                        <div className="text-right">
                          <p>{formatTimeAgo(task.created_at)}</p>
                          {task.completed_at && (
                            <p className="text-xs">
                              Completed {formatTimeAgo(task.completed_at)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                    {task.error_message && (
                      <div className="mt-2 ml-12">
                        <div className="bg-red-50 border border-red-200 rounded-md p-3">
                          <p className="text-sm text-red-700">{task.error_message}</p>
                        </div>
                      </div>
                    )}
                  </li>
                );
              })}
            </ul>
          </div>
        )}
      </div>
    </div>
  );
}

export default function ManualTasksPage() {
  const [tasks, setTasks] = useState<ManualTask[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [triggeringTasks, setTriggeringTasks] = useState<Set<ManualTask['type']>>(new Set());
  const [isRefreshing, setIsRefreshing] = useState(false);

  const taskDefinitions = [
    {
      type: 'refresh_all_ips' as const,
      title: 'Refresh All IP Data',
      description: 'Update all cached IP intelligence data from ipRegistry',
    },
    {
      type: 'sync_freemius_data' as const,
      title: 'Sync Freemius Data',
      description: 'Synchronize products and installations from Freemius API',
    },
    {
      type: 'cleanup_old_logs' as const,
      title: 'Cleanup Old Logs',
      description: 'Remove old API request logs and webhook logs',
    },
  ];

  useEffect(() => {
    loadTasks();
    
    // Set up polling for running tasks
    const interval = setInterval(() => {
      const hasRunningTasks = tasks.some(task => task.status === 'running' || task.status === 'pending');
      if (hasRunningTasks) {
        loadTasks();
      }
    }, 5000);

    return () => clearInterval(interval);
  }, [tasks]);

  const loadTasks = async () => {
    try {
      setError(null);
      const response = await api.getManualTasks();
      
      if (response.success && response.data) {
        setTasks(response.data);
      } else {
        setError(response.error || 'Failed to load tasks');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  const handleTriggerTask = async (type: ManualTask['type']) => {
    setTriggeringTasks(prev => new Set(prev).add(type));
    setError(null);

    try {
      const response = await api.createManualTask(type);
      
      if (response.success && response.data) {
        setTasks(prev => [response.data!, ...prev]);
      } else {
        setError(response.error || 'Failed to start task');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setTriggeringTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(type);
        return newSet;
      });
    }
  };

  const handleRefreshTasks = () => {
    setIsRefreshing(true);
    loadTasks();
  };

  const hasRunningTask = (type: ManualTask['type']) => {
    return tasks.some(task => 
      task.type === type && 
      (task.status === 'running' || task.status === 'pending')
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Manual Tasks</h1>
        <p className="mt-1 text-sm text-gray-600">
          Trigger manual refresh tasks and monitor their progress
        </p>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      {/* Task Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3">
        {taskDefinitions.map((taskDef) => (
          <TaskCard
            key={taskDef.type}
            title={taskDef.title}
            description={taskDef.description}
            type={taskDef.type}
            onTrigger={() => handleTriggerTask(taskDef.type)}
            isLoading={triggeringTasks.has(taskDef.type)}
            disabled={hasRunningTask(taskDef.type)}
          />
        ))}
      </div>

      {/* Important Notes */}
      <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div className="flex">
          <InformationCircleIcon className="h-5 w-5 text-blue-400" />
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Important Notes</h3>
            <div className="mt-2 text-sm text-blue-700">
              <ul className="list-disc pl-5 space-y-1">
                <li><strong>Refresh All IP Data:</strong> This will update all cached IP data from ipRegistry. This may take several minutes depending on the number of IPs in the database.</li>
                <li><strong>Sync Freemius Data:</strong> This will fetch the latest product and installation data from the Freemius API to ensure validation accuracy.</li>
                <li><strong>Cleanup Old Logs:</strong> This will remove API request logs and webhook logs older than the configured retention period.</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Task History */}
      <TaskHistory
        tasks={tasks}
        onRefresh={handleRefreshTasks}
        isRefreshing={isRefreshing}
      />
    </div>
  );
}