import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { ManualTask } from '../types';
import { formatTimeAgo, formatDuration } from '../utils/formatters';
import {
    ArrowPathIcon,
    PlayIcon,
    ClockIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    XCircleIcon,
    ChartBarIcon,
    DocumentTextIcon,
} from '@heroicons/react/24/outline';

interface SyncStats {
    lastSyncTime?: string;
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    averageSyncDuration: number;
    productsLastSync: number;
    installationsLastSync: number;
    eventsLastSync: number;
}

interface SyncHistory {
    id: string;
    type: string;
    status: 'pending' | 'running' | 'completed' | 'failed';
    progress_percentage: number;
    started_at: string | null;
    completed_at: string | null;
    error_message: string | null;
    created_by: string;
    created_at: string;
}

export default function FreemiusSyncPage() {
    const [currentTask, setCurrentTask] = useState<ManualTask | null>(null);
    const [syncHistory, setSyncHistory] = useState<SyncHistory[]>([]);
    const [syncStats, setSyncStats] = useState<SyncStats>({
        totalSyncs: 0,
        successfulSyncs: 0,
        failedSyncs: 0,
        averageSyncDuration: 0,
        productsLastSync: 0,
        installationsLastSync: 0,
        eventsLastSync: 0,
    });
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [autoSyncEnabled, setAutoSyncEnabled] = useState(false);
    const [syncInterval, setSyncInterval] = useState(24); // hours

    useEffect(() => {
        loadSyncData();
        const interval = setInterval(loadSyncData, 5000); // Poll every 5 seconds
        return () => clearInterval(interval);
    }, []);

    const loadSyncData = async () => {
        try {
            setIsLoading(true);
            setError(null);

            // Load current sync tasks
            const tasksResponse = await api.getManualTasks();
            if (tasksResponse.success && tasksResponse.data) {
                const freemiusTasks = tasksResponse.data.filter(task => task.type === 'sync_freemius_data');
                const runningTask = freemiusTasks.find(task => task.status === 'running' || task.status === 'pending');
                setCurrentTask(runningTask || null);

                // Use tasks as sync history for now
                setSyncHistory(freemiusTasks.slice(0, 10) as SyncHistory[]);

                // Calculate stats from history
                const completed = freemiusTasks.filter(task => task.status === 'completed');
                const failed = freemiusTasks.filter(task => task.status === 'failed');
                const durations = completed
                    .filter(task => task.started_at && task.completed_at)
                    .map(task => {
                        const start = new Date(task.started_at!).getTime();
                        const end = new Date(task.completed_at!).getTime();
                        return end - start;
                    });

                setSyncStats({
                    totalSyncs: freemiusTasks.length,
                    successfulSyncs: completed.length,
                    failedSyncs: failed.length,
                    averageSyncDuration: durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0,
                    productsLastSync: 0, // Would come from API
                    installationsLastSync: 0, // Would come from API
                    eventsLastSync: 0, // Would come from API
                    lastSyncTime: completed.length > 0 ? completed[0].completed_at || undefined : undefined,
                });
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const handleStartSync = async () => {
        try {
            setError(null);
            const response = await api.syncFreemiusData();

            if (response.success && response.data) {
                setCurrentTask(response.data);
                // Reload data to get updated task
                setTimeout(loadSyncData, 1000);
            } else {
                setError(response.error || 'Failed to start sync');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        }
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'running':
                return <ArrowPathIcon className="h-5 w-5 text-blue-600 animate-spin" />;
            case 'completed':
                return <CheckCircleIcon className="h-5 w-5 text-green-600" />;
            case 'failed':
                return <XCircleIcon className="h-5 w-5 text-red-600" />;
            case 'pending':
                return <ClockIcon className="h-5 w-5 text-yellow-600" />;
            default:
                return <ClockIcon className="h-5 w-5 text-gray-400" />;
        }
    };

    const getStatusBadge = (status: string) => {
        const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
        switch (status) {
            case 'running':
                return `${baseClasses} bg-blue-100 text-blue-800`;
            case 'completed':
                return `${baseClasses} bg-green-100 text-green-800`;
            case 'failed':
                return `${baseClasses} bg-red-100 text-red-800`;
            case 'pending':
                return `${baseClasses} bg-yellow-100 text-yellow-800`;
            default:
                return `${baseClasses} bg-gray-100 text-gray-800`;
        }
    };

    if (isLoading && !currentTask) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Freemius Sync Management</h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Monitor and control Freemius data synchronization processes
                    </p>
                </div>
                <button
                    onClick={handleStartSync}
                    disabled={!!currentTask}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <PlayIcon className="-ml-1 mr-2 h-4 w-4" />
                    {currentTask ? 'Sync Running...' : 'Start Manual Sync'}
                </button>
            </div>

            {/* Error Alert */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error</h3>
                            <div className="mt-2 text-sm text-red-700">{error}</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Current Sync Status */}
            {currentTask && (
                <div className="bg-white shadow rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Current Sync Operation</h3>
                        <span className={getStatusBadge(currentTask.status)}>
                            {currentTask.status.charAt(0).toUpperCase() + currentTask.status.slice(1)}
                        </span>
                    </div>

                    <div className="space-y-4">
                        <div>
                            <div className="flex justify-between text-sm text-gray-600 mb-1">
                                <span>Progress</span>
                                <span>{currentTask.progress_percentage}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                                <div
                                    className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${currentTask.progress_percentage}%` }}
                                />
                            </div>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-sm">
                            <div>
                                <span className="text-gray-500">Started:</span>
                                <div className="font-medium">
                                    {currentTask.started_at ? formatTimeAgo(currentTask.started_at) : 'Not started'}
                                </div>
                            </div>
                            <div>
                                <span className="text-gray-500">Duration:</span>
                                <div className="font-medium">
                                    {currentTask.started_at
                                        ? formatDuration(Date.now() - new Date(currentTask.started_at).getTime())
                                        : 'N/A'
                                    }
                                </div>
                            </div>
                            <div>
                                <span className="text-gray-500">Created by:</span>
                                <div className="font-medium">{currentTask.created_by}</div>
                            </div>
                        </div>

                        {currentTask.error_message && (
                            <div className="bg-red-50 border border-red-200 rounded-md p-3">
                                <p className="text-sm text-red-700">{currentTask.error_message}</p>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Sync Statistics */}
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ChartBarIcon className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        Total Syncs
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {syncStats.totalSyncs}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <CheckCircleIcon className="h-6 w-6 text-green-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        Success Rate
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {syncStats.totalSyncs > 0
                                            ? Math.round((syncStats.successfulSyncs / syncStats.totalSyncs) * 100)
                                            : 0
                                        }%
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ClockIcon className="h-6 w-6 text-purple-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        Avg Duration
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {formatDuration(syncStats.averageSyncDuration)}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="bg-white overflow-hidden shadow rounded-lg">
                    <div className="p-5">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <ArrowPathIcon className="h-6 w-6 text-indigo-600" />
                            </div>
                            <div className="ml-5 w-0 flex-1">
                                <dl>
                                    <dt className="text-sm font-medium text-gray-500 truncate">
                                        Last Sync
                                    </dt>
                                    <dd className="text-lg font-medium text-gray-900">
                                        {syncStats.lastSyncTime ? formatTimeAgo(syncStats.lastSyncTime) : 'Never'}
                                    </dd>
                                </dl>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Sync Configuration */}
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Sync Configuration</h3>
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <label htmlFor="auto-sync" className="text-sm font-medium text-gray-700">
                                Automatic Sync
                            </label>
                            <p className="text-sm text-gray-500">
                                Enable automatic synchronization of Freemius data
                            </p>
                        </div>
                        <button
                            type="button"
                            onClick={() => setAutoSyncEnabled(!autoSyncEnabled)}
                            className={`${autoSyncEnabled ? 'bg-indigo-600' : 'bg-gray-200'
                                } relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`}
                        >
                            <span
                                className={`${autoSyncEnabled ? 'translate-x-5' : 'translate-x-0'
                                    } pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200`}
                            />
                        </button>
                    </div>

                    {autoSyncEnabled && (
                        <div>
                            <label htmlFor="sync-interval" className="block text-sm font-medium text-gray-700">
                                Sync Interval (hours)
                            </label>
                            <select
                                id="sync-interval"
                                value={syncInterval}
                                onChange={(e) => setSyncInterval(Number(e.target.value))}
                                className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            >
                                <option value={1}>Every hour</option>
                                <option value={6}>Every 6 hours</option>
                                <option value={12}>Every 12 hours</option>
                                <option value={24}>Daily</option>
                                <option value={168}>Weekly</option>
                            </select>
                        </div>
                    )}
                </div>
            </div>

            {/* Sync History */}
            <div className="bg-white shadow rounded-lg">
                <div className="px-4 py-5 sm:p-6">
                    <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                        Sync History
                    </h3>

                    {syncHistory.length > 0 ? (
                        <div className="flow-root">
                            <ul className="-my-5 divide-y divide-gray-200">
                                {syncHistory.map((sync) => (
                                    <li key={sync.id} className="py-4">
                                        <div className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                                {getStatusIcon(sync.status)}
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium text-gray-900">
                                                    Freemius Data Sync
                                                </p>
                                                <p className="text-sm text-gray-500">
                                                    Started {sync.started_at ? formatTimeAgo(sync.started_at) : 'Not started'} •
                                                    Created by {sync.created_by}
                                                </p>
                                                {sync.error_message && (
                                                    <p className="text-sm text-red-600 mt-1">
                                                        Error: {sync.error_message}
                                                    </p>
                                                )}
                                            </div>
                                            <div className="flex-shrink-0 text-right">
                                                <span className={getStatusBadge(sync.status)}>
                                                    {sync.status.charAt(0).toUpperCase() + sync.status.slice(1)}
                                                </span>
                                                {sync.status === 'completed' && sync.started_at && sync.completed_at && (
                                                    <p className="text-xs text-gray-500 mt-1">
                                                        {formatDuration(
                                                            new Date(sync.completed_at).getTime() - new Date(sync.started_at).getTime()
                                                        )}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ) : (
                        <div className="text-center py-6">
                            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No sync history</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                No synchronization operations have been performed yet.
                            </p>
                        </div>
                    )}
                </div>
            </div>

            {/* Performance Metrics */}
            <div className="bg-white shadow rounded-lg p-6">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {syncStats.productsLastSync}
                        </div>
                        <div className="text-sm text-gray-500">Products Synced</div>
                        <div className="text-xs text-gray-400">Last sync</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {syncStats.installationsLastSync}
                        </div>
                        <div className="text-sm text-gray-500">Installations Synced</div>
                        <div className="text-xs text-gray-400">Last sync</div>
                    </div>
                    <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                            {syncStats.eventsLastSync}
                        </div>
                        <div className="text-sm text-gray-500">Events Processed</div>
                        <div className="text-xs text-gray-400">Last sync</div>
                    </div>
                </div>
            </div>
        </div>
    );
}