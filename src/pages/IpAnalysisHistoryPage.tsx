import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { api } from '../services/api';
import { formatDate, formatIpAddress, formatDuration } from '../utils/formatters';
import {
    MagnifyingGlassIcon,
    FunnelIcon,
    ArrowDownTrayIcon,
    ChartBarIcon,
    ClockIcon,
    GlobeAltIcon,
    ShieldExclamationIcon,
    CheckCircleIcon,
    ExclamationTriangleIcon,
    InformationCircleIcon,
    ChevronLeftIcon,
    ChevronRightIcon,
    ArrowPathIcon,
} from '@heroicons/react/24/outline';

interface IpAnalysisRequest {
    id: string;
    ip_address: string;
    installation_id: string | null;
    request_source: 'plugin' | 'admin' | 'webhook';
    user_agent: string | null;
    referer: string | null;
    analysis_result: any;
    risk_score: number | null;
    recommendation: 'allow' | 'block' | 'review' | null;
    processing_time_ms: number | null;
    used_cached_data: boolean;
    requested_at: string;
    completed_at: string | null;
    ip_data: {
        country_name: string | null;
        city: string | null;
        is_threat: boolean;
        is_vpn: boolean;
        is_proxy: boolean;
        is_tor: boolean;
    } | null;
    installation: {
        id: string;
        url: string | null;
        title: string | null;
    } | null;
}

interface AnalysisStats {
    totalRequests: number;
    cacheHitRate: number;
    averageProcessingTime: number;
    threatDetections: number;
    topCountries: Array<{
        country: string;
        count: number;
    }>;
    requestsBySource: Array<{
        source: string;
        count: number;
    }>;
    recommendationBreakdown: Array<{
        recommendation: string;
        count: number;
    }>;
}

interface FilterState {
    ip: string;
    source: string;
    recommendation: string;
    startDate: string;
    endDate: string;
    page: number;
    limit: number;
}

interface PaginationInfo {
    page: number;
    limit: number;
    total: number;
    pages: number;
}

function RecommendationBadge({ recommendation }: { recommendation: string | null }) {
    if (!recommendation) return <span className="text-gray-400">-</span>;

    const colors = {
        allow: 'bg-green-100 text-green-800',
        block: 'bg-red-100 text-red-800',
        review: 'bg-yellow-100 text-yellow-800',
    };

    const icons = {
        allow: CheckCircleIcon,
        block: ShieldExclamationIcon,
        review: ExclamationTriangleIcon,
    };

    const Icon = icons[recommendation as keyof typeof icons] || InformationCircleIcon;
    const colorClass = colors[recommendation as keyof typeof colors] || 'bg-gray-100 text-gray-800';

    return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
            <Icon className="h-3 w-3 mr-1" />
            {recommendation}
        </span>
    );
}

function SourceBadge({ source }: { source: string }) {
    const colors = {
        plugin: 'bg-blue-100 text-blue-800',
        admin: 'bg-purple-100 text-purple-800',
        webhook: 'bg-green-100 text-green-800',
    };

    const colorClass = colors[source as keyof typeof colors] || 'bg-gray-100 text-gray-800';

    return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
            {source}
        </span>
    );
}

function SecurityFlagsBadge({ ipData }: { ipData: IpAnalysisRequest['ip_data'] }) {
    if (!ipData) return <span className="text-gray-400">-</span>;

    const flags = [];
    if (ipData.is_threat) flags.push('Threat');
    if (ipData.is_vpn) flags.push('VPN');
    if (ipData.is_proxy) flags.push('Proxy');
    if (ipData.is_tor) flags.push('Tor');

    if (flags.length === 0) {
        return (
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <CheckCircleIcon className="h-3 w-3 mr-1" />
                Clean
            </span>
        );
    }

    return (
        <div className="flex flex-wrap gap-1">
            {flags.map((flag, index) => (
                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <ShieldExclamationIcon className="h-3 w-3 mr-1" />
                    {flag}
                </span>
            ))}
        </div>
    );
}

function StatsCard({ title, value, subtitle, icon: Icon, color }: {
    title: string;
    value: string | number;
    subtitle?: string;
    icon: React.ComponentType<any>;
    color: string;
}) {
    return (
        <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
                <div className="flex items-center">
                    <div className="flex-shrink-0">
                        <div className={`${color} rounded-md p-3`}>
                            <Icon className="h-6 w-6 text-white" />
                        </div>
                    </div>
                    <div className="ml-5 w-0 flex-1">
                        <dl>
                            <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
                            <dd className="text-lg font-medium text-gray-900">{value}</dd>
                            {subtitle && <dd className="text-sm text-gray-500">{subtitle}</dd>}
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default function IpAnalysisHistoryPage() {
    const [requests, setRequests] = useState<IpAnalysisRequest[]>([]);
    const [stats, setStats] = useState<AnalysisStats | null>(null);
    const [pagination, setPagination] = useState<PaginationInfo>({
        page: 1,
        limit: 50,
        total: 0,
        pages: 0,
    });
    const [filters, setFilters] = useState<FilterState>({
        ip: '',
        source: '',
        recommendation: '',
        startDate: '',
        endDate: '',
        page: 1,
        limit: 50,
    });
    const [isLoading, setIsLoading] = useState(true);
    const [isExporting, setIsExporting] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [showFilters, setShowFilters] = useState(false);

    useEffect(() => {
        loadData();
        loadStats();
    }, [filters.page, filters.limit]);

    const loadData = async () => {
        setIsLoading(true);
        setError(null);

        try {
            const response = await api.getIpAnalysisHistory(filters);

            if (response.success && response.data) {
                setRequests(response.data.requests);
                setPagination(response.data.pagination);
            } else {
                setError(response.error || 'Failed to load IP analysis history');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const loadStats = async () => {
        try {
            const response = await api.getIpAnalysisStats('day');

            if (response.success && response.data) {
                setStats(response.data);
            }
        } catch (error: any) {
            console.error('Failed to load stats:', error);
        }
    };

    const handleFilterChange = (key: keyof FilterState, value: string | number) => {
        setFilters(prev => ({
            ...prev,
            [key]: value,
            page: key !== 'page' ? 1 : value, // Reset to page 1 when changing filters
        }));
    };

    const handleSearch = () => {
        loadData();
    };

    const handleExport = async () => {
        setIsExporting(true);

        try {
            // In a real implementation, this would call an export endpoint
            // For now, we'll create a simple CSV export
            const csvData = requests.map(request => ({
                'IP Address': request.ip_address,
                'Source': request.request_source,
                'Recommendation': request.recommendation || '',
                'Risk Score': request.risk_score || '',
                'Processing Time (ms)': request.processing_time_ms || '',
                'Cached': request.used_cached_data ? 'Yes' : 'No',
                'Country': request.ip_data?.country_name || '',
                'City': request.ip_data?.city || '',
                'Requested At': formatDate(request.requested_at),
                'Completed At': request.completed_at ? formatDate(request.completed_at) : '',
            }));

            const csvContent = [
                Object.keys(csvData[0]).join(','),
                ...csvData.map(row => Object.values(row).join(','))
            ].join('\n');

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ip-analysis-history-${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error: any) {
            console.error('Export failed:', error);
        } finally {
            setIsExporting(false);
        }
    };

    const handlePageChange = (newPage: number) => {
        handleFilterChange('page', newPage);
    };

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-2xl font-bold text-gray-900">IP Analysis History</h1>
                <p className="mt-1 text-sm text-gray-600">
                    View and analyze historical IP intelligence requests and performance metrics
                </p>
            </div>

            {/* Statistics Cards */}
            {stats && (
                <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                    <StatsCard
                        title="Total Requests"
                        value={stats.totalRequests.toLocaleString()}
                        subtitle="Last 24 hours"
                        icon={ChartBarIcon}
                        color="bg-blue-500"
                    />
                    <StatsCard
                        title="Cache Hit Rate"
                        value={`${stats.cacheHitRate.toFixed(1)}%`}
                        subtitle="Performance metric"
                        icon={ArrowPathIcon}
                        color="bg-green-500"
                    />
                    <StatsCard
                        title="Avg Processing Time"
                        value={`${stats.averageProcessingTime.toFixed(0)}ms`}
                        subtitle="Response time"
                        icon={ClockIcon}
                        color="bg-purple-500"
                    />
                    <StatsCard
                        title="Threat Detections"
                        value={stats.threatDetections.toLocaleString()}
                        subtitle="Security alerts"
                        icon={ShieldExclamationIcon}
                        color="bg-red-500"
                    />
                </div>
            )}

            {/* Filters and Search */}
            <div className="bg-white shadow rounded-lg p-6">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-gray-900">Search & Filter</h3>
                    <div className="flex items-center space-x-3">
                        <button
                            onClick={() => setShowFilters(!showFilters)}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <FunnelIcon className="h-4 w-4 mr-2" />
                            {showFilters ? 'Hide Filters' : 'Show Filters'}
                        </button>
                        <button
                            onClick={handleExport}
                            disabled={isExporting || requests.length === 0}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        >
                            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                            {isExporting ? 'Exporting...' : 'Export CSV'}
                        </button>
                    </div>
                </div>

                <div className="flex space-x-3 mb-4">
                    <div className="flex-1">
                        <input
                            type="text"
                            placeholder="Search by IP address..."
                            value={filters.ip}
                            onChange={(e) => handleFilterChange('ip', e.target.value)}
                            className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                        />
                    </div>
                    <button
                        onClick={handleSearch}
                        className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                    >
                        <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                        Search
                    </button>
                </div>

                {showFilters && (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-gray-200">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Source</label>
                            <select
                                value={filters.source}
                                onChange={(e) => handleFilterChange('source', e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="">All Sources</option>
                                <option value="plugin">Plugin</option>
                                <option value="admin">Admin</option>
                                <option value="webhook">Webhook</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Recommendation</label>
                            <select
                                value={filters.recommendation}
                                onChange={(e) => handleFilterChange('recommendation', e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            >
                                <option value="">All Recommendations</option>
                                <option value="allow">Allow</option>
                                <option value="block">Block</option>
                                <option value="review">Review</option>
                            </select>
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input
                                type="date"
                                value={filters.startDate}
                                onChange={(e) => handleFilterChange('startDate', e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input
                                type="date"
                                value={filters.endDate}
                                onChange={(e) => handleFilterChange('endDate', e.target.value)}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                            />
                        </div>
                    </div>
                )}
            </div>

            {/* Error Display */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error</h3>
                            <div className="mt-2 text-sm text-red-700">{error}</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Results Table */}
            <div className="bg-white shadow rounded-lg overflow-hidden">
                <div className="px-4 py-5 sm:p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-900">Analysis Requests</h3>
                        <div className="text-sm text-gray-500">
                            Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} results
                        </div>
                    </div>

                    {isLoading ? (
                        <div className="flex items-center justify-center h-32">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                        </div>
                    ) : requests.length === 0 ? (
                        <div className="text-center py-12">
                            <GlobeAltIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No analysis requests found</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Try adjusting your search criteria or date range.
                            </p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            IP Address
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Location
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Source
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Risk Score
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Recommendation
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Security Flags
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Performance
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Requested At
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {requests.map((request) => (
                                        <tr key={request.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <Link
                                                    to={`/admin/ip-detail/${encodeURIComponent(request.ip_address)}`}
                                                    className="text-sm font-mono text-indigo-600 hover:text-indigo-900"
                                                >
                                                    {formatIpAddress(request.ip_address)}
                                                </Link>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {request.ip_data ? (
                                                    <div>
                                                        <div>{request.ip_data.country_name}</div>
                                                        {request.ip_data.city && (
                                                            <div className="text-xs text-gray-500">{request.ip_data.city}</div>
                                                        )}
                                                    </div>
                                                ) : (
                                                    <span className="text-gray-400">-</span>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <SourceBadge source={request.request_source} />
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {request.risk_score !== null ? (
                                                    <span className={`font-medium ${request.risk_score >= 80 ? 'text-red-600' :
                                                            request.risk_score >= 60 ? 'text-orange-600' :
                                                                request.risk_score >= 40 ? 'text-yellow-600' :
                                                                    'text-green-600'
                                                        }`}>
                                                        {request.risk_score}/100
                                                    </span>
                                                ) : (
                                                    <span className="text-gray-400">-</span>
                                                )}
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <RecommendationBadge recommendation={request.recommendation} />
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <SecurityFlagsBadge ipData={request.ip_data} />
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div className="flex items-center space-x-2">
                                                    {request.processing_time_ms && (
                                                        <span className="text-xs text-gray-500">
                                                            {formatDuration(request.processing_time_ms)}
                                                        </span>
                                                    )}
                                                    {request.used_cached_data && (
                                                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                            Cached
                                                        </span>
                                                    )}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {formatDate(request.requested_at)}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}

                    {/* Pagination */}
                    {pagination.pages > 1 && (
                        <div className="flex items-center justify-between mt-6">
                            <div className="flex items-center">
                                <span className="text-sm text-gray-700">
                                    Page {pagination.page} of {pagination.pages}
                                </span>
                            </div>
                            <div className="flex items-center space-x-2">
                                <button
                                    onClick={() => handlePageChange(pagination.page - 1)}
                                    disabled={pagination.page <= 1}
                                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    <ChevronLeftIcon className="h-4 w-4 mr-1" />
                                    Previous
                                </button>
                                <button
                                    onClick={() => handlePageChange(pagination.page + 1)}
                                    disabled={pagination.page >= pagination.pages}
                                    className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                >
                                    Next
                                    <ChevronRightIcon className="h-4 w-4 ml-1" />
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Additional Statistics */}
            {stats && (
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Top Countries */}
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Top Countries</h3>
                        <div className="space-y-3">
                            {stats.topCountries.slice(0, 5).map((country, index) => (
                                <div key={country.country} className="flex items-center justify-between">
                                    <div className="flex items-center">
                                        <span className="text-sm font-medium text-gray-900 mr-2">#{index + 1}</span>
                                        <span className="text-sm text-gray-600">{country.country}</span>
                                    </div>
                                    <span className="text-sm font-medium text-gray-900">
                                        {country.count.toLocaleString()}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Requests by Source */}
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Requests by Source</h3>
                        <div className="space-y-3">
                            {stats.requestsBySource.map((source) => (
                                <div key={source.source} className="flex items-center justify-between">
                                    <SourceBadge source={source.source} />
                                    <span className="text-sm font-medium text-gray-900">
                                        {source.count.toLocaleString()}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Recommendation Breakdown */}
                    <div className="bg-white shadow rounded-lg p-6">
                        <h3 className="text-lg font-medium text-gray-900 mb-4">Recommendations</h3>
                        <div className="space-y-3">
                            {stats.recommendationBreakdown.map((rec) => (
                                <div key={rec.recommendation} className="flex items-center justify-between">
                                    <RecommendationBadge recommendation={rec.recommendation} />
                                    <span className="text-sm font-medium text-gray-900">
                                        {rec.count.toLocaleString()}
                                    </span>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}