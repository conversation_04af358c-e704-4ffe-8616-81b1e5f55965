import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useFeatureAccess } from '../hooks/useRoleAccess';

interface UnauthorizedPageProps {
    requiredRole?: string;
    requiredPermissions?: string[];
    message?: string;
}

export default function UnauthorizedPage({
    requiredRole,
    requiredPermissions,
    message
}: UnauthorizedPageProps) {
    const navigate = useNavigate();
    const { user, logout } = useAuth();
    const featureAccess = useFeatureAccess();

    const handleGoBack = () => {
        navigate(-1);
    };

    const handleGoToDashboard = () => {
        if (featureAccess.canViewDashboard) {
            navigate('/dashboard');
        } else {
            navigate('/');
        }
    };

    const handleLogout = async () => {
        try {
            await logout();
            navigate('/login');
        } catch (error) {
            console.error('Logout error:', error);
            navigate('/login');
        }
    };

    return (
        <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
            <div className="sm:mx-auto sm:w-full sm:max-w-md">
                <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
                    <div className="text-center">
                        {/* Lock Icon */}
                        <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg
                                className="h-6 w-6 text-red-600"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                            >
                                <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                                />
                            </svg>
                        </div>

                        <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
                            Access Denied
                        </h2>

                        <p className="mt-2 text-sm text-gray-600">
                            {message || 'You don\'t have permission to access this page.'}
                        </p>

                        {/* User Info */}
                        {user && (
                            <div className="mt-4 p-4 bg-gray-50 rounded-md">
                                <div className="text-sm text-gray-700">
                                    <p><span className="font-medium">Logged in as:</span> {user.email}</p>
                                    <p><span className="font-medium">Current role:</span> {user.role}</p>
                                </div>
                            </div>
                        )}

                        {/* Required Access Info */}
                        {(requiredRole || requiredPermissions) && (
                            <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                                <div className="text-sm text-yellow-800">
                                    <p className="font-medium mb-2">Required access:</p>
                                    {requiredRole && (
                                        <p>• Role: <span className="font-mono">{requiredRole}</span></p>
                                    )}
                                    {requiredPermissions && requiredPermissions.length > 0 && (
                                        <div>
                                            <p>• Permissions:</p>
                                            <ul className="ml-4 mt-1">
                                                {requiredPermissions.map((permission, index) => (
                                                    <li key={index} className="font-mono text-xs">
                                                        {permission}
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    )}
                                </div>
                            </div>
                        )}

                        {/* Action Buttons */}
                        <div className="mt-6 space-y-3">
                            <button
                                onClick={handleGoBack}
                                className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Go Back
                            </button>

                            {featureAccess.canViewDashboard && (
                                <button
                                    onClick={handleGoToDashboard}
                                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Go to Dashboard
                                </button>
                            )}

                            <button
                                onClick={handleLogout}
                                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                            >
                                Logout
                            </button>
                        </div>

                        {/* Contact Info */}
                        <div className="mt-6 text-xs text-gray-500">
                            <p>
                                If you believe you should have access to this page, please contact your administrator.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}