import React, { useState, useEffect, useCallback } from 'react';
import { api } from '../services/api';
import { DashboardStats } from '../types';
import { formatNumber, formatPercentage, formatDuration } from '../utils/formatters';
import {
  ChartBarIcon,
  GlobeAltIcon,
  ShieldExclamationIcon,
  ClockIcon,
  EyeIcon,
  ServerIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
} from '@heroicons/react/24/outline';

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  database: { status: string; responseTime: number };
  externalApis: {
    freemius: { status: string; lastCheck: string };
    ipRegistry: { status: string; lastCheck: string };
  };
  memory: { used: number; total: number; percentage: number };
  uptime: number;
  lastUpdated: string;
}

interface RecentActivity {
  requests: any[];
  logins: any[];
  errors: any[];
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const loadDashboardData = useCallback(async (showRefreshing = false) => {
    try {
      if (showRefreshing) {
        setIsRefreshing(true);
      } else {
        setIsLoading(true);
      }
      setError(null);

      // Load all dashboard data in parallel
      const [statsResponse, healthResponse, activityResponse] = await Promise.allSettled([
        api.getDashboardStats(),
        api.getSystemHealth(),
        api.getRecentActivity(10)
      ]);

      // Handle stats response
      if (statsResponse.status === 'fulfilled' && statsResponse.value.success && statsResponse.value.data) {
        setStats(statsResponse.value.data);
      } else if (statsResponse.status === 'rejected' || !statsResponse.value.success) {
        console.error('Failed to load dashboard stats:',
          statsResponse.status === 'rejected' ? statsResponse.reason : statsResponse.value.error);
      }

      // Handle system health response
      if (healthResponse.status === 'fulfilled' && healthResponse.value.success && healthResponse.value.data) {
        setSystemHealth(healthResponse.value.data);
      } else if (healthResponse.status === 'rejected' || !healthResponse.value.success) {
        console.error('Failed to load system health:',
          healthResponse.status === 'rejected' ? healthResponse.reason : healthResponse.value.error);
      }

      // Handle recent activity response
      if (activityResponse.status === 'fulfilled' && activityResponse.value.success && activityResponse.value.data) {
        setRecentActivity(activityResponse.value.data);
      } else if (activityResponse.status === 'rejected' || !activityResponse.value.success) {
        console.error('Failed to load recent activity:',
          activityResponse.status === 'rejected' ? activityResponse.reason : activityResponse.value.error);
      }

      setLastUpdated(new Date());
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
      console.error('Dashboard loading error:', error);
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadDashboardData();

    // Set up auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadDashboardData(true);
    }, 30000);

    return () => clearInterval(interval);
  }, [loadDashboardData]);

  const handleRefresh = () => {
    loadDashboardData(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error Loading Dashboard</h3>
            <div className="mt-2 text-sm text-red-700">{error}</div>
          </div>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500">No dashboard data available</p>
      </div>
    );
  }

  const statCards = [
    {
      name: 'API Requests Today',
      value: formatNumber(stats.total_api_requests_today),
      icon: ChartBarIcon,
      color: 'bg-blue-500',
    },
    {
      name: 'API Requests This Month',
      value: formatNumber(stats.total_api_requests_this_month),
      icon: ChartBarIcon,
      color: 'bg-green-500',
    },
    {
      name: 'Unique IPs Today',
      value: formatNumber(stats.unique_ips_today),
      icon: GlobeAltIcon,
      color: 'bg-purple-500',
    },
    {
      name: 'Unique IPs This Month',
      value: formatNumber(stats.unique_ips_this_month),
      icon: GlobeAltIcon,
      color: 'bg-indigo-500',
    },
    {
      name: 'Active Installations',
      value: formatNumber(stats.active_installations),
      icon: ServerIcon,
      color: 'bg-yellow-500',
    },
    {
      name: 'Total Products',
      value: formatNumber(stats.total_products),
      icon: EyeIcon,
      color: 'bg-pink-500',
    },
    {
      name: 'Cache Hit Rate',
      value: formatPercentage(stats.cache_hit_rate_percentage),
      icon: ClockIcon,
      color: 'bg-teal-500',
    },
    {
      name: 'Avg Response Time',
      value: formatDuration(stats.average_response_time_ms),
      icon: ClockIcon,
      color: 'bg-orange-500',
    },
  ];

  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'critical': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return CheckCircleIcon;
      case 'warning': return ExclamationTriangleIcon;
      case 'critical': return ExclamationTriangleIcon;
      default: return ClockIcon;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="mt-1 text-sm text-gray-600">
            Overview of GuardGeo API Platform activity and performance
          </p>
          {lastUpdated && (
            <p className="mt-1 text-xs text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </p>
          )}
        </div>
        <button
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          {isRefreshing ? 'Refreshing...' : 'Refresh'}
        </button>
      </div>

      {/* System Health Status */}
      {systemHealth && (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg leading-6 font-medium text-gray-900">System Health</h3>
              <div className="flex items-center">
                {React.createElement(getHealthStatusIcon(systemHealth.status), {
                  className: `h-5 w-5 ${getHealthStatusColor(systemHealth.status)}`
                })}
                <span className={`ml-2 text-sm font-medium ${getHealthStatusColor(systemHealth.status)}`}>
                  {systemHealth.status.charAt(0).toUpperCase() + systemHealth.status.slice(1)}
                </span>
              </div>
            </div>
            <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-500">Database</div>
                <div className="mt-1 text-sm text-gray-900">
                  {systemHealth.database.status} ({systemHealth.database.responseTime}ms)
                </div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-500">Freemius API</div>
                <div className="mt-1 text-sm text-gray-900">{systemHealth.externalApis.freemius.status}</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-500">ipRegistry API</div>
                <div className="mt-1 text-sm text-gray-900">{systemHealth.externalApis.ipRegistry.status}</div>
              </div>
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="text-sm font-medium text-gray-500">Memory Usage</div>
                <div className="mt-1 text-sm text-gray-900">
                  {formatPercentage(systemHealth.memory.percentage)}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((stat) => (
          <div key={stat.name} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`${stat.color} rounded-md p-3`}>
                    <stat.icon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stat.value}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Threat Detection Alert */}
      {stats.threat_detections_today > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ShieldExclamationIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">
                Threat Detections Today
              </h3>
              <div className="mt-2 text-sm text-red-700">
                <p>
                  {formatNumber(stats.threat_detections_today)} potential threats detected today.
                  Review the API logs for detailed information.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Recent Activity and Top Countries */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Requests */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Recent API Requests
            </h3>
            <div className="mt-5">
              {(recentActivity?.requests || stats?.recent_requests || []).length > 0 ? (
                <div className="flow-root">
                  <ul className="-my-5 divide-y divide-gray-200">
                    {(recentActivity?.requests || stats?.recent_requests || []).slice(0, 5).map((request) => (
                      <li key={request.id} className="py-4">
                        <div className="flex items-center space-x-4">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {request.ip}
                            </p>
                            <p className="text-sm text-gray-500 truncate">
                              Plugin ID: {request.plugin_id} • Install ID: {request.install_id}
                            </p>
                            <p className="text-xs text-gray-400">
                              {new Date(request.created_at).toLocaleString()}
                            </p>
                          </div>
                          <div className="flex-shrink-0">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${request.validation_result === 'success'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                              }`}>
                              {request.validation_result}
                            </span>
                          </div>
                        </div>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No recent requests</p>
              )}
            </div>
          </div>
        </div>

        {/* Top Countries */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 py-5 sm:p-6">
            <h3 className="text-lg leading-6 font-medium text-gray-900">
              Top Countries
            </h3>
            <div className="mt-5">
              {stats.top_countries.length > 0 ? (
                <div className="space-y-3">
                  {stats.top_countries.slice(0, 5).map((country, index) => (
                    <div key={country.country} className="flex items-center justify-between">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-900">
                          #{index + 1}
                        </span>
                        <span className="ml-3 text-sm text-gray-600">
                          {country.country}
                        </span>
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {formatNumber(country.count)}
                      </span>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No country data available</p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}