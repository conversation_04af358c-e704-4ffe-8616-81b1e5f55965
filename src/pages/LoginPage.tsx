import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { validateLoginForm } from '../utils/validation';
import { ShieldCheckIcon, ExclamationTriangleIcon, ClockIcon } from '@heroicons/react/24/outline';

export default function LoginPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSecurityAlert, setShowSecurityAlert] = useState(false);

  const {
    login,
    isAuthenticated,
    requires2FA,
    lockoutInfo,
    securityEvents,
    clearSecurityEvents
  } = useAuth();
  const location = useLocation();

  const from = location.state?.from?.pathname || '/admin';

  // Handle security events
  useEffect(() => {
    if (securityEvents.length > 0) {
      setShowSecurityAlert(true);
      const timer = setTimeout(() => {
        setShowSecurityAlert(false);
        clearSecurityEvents();
      }, 10000); // Auto-dismiss after 10 seconds

      return () => clearTimeout(timer);
    }
  }, [securityEvents, clearSecurityEvents]);

  // Clear errors when lockout expires
  useEffect(() => {
    if (lockoutInfo && !lockoutInfo.isLocked) {
      setErrors([]);
    }
  }, [lockoutInfo]);

  // Handle redirects after all hooks are called
  if (isAuthenticated) {
    return <Navigate to={from} replace />;
  }

  if (requires2FA) {
    return <Navigate to="/2fa" state={{ from: location.state?.from }} replace />;
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if account is locked
    if (lockoutInfo?.isLocked) {
      setErrors([`Account is locked. Please try again in ${lockoutInfo.remainingTime || 0} minutes.`]);
      return;
    }

    const validationErrors = validateLoginForm(email, password);
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsLoading(true);
    setErrors([]);

    try {
      const result = await login({ email, password });

      if (result.lockoutInfo?.isLocked) {
        const remainingTime = result.lockoutInfo.remainingTime ||
          Math.ceil(((result.lockoutInfo.lockoutExpires?.getTime() || 0) - Date.now()) / 60000);
        setErrors([`Account locked due to multiple failed attempts. Try again in ${remainingTime} minutes.`]);
        return;
      }

      if (result.requires2FA) {
        // Will be redirected by the useEffect in the component
        return;
      }

      if (result.requiresPasswordChange) {
        // Will be redirected to password change page
        return;
      }

      // Will be redirected by the isAuthenticated check above
    } catch (error: any) {
      setErrors([error.message || 'Login failed. Please try again.']);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-12 w-12 bg-indigo-600 rounded-lg flex items-center justify-center">
              <ShieldCheckIcon className="h-8 w-8 text-white" />
            </div>
            <span className="ml-3 text-2xl font-bold text-gray-900">GuardGeo</span>
          </div>
        </div>
        <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          IP Intelligence Platform - Admin Access Only
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          {/* Security Events Alert */}
          {showSecurityAlert && securityEvents.length > 0 && (
            <div className="mb-6 bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div className="flex">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Security Alert
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    {securityEvents.map((event, index) => (
                      <div key={index} className="mb-1">
                        <strong>{event.type}:</strong> {event.message}
                      </div>
                    ))}
                  </div>
                  <button
                    type="button"
                    onClick={() => {
                      setShowSecurityAlert(false);
                      clearSecurityEvents();
                    }}
                    className="mt-2 text-sm text-yellow-800 underline hover:text-yellow-900"
                  >
                    Dismiss
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Account Lockout Display */}
          {lockoutInfo?.isLocked && (
            <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <ClockIcon className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">
                    Account Temporarily Locked
                  </h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>Your account has been locked due to multiple failed login attempts.</p>
                    <p className="mt-1">
                      <strong>Failed attempts:</strong> {lockoutInfo.failedAttempts}
                    </p>
                    {lockoutInfo.remainingTime && lockoutInfo.remainingTime > 0 && (
                      <p className="mt-1">
                        <strong>Try again in:</strong> {lockoutInfo.remainingTime} minutes
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          <form className="space-y-6" onSubmit={handleSubmit}>
            {errors.length > 0 && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-red-800">
                      {errors.length === 1 ? 'Error' : 'Errors'}
                    </h3>
                    <div className="mt-2 text-sm text-red-700">
                      <ul className="list-disc pl-5 space-y-1">
                        {errors.map((error, index) => (
                          <li key={index}>{error}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email address
              </label>
              <div className="mt-1">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                Password
              </label>
              <div className="mt-1">
                <input
                  id="password"
                  name="password"
                  type="password"
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={isLoading || lockoutInfo?.isLocked}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </>
                ) : lockoutInfo?.isLocked ? (
                  `Account Locked (${lockoutInfo.remainingTime || 0}m)`
                ) : (
                  'Sign in'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}