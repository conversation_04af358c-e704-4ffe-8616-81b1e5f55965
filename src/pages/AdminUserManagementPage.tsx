import React, { useState, useEffect } from 'react';
import {
    UserPlusIcon,
    PencilIcon,
    TrashIcon,
    CheckCircleIcon,
    XCircleIcon,
    KeyIcon,
    ShieldCheckIcon,
    UserIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { api } from '../services/api';

// Helper functions previously in adminService
const generateSecurePassword = (): string => {
    const length = 12;
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < length; i++) {
        password += charset.charAt(Math.floor(Math.random() * charset.length));
    }
    return password;
};

const getAvailableRoles = (userRole: string): Array<{ value: string; label: string; description: string }> => {
    const normalizedRole = userRole.toUpperCase();
    if (normalizedRole === 'SUPER_ADMIN') {
        return [
            {
                value: 'ADMIN',
                label: 'Admin',
                description: 'Full administrative access with user management capabilities'
            },
            {
                value: 'MODERATOR',
                label: 'Moderator',
                description: 'Limited administrative access for content and user moderation'
            }
        ];
    }
    return [];
};

import {
    AdminUser,
    CreateAdminRequest,
    UpdateAdminRequest,
    AdminListFilters
} from '../types';

const AdminUserManagementPage: React.FC = () => {
    const { user: currentUser } = useAuth();
    const [adminUsers, setAdminUsers] = useState<AdminUser[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showCreateModal, setShowCreateModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [selectedUser, setSelectedUser] = useState<AdminUser | null>(null);
    const [filters, setFilters] = useState<AdminListFilters>({
        page: 1,
        limit: 10
    });
    const [pagination, setPagination] = useState({
        page: 1,
        limit: 10,
        total: 0,
        totalPages: 0
    });

    // Form states
    const [createForm, setCreateForm] = useState<CreateAdminRequest>({
        email: '',
        firstName: '',
        lastName: '',
        role: 'ADMIN',
        requirePasswordChange: true
    });
    const [updateForm, setUpdateForm] = useState<UpdateAdminRequest>({});
    const [generatedPassword, setGeneratedPassword] = useState<string | null>(null);
    const [showPassword, setShowPassword] = useState(false);

    // Load admin users
    const loadAdminUsers = async () => {
        try {
            setLoading(true);
            const response = await api.getAdminUsers(filters);
            if (response.success && response.data) {
                setAdminUsers(response.data.users);
                setPagination(response.data.pagination);
            } else {
                throw new Error(response.error || 'Failed to load admin users');
            }
            setError(null);
        } catch (err) {
            setError('Failed to load admin users');
            console.error('Load admin users error:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadAdminUsers();
    }, [filters]);

    // Handle create admin user
    const handleCreateAdmin = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            setLoading(true);

            // Generate temporary password if not provided
            let tempPassword = createForm.temporaryPassword;
            if (!tempPassword) {
                tempPassword = generateSecurePassword();
                setGeneratedPassword(tempPassword);
            }

            const adminData: CreateAdminRequest = {
                ...createForm,
                temporaryPassword: tempPassword
            };

            const result = await api.createAdminUser(adminData);

            if (result.success && result.data) {
                setShowCreateModal(false);
                setCreateForm({
                    email: '',
                    firstName: '',
                    lastName: '',
                    role: 'ADMIN',
                    requirePasswordChange: true
                });

                if (result.data.temporaryPassword) {
                    setGeneratedPassword(result.data.temporaryPassword);
                    setShowPassword(true);
                }
                await loadAdminUsers();
            } else {
                setError(result.error || 'Failed to create admin user');
            }
        } catch (err) {
            setError('Failed to create admin user');
            console.error('Create admin error:', err);
        } finally {
            setLoading(false);
        }
    };

    // Handle update admin user
    const handleUpdateAdmin = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!selectedUser) return;

        try {
            setLoading(true);

            const result = await api.updateAdminUser(selectedUser.id, updateForm);

            if (result.success && result.data) {
                setShowEditModal(false);
                setSelectedUser(null);
                setUpdateForm({});
                await loadAdminUsers();
            } else {
                setError(result.error || 'Failed to update admin user');
            }
        } catch (err) {
            setError('Failed to update admin user');
            console.error('Update admin error:', err);
        } finally {
            setLoading(false);
        }
    };

    // Handle deactivate admin user
    const handleDeactivateAdmin = async (user: AdminUser) => {
        if (!confirm(`Are you sure you want to deactivate ${user.first_name} ${user.last_name}?`)) {
            return;
        }

        try {
            setLoading(true);

            const result = await api.deactivateAdminUser(user.id);

            if (result.success && result.data) {
                await loadAdminUsers();
            } else {
                setError(result.error || 'Failed to deactivate admin user');
            }
        } catch (err) {
            setError('Failed to deactivate admin user');
            console.error('Deactivate admin error:', err);
        } finally {
            setLoading(false);
        }
    };

    // Check if current user can manage admin users
    const canManageAdmins = currentUser?.role === 'super_admin';
    const availableRoles = getAvailableRoles(currentUser?.role?.toUpperCase() || '');

    if (!currentUser) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
            </div>
        );
    }

    if (!canManageAdmins) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="text-center">
                    <ShieldCheckIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        You don't have permission to manage admin users.
                    </p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50">
            <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
                {/* Header */}
                <div className="md:flex md:items-center md:justify-between mb-6">
                    <div className="flex-1 min-w-0">
                        <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                            Admin User Management
                        </h2>
                        <p className="mt-1 text-sm text-gray-500">
                            Create and manage admin users with role-based permissions
                        </p>
                    </div>
                    <div className="mt-4 flex md:mt-0 md:ml-4">
                        <button
                            onClick={() => setShowCreateModal(true)}
                            className="ml-3 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                        >
                            <UserPlusIcon className="-ml-1 mr-2 h-5 w-5" />
                            Create Admin User
                        </button>
                    </div>
                </div>

                {/* Error Alert */}
                {error && (
                    <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
                        <div className="flex">
                            <XCircleIcon className="h-5 w-5 text-red-400" />
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-red-800">Error</h3>
                                <p className="mt-1 text-sm text-red-700">{error}</p>
                            </div>
                            <button
                                onClick={() => setError(null)}
                                className="ml-auto -mx-1.5 -my-1.5 bg-red-50 text-red-500 rounded-lg focus:ring-2 focus:ring-red-600 p-1.5 hover:bg-red-100"
                            >
                                <XCircleIcon className="w-5 h-5" />
                            </button>
                        </div>
                    </div>
                )}

                {/* Filters */}
                <div className="bg-white shadow rounded-lg mb-6">
                    <div className="px-4 py-5 sm:p-6">
                        <div className="grid grid-cols-1 gap-4 sm:grid-cols-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Role</label>
                                <select
                                    value={filters.role || ''}
                                    onChange={(e) => setFilters({ ...filters, role: e.target.value as any || undefined, page: 1 })}
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                >
                                    <option value="">All Roles</option>
                                    <option value="ADMIN">Admin</option>
                                    <option value="MODERATOR">Moderator</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Status</label>
                                <select
                                    value={filters.isActive === undefined ? '' : filters.isActive.toString()}
                                    onChange={(e) => setFilters({
                                        ...filters,
                                        isActive: e.target.value === '' ? undefined : e.target.value === 'true',
                                        page: 1
                                    })}
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                >
                                    <option value="">All Status</option>
                                    <option value="true">Active</option>
                                    <option value="false">Inactive</option>
                                </select>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700">Search</label>
                                <input
                                    type="text"
                                    value={filters.search || ''}
                                    onChange={(e) => setFilters({ ...filters, search: e.target.value || undefined, page: 1 })}
                                    placeholder="Search by name or email..."
                                    className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                />
                            </div>
                            <div className="flex items-end">
                                <button
                                    onClick={() => setFilters({ page: 1, limit: 10 })}
                                    className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                >
                                    Clear Filters
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Admin Users Table */}
                <div className="bg-white shadow overflow-hidden sm:rounded-md">
                    <div className="px-4 py-5 sm:px-6">
                        <h3 className="text-lg leading-6 font-medium text-gray-900">
                            Admin Users ({pagination.total})
                        </h3>
                    </div>

                    {loading ? (
                        <div className="px-4 py-12 text-center">
                            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                            <p className="mt-2 text-sm text-gray-500">Loading admin users...</p>
                        </div>
                    ) : adminUsers.length === 0 ? (
                        <div className="px-4 py-12 text-center">
                            <UserIcon className="mx-auto h-12 w-12 text-gray-400" />
                            <h3 className="mt-2 text-sm font-medium text-gray-900">No admin users found</h3>
                            <p className="mt-1 text-sm text-gray-500">
                                Get started by creating your first admin user.
                            </p>
                        </div>
                    ) : (
                        <ul className="divide-y divide-gray-200">
                            {adminUsers.map((user) => (
                                <li key={user.id} className="px-4 py-4 sm:px-6">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center">
                                            <div className="flex-shrink-0">
                                                <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                                    <UserIcon className="h-6 w-6 text-gray-600" />
                                                </div>
                                            </div>
                                            <div className="ml-4">
                                                <div className="flex items-center">
                                                    <p className="text-sm font-medium text-gray-900">
                                                        {user.first_name} {user.last_name}
                                                    </p>
                                                    <span className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${user.role === 'ADMIN'
                                                        ? 'bg-blue-100 text-blue-800'
                                                        : 'bg-green-100 text-green-800'
                                                        }`}>
                                                        {user.role}
                                                    </span>
                                                    {user.is_active ? (
                                                        <CheckCircleIcon className="ml-2 h-4 w-4 text-green-500" />
                                                    ) : (
                                                        <XCircleIcon className="ml-2 h-4 w-4 text-red-500" />
                                                    )}
                                                </div>
                                                <p className="text-sm text-gray-500">{user.email}</p>
                                                <div className="flex items-center text-xs text-gray-400 mt-1">
                                                    <span>Created: {new Date(user.created_at).toLocaleDateString()}</span>
                                                    {user.requires_password_change && (
                                                        <span className="ml-4 flex items-center text-orange-600">
                                                            <KeyIcon className="h-3 w-3 mr-1" />
                                                            Password change required
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <button
                                                onClick={() => {
                                                    setSelectedUser(user);
                                                    setUpdateForm({
                                                        email: user.email,
                                                        firstName: user.first_name,
                                                        lastName: user.last_name,
                                                        role: user.role as 'ADMIN' | 'MODERATOR',
                                                        isActive: user.is_active
                                                    });
                                                    setShowEditModal(true);
                                                }}
                                                className="text-indigo-600 hover:text-indigo-900 text-sm font-medium"
                                            >
                                                <PencilIcon className="h-4 w-4" />
                                            </button>
                                            {user.is_active && (
                                                <button
                                                    onClick={() => handleDeactivateAdmin(user)}
                                                    className="text-red-600 hover:text-red-900 text-sm font-medium"
                                                >
                                                    <TrashIcon className="h-4 w-4" />
                                                </button>
                                            )}
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    )}

                    {/* Pagination */}
                    {pagination.totalPages > 1 && (
                        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                            <div className="flex-1 flex justify-between sm:hidden">
                                <button
                                    onClick={() => setFilters({ ...filters, page: Math.max(1, pagination.page - 1) })}
                                    disabled={pagination.page <= 1}
                                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                >
                                    Previous
                                </button>
                                <button
                                    onClick={() => setFilters({ ...filters, page: Math.min(pagination.totalPages, pagination.page + 1) })}
                                    disabled={pagination.page >= pagination.totalPages}
                                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                                >
                                    Next
                                </button>
                            </div>
                            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                                <div>
                                    <p className="text-sm text-gray-700">
                                        Showing{' '}
                                        <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                                        {' '}to{' '}
                                        <span className="font-medium">
                                            {Math.min(pagination.page * pagination.limit, pagination.total)}
                                        </span>
                                        {' '}of{' '}
                                        <span className="font-medium">{pagination.total}</span>
                                        {' '}results
                                    </p>
                                </div>
                                <div>
                                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                        <button
                                            onClick={() => setFilters({ ...filters, page: Math.max(1, pagination.page - 1) })}
                                            disabled={pagination.page <= 1}
                                            className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                        >
                                            Previous
                                        </button>
                                        <button
                                            onClick={() => setFilters({ ...filters, page: Math.min(pagination.totalPages, pagination.page + 1) })}
                                            disabled={pagination.page >= pagination.totalPages}
                                            className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                                        >
                                            Next
                                        </button>
                                    </nav>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Create Admin Modal */}
            {showCreateModal && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Create Admin User</h3>
                            <form onSubmit={handleCreateAdmin} className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Email</label>
                                    <input
                                        type="email"
                                        required
                                        value={createForm.email}
                                        onChange={(e) => setCreateForm({ ...createForm, email: e.target.value })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">First Name</label>
                                    <input
                                        type="text"
                                        required
                                        value={createForm.firstName}
                                        onChange={(e) => setCreateForm({ ...createForm, firstName: e.target.value })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Last Name</label>
                                    <input
                                        type="text"
                                        required
                                        value={createForm.lastName}
                                        onChange={(e) => setCreateForm({ ...createForm, lastName: e.target.value })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Role</label>
                                    <select
                                        value={createForm.role}
                                        onChange={(e) => setCreateForm({ ...createForm, role: e.target.value as 'ADMIN' | 'MODERATOR' })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    >
                                        {availableRoles.map((role) => (
                                            <option key={role.value} value={role.value}>
                                                {role.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Temporary Password (optional)</label>
                                    <input
                                        type="password"
                                        value={createForm.temporaryPassword || ''}
                                        onChange={(e) => setCreateForm({ ...createForm, temporaryPassword: e.target.value || undefined })}
                                        placeholder="Leave empty to auto-generate"
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                    <p className="mt-1 text-xs text-gray-500">
                                        If left empty, a secure password will be generated automatically
                                    </p>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={createForm.requirePasswordChange !== false}
                                        onChange={(e) => setCreateForm({ ...createForm, requirePasswordChange: e.target.checked })}
                                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    />
                                    <label className="ml-2 block text-sm text-gray-900">
                                        Require password change on first login
                                    </label>
                                </div>
                                <div className="flex justify-end space-x-3 pt-4">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowCreateModal(false);
                                            setCreateForm({
                                                email: '',
                                                firstName: '',
                                                lastName: '',
                                                role: 'ADMIN',
                                                requirePasswordChange: true
                                            });
                                        }}
                                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={loading}
                                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                    >
                                        {loading ? 'Creating...' : 'Create Admin'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {/* Edit Admin Modal */}
            {showEditModal && selectedUser && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Edit Admin User</h3>
                            <form onSubmit={handleUpdateAdmin} className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Email</label>
                                    <input
                                        type="email"
                                        required
                                        value={updateForm.email || ''}
                                        onChange={(e) => setUpdateForm({ ...updateForm, email: e.target.value })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">First Name</label>
                                    <input
                                        type="text"
                                        required
                                        value={updateForm.firstName || ''}
                                        onChange={(e) => setUpdateForm({ ...updateForm, firstName: e.target.value })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Last Name</label>
                                    <input
                                        type="text"
                                        required
                                        value={updateForm.lastName || ''}
                                        onChange={(e) => setUpdateForm({ ...updateForm, lastName: e.target.value })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    />
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Role</label>
                                    <select
                                        value={updateForm.role || ''}
                                        onChange={(e) => setUpdateForm({ ...updateForm, role: e.target.value as 'ADMIN' | 'MODERATOR' })}
                                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                    >
                                        {availableRoles.map((role) => (
                                            <option key={role.value} value={role.value}>
                                                {role.label}
                                            </option>
                                        ))}
                                    </select>
                                </div>
                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={updateForm.isActive !== false}
                                        onChange={(e) => setUpdateForm({ ...updateForm, isActive: e.target.checked })}
                                        className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                                    />
                                    <label className="ml-2 block text-sm text-gray-900">
                                        Active
                                    </label>
                                </div>
                                <div className="flex justify-end space-x-3 pt-4">
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowEditModal(false);
                                            setSelectedUser(null);
                                            setUpdateForm({});
                                        }}
                                        className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        Cancel
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={loading}
                                        className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                                    >
                                        {loading ? 'Updating...' : 'Update Admin'}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            )}

            {/* Generated Password Modal */}
            {showPassword && generatedPassword && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div className="mt-3 text-center">
                            <CheckCircleIcon className="mx-auto h-12 w-12 text-green-500" />
                            <h3 className="text-lg font-medium text-gray-900 mt-4 mb-4">Admin User Created Successfully</h3>
                            <div className="bg-gray-50 p-4 rounded-md mb-4">
                                <p className="text-sm text-gray-700 mb-2">Temporary Password:</p>
                                <div className="bg-white p-2 rounded border font-mono text-sm break-all">
                                    {generatedPassword}
                                </div>
                                <p className="text-xs text-red-600 mt-2">
                                    Please save this password securely. It will not be shown again.
                                </p>
                            </div>
                            <button
                                onClick={() => {
                                    setShowPassword(false);
                                    setGeneratedPassword(null);
                                }}
                                className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AdminUserManagementPage;