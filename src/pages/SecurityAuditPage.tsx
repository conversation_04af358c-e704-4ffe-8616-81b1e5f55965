import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, Eye, Download, RefreshCw, Calendar, Filter } from 'lucide-react';
import { api } from '../services/api';

interface AuditLogEntry {
    id: string;
    eventType: string;
    userId?: string;
    email?: string;
    ipAddress: string;
    userAgent?: string;
    resource?: string;
    action?: string;
    success: boolean;
    errorMessage?: string;
    createdAt: string;
}

interface SecurityAlert {
    id: string;
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    title: string;
    description: string;
    affectedUsers: string[];
    affectedIPs: string[];
    eventCount: number;
    timeWindow: string;
    status: string;
}

interface AuditFilters {
    timeRange?: string;
    eventTypes?: string[];
    userId?: string;
    email?: string;
    ipAddress?: string;
    success?: boolean;
    suspiciousOnly?: boolean;
    limit?: number;
}

const SecurityAuditPage: React.FC = () => {
    const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
    const [securityAlerts, setSecurityAlerts] = useState<SecurityAlert[]>([]);
    const [auditStats, setAuditStats] = useState<any>(null);
    const [complianceReport, setComplianceReport] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [filters, setFilters] = useState<AuditFilters>({
        timeRange: 'last_24h',
        limit: 100,
    });
    const [activeTab, setActiveTab] = useState<'logs' | 'alerts' | 'stats' | 'compliance'>('logs');

    useEffect(() => {
        loadAuditData();
    }, [filters]);

    const loadAuditData = async () => {
        setLoading(true);
        try {
            const [logsResponse, alertsResponse, statsResponse] = await Promise.all([
                api.getAuditLogs(filters),
                api.getSecurityAlerts(),
                api.getAuditStats('day'),
            ]);

            if (logsResponse.success && logsResponse.data) {
                setAuditLogs(logsResponse.data.events || []);
                setSecurityAlerts(logsResponse.data.securityAlerts || []);
            }

            if (alertsResponse.success && alertsResponse.data) {
                setSecurityAlerts(alertsResponse.data);
            }

            if (statsResponse.success && statsResponse.data) {
                setAuditStats(statsResponse.data);
            }
        } catch (error) {
            console.error('Failed to load audit data:', error);
        } finally {
            setLoading(false);
        }
    };

    const loadComplianceReport = async (timeRange: 'week' | 'month' | 'quarter' | 'year' = 'month') => {
        try {
            const response = await api.getSecurityReport(timeRange);
            if (response.success && response.data) {
                setComplianceReport(response.data);
            }
        } catch (error) {
            console.error('Failed to load compliance report:', error);
        }
    };

    const verifyIntegrity = async () => {
        try {
            const response = await api.verifyLogIntegrity();
            if (response.success && response.data) {
                alert(`Log integrity check completed. ${response.data.isValid ? 'No issues found' : `${response.data.issues.length} issues detected`}`);
            }
        } catch (error) {
            console.error('Failed to verify log integrity:', error);
        }
    };

    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'CRITICAL': return 'text-red-600 bg-red-100';
            case 'HIGH': return 'text-orange-600 bg-orange-100';
            case 'MEDIUM': return 'text-yellow-600 bg-yellow-100';
            case 'LOW': return 'text-blue-600 bg-blue-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const getEventTypeColor = (eventType: string, success: boolean) => {
        if (!success) return 'text-red-600 bg-red-100';
        if (eventType.includes('LOGIN')) return 'text-green-600 bg-green-100';
        if (eventType.includes('ADMIN')) return 'text-purple-600 bg-purple-100';
        if (eventType.includes('SESSION')) return 'text-blue-600 bg-blue-100';
        return 'text-gray-600 bg-gray-100';
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString();
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading audit data...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <Shield className="w-8 h-8 text-blue-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Security Audit</h1>
                        <p className="text-gray-600">Monitor security events and compliance</p>
                    </div>
                </div>
                <div className="flex space-x-2">
                    <button
                        onClick={verifyIntegrity}
                        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                        <Shield className="w-4 h-4 mr-2" />
                        Verify Integrity
                    </button>
                    <button
                        onClick={() => loadComplianceReport()}
                        className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                    >
                        <Download className="w-4 h-4 mr-2" />
                        Generate Report
                    </button>
                </div>
            </div>

            {/* Stats Cards */}
            {auditStats && (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <Eye className="w-8 h-8 text-blue-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Events</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.totalEvents}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <Shield className="w-8 h-8 text-green-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {Math.round((auditStats.successfulEvents / auditStats.totalEvents) * 100)}%
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <AlertTriangle className="w-8 h-8 text-orange-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Failed Events</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.failedEvents}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <Calendar className="w-8 h-8 text-purple-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Unique IPs</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.uniqueIPs}</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Security Alerts */}
            {securityAlerts.length > 0 && (
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                            <AlertTriangle className="w-5 h-5 text-red-600 mr-2" />
                            Active Security Alerts ({securityAlerts.length})
                        </h2>
                    </div>
                    <div className="p-6">
                        <div className="space-y-4">
                            {securityAlerts.map((alert) => (
                                <div key={alert.id} className="border border-red-200 rounded-lg p-4 bg-red-50">
                                    <div className="flex items-start justify-between">
                                        <div className="flex-1">
                                            <div className="flex items-center space-x-2">
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                                                    {alert.severity}
                                                </span>
                                                <span className="text-sm text-gray-600">{alert.type}</span>
                                            </div>
                                            <h3 className="text-lg font-semibold text-gray-900 mt-2">{alert.title}</h3>
                                            <p className="text-gray-600 mt-1">{alert.description}</p>
                                            <div className="mt-2 text-sm text-gray-500">
                                                <span>Events: {alert.eventCount}</span>
                                                <span className="mx-2">•</span>
                                                <span>Time Window: {alert.timeWindow}</span>
                                                {alert.affectedIPs.length > 0 && (
                                                    <>
                                                        <span className="mx-2">•</span>
                                                        <span>IPs: {alert.affectedIPs.join(', ')}</span>
                                                    </>
                                                )}
                                            </div>
                                        </div>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${alert.status === 'ACTIVE' ? 'text-red-600 bg-red-100' : 'text-gray-600 bg-gray-100'
                                            }`}>
                                            {alert.status}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Tabs */}
            <div className="bg-white rounded-lg shadow">
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6">
                        {[
                            { id: 'logs', label: 'Audit Logs', icon: Eye },
                            { id: 'alerts', label: 'Security Alerts', icon: AlertTriangle },
                            { id: 'stats', label: 'Statistics', icon: Calendar },
                            { id: 'compliance', label: 'Compliance', icon: Shield },
                        ].map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id as any)}
                                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                >
                                    <Icon className="w-4 h-4 mr-2" />
                                    {tab.label}
                                </button>
                            );
                        })}
                    </nav>
                </div>

                <div className="p-6">
                    {activeTab === 'logs' && (
                        <div className="space-y-4">
                            {/* Filters */}
                            <div className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                                <Filter className="w-4 h-4 text-gray-600" />
                                <select
                                    value={filters.timeRange}
                                    onChange={(e) => setFilters({ ...filters, timeRange: e.target.value })}
                                    className="border border-gray-300 rounded-md px-3 py-1 text-sm"
                                >
                                    <option value="last_hour">Last Hour</option>
                                    <option value="last_24h">Last 24 Hours</option>
                                    <option value="last_week">Last Week</option>
                                    <option value="last_month">Last Month</option>
                                </select>
                                <label className="flex items-center">
                                    <input
                                        type="checkbox"
                                        checked={filters.suspiciousOnly || false}
                                        onChange={(e) => setFilters({ ...filters, suspiciousOnly: e.target.checked })}
                                        className="mr-2"
                                    />
                                    <span className="text-sm text-gray-600">Suspicious Only</span>
                                </label>
                                <button
                                    onClick={loadAuditData}
                                    className="flex items-center px-3 py-1 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                                >
                                    <RefreshCw className="w-4 h-4 mr-1" />
                                    Refresh
                                </button>
                            </div>

                            {/* Audit Logs Table */}
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Event
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                User
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                IP Address
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Time
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {auditLogs.map((log) => (
                                            <tr key={log.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="flex items-center">
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(log.eventType, log.success)}`}>
                                                            {log.eventType.replace(/_/g, ' ')}
                                                        </span>
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{log.email || 'System'}</div>
                                                    {log.userId && (
                                                        <div className="text-sm text-gray-500">{log.userId}</div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {log.ipAddress}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${log.success ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
                                                        }`}>
                                                        {log.success ? 'Success' : 'Failed'}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {formatDate(log.createdAt)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {activeTab === 'compliance' && complianceReport && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-blue-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-blue-900">Compliance Score</h3>
                                    <p className="text-3xl font-bold text-blue-600 mt-2">{complianceReport.complianceScore}/100</p>
                                    <p className="text-sm text-blue-700 mt-1">Risk Level: {complianceReport.riskAssessment.level}</p>
                                </div>
                                <div className="bg-green-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-green-900">Authentication Events</h3>
                                    <p className="text-3xl font-bold text-green-600 mt-2">{complianceReport.securityMetrics.authenticationEvents}</p>
                                    <p className="text-sm text-green-700 mt-1">Success Rate: {Math.round((complianceReport.securityMetrics.successfulLogins / complianceReport.securityMetrics.authenticationEvents) * 100)}%</p>
                                </div>
                                <div className="bg-red-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-red-900">Security Violations</h3>
                                    <p className="text-3xl font-bold text-red-600 mt-2">{complianceReport.securityMetrics.securityViolations}</p>
                                    <p className="text-sm text-red-700 mt-1">Account Lockouts: {complianceReport.securityMetrics.accountLockouts}</p>
                                </div>
                            </div>

                            {complianceReport.recommendations.length > 0 && (
                                <div className="bg-yellow-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-yellow-900 mb-4">Recommendations</h3>
                                    <ul className="space-y-2">
                                        {complianceReport.recommendations.map((recommendation: string, index: number) => (
                                            <li key={index} className="flex items-start">
                                                <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                                                <span className="text-yellow-800">{recommendation}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default SecurityAuditPage;