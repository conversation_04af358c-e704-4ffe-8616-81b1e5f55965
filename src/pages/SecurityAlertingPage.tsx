import React, { useState, useEffect } from 'react';
import {
    <PERSON>ert<PERSON><PERSON>gle,
    Shield,
    Bell,
    CheckCircle,
    XCircle,
    Clock,
    Settings,
    Download,
    Refresh<PERSON><PERSON>,
    Eye,
    EyeOff,
    Play,
    Pause,
    Activity,
    TrendingUp,
    Users,
    Lock,
} from 'lucide-react';
import { api } from '../services/api';

interface SecurityAlert {
    id: string;
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    title: string;
    description: string;
    status: 'ACTIVE' | 'INVESTIGATING' | 'RESOLVED' | 'FALSE_POSITIVE' | 'SUPPRESSED';
    triggeredAt: Date;
    resolvedAt?: Date;
    affectedResources: string[];
    eventCount: number;
    metadata: Record<string, any>;
    automatedResponse?: {
        type: string;
        executedAt?: Date;
        success?: boolean;
        error?: string;
    };
}

interface AlertRule {
    id: string;
    name: string;
    description: string;
    alertType: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    isEnabled: boolean;
    threshold: {
        metric: string;
        operator: string;
        value: number;
        timeWindow: number;
        evaluationInterval: number;
    };
    automatedResponse?: {
        type: string;
        parameters: Record<string, any>;
    };
}

interface SecurityHealthCheck {
    overall: 'HEALTHY' | 'WARNING' | 'CRITICAL';
    checks: Array<{
        name: string;
        status: 'PASS' | 'WARN' | 'FAIL';
        message: string;
        details?: any;
    }>;
    recommendations: string[];
}

const SecurityAlertingPage: React.FC = () => {
    const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
    const [alertRules, setAlertRules] = useState<AlertRule[]>([]);
    const [healthCheck, setHealthCheck] = useState<SecurityHealthCheck | null>(null);
    const [loading, setLoading] = useState(true);
    const [activeTab, setActiveTab] = useState<'alerts' | 'rules' | 'health' | 'reports'>('alerts');
    const [selectedAlert, setSelectedAlert] = useState<SecurityAlert | null>(null);
    const [autoRefresh, setAutoRefresh] = useState(true);

    useEffect(() => {
        loadAlertingData();

        if (autoRefresh) {
            const interval = setInterval(loadAlertingData, 30000); // Refresh every 30 seconds
            return () => clearInterval(interval);
        }
    }, [autoRefresh]);

    const loadAlertingData = async () => {
        try {
            const [alertsResponse, rulesResponse, healthResponse] = await Promise.all([
                api.getSecurityAlerts(),
                api.getAlertRules(),
                api.getSecurityHealthCheck(),
            ]);

            if (alertsResponse.success && alertsResponse.data) {
                setAlerts(alertsResponse.data);
            }

            if (rulesResponse.success && rulesResponse.data) {
                setAlertRules(rulesResponse.data);
            }

            if (healthResponse.success && healthResponse.data) {
                setHealthCheck(healthResponse.data);
            }
        } catch (error) {
            console.error('Failed to load alerting data:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleResolveAlert = async (alertId: string, reason?: string) => {
        try {
            const response = await api.resolveSecurityAlert(alertId, reason);
            if (response.success) {
                await loadAlertingData();
            }
        } catch (error) {
            console.error('Failed to resolve alert:', error);
        }
    };

    const handleSuppressAlert = async (alertId: string, duration: number, reason: string) => {
        try {
            const response = await api.suppressSecurityAlert(alertId, duration, reason);
            if (response.success) {
                await loadAlertingData();
            }
        } catch (error) {
            console.error('Failed to suppress alert:', error);
        }
    };

    const handleToggleRule = async (ruleId: string, enabled: boolean) => {
        try {
            const response = await api.updateAlertRule(ruleId, { isEnabled: enabled });
            if (response.success) {
                await loadAlertingData();
            }
        } catch (error) {
            console.error('Failed to toggle alert rule:', error);
        }
    };

    const generateSecurityReport = async (type: 'DAILY' | 'WEEKLY' | 'MONTHLY') => {
        try {
            const response = await api.generateSecurityReport(type);
            if (response.success && response.data) {
                // Download the report
                const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `security-report-${type.toLowerCase()}-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }
        } catch (error) {
            console.error('Failed to generate security report:', error);
        }
    };

    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'CRITICAL': return 'text-red-600 bg-red-100 border-red-200';
            case 'HIGH': return 'text-orange-600 bg-orange-100 border-orange-200';
            case 'MEDIUM': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
            case 'LOW': return 'text-blue-600 bg-blue-100 border-blue-200';
            default: return 'text-gray-600 bg-gray-100 border-gray-200';
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'ACTIVE': return 'text-red-600 bg-red-100';
            case 'INVESTIGATING': return 'text-yellow-600 bg-yellow-100';
            case 'RESOLVED': return 'text-green-600 bg-green-100';
            case 'FALSE_POSITIVE': return 'text-gray-600 bg-gray-100';
            case 'SUPPRESSED': return 'text-purple-600 bg-purple-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const getHealthColor = (status: string) => {
        switch (status) {
            case 'HEALTHY': return 'text-green-600 bg-green-100';
            case 'WARNING': return 'text-yellow-600 bg-yellow-100';
            case 'CRITICAL': return 'text-red-600 bg-red-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    const formatDate = (date: Date | string) => {
        return new Date(date).toLocaleString();
    };

    const formatTimeAgo = (date: Date | string) => {
        const now = new Date();
        const past = new Date(date);
        const diffMs = now.getTime() - past.getTime();
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        return `${diffDays}d ago`;
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading security alerting data...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <Bell className="w-8 h-8 text-blue-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Security Alerting</h1>
                        <p className="text-gray-600">Automated threat detection and response</p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <button
                        onClick={() => setAutoRefresh(!autoRefresh)}
                        className={`flex items-center px-3 py-2 rounded-md text-sm ${autoRefresh
                                ? 'bg-green-100 text-green-700 border border-green-200'
                                : 'bg-gray-100 text-gray-700 border border-gray-200'
                            }`}
                    >
                        <Activity className="w-4 h-4 mr-1" />
                        Auto Refresh
                    </button>
                    <button
                        onClick={loadAlertingData}
                        className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                    >
                        <RefreshCw className="w-4 h-4 mr-1" />
                        Refresh
                    </button>
                </div>
            </div>

            {/* Health Status */}
            {healthCheck && (
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h2 className="text-lg font-semibold text-gray-900">System Health</h2>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getHealthColor(healthCheck.overall)}`}>
                            {healthCheck.overall}
                        </span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {healthCheck.checks.map((check, index) => (
                            <div key={index} className="border rounded-lg p-3">
                                <div className="flex items-center justify-between mb-2">
                                    <span className="text-sm font-medium text-gray-900">{check.name}</span>
                                    {check.status === 'PASS' && <CheckCircle className="w-4 h-4 text-green-600" />}
                                    {check.status === 'WARN' && <AlertTriangle className="w-4 h-4 text-yellow-600" />}
                                    {check.status === 'FAIL' && <XCircle className="w-4 h-4 text-red-600" />}
                                </div>
                                <p className="text-xs text-gray-600">{check.message}</p>
                            </div>
                        ))}
                    </div>
                </div>
            )}

            {/* Alert Summary */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-white p-4 rounded-lg shadow border-l-4 border-red-500">
                    <div className="flex items-center">
                        <AlertTriangle className="w-6 h-6 text-red-600" />
                        <div className="ml-3">
                            <p className="text-sm font-medium text-gray-600">Active Alerts</p>
                            <p className="text-xl font-bold text-gray-900">
                                {alerts.filter(a => a.status === 'ACTIVE').length}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow border-l-4 border-orange-500">
                    <div className="flex items-center">
                        <Clock className="w-6 h-6 text-orange-600" />
                        <div className="ml-3">
                            <p className="text-sm font-medium text-gray-600">Critical Alerts</p>
                            <p className="text-xl font-bold text-gray-900">
                                {alerts.filter(a => a.severity === 'CRITICAL' && a.status === 'ACTIVE').length}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
                    <div className="flex items-center">
                        <Shield className="w-6 h-6 text-green-600" />
                        <div className="ml-3">
                            <p className="text-sm font-medium text-gray-600">Active Rules</p>
                            <p className="text-xl font-bold text-gray-900">
                                {alertRules.filter(r => r.isEnabled).length}
                            </p>
                        </div>
                    </div>
                </div>
                <div className="bg-white p-4 rounded-lg shadow border-l-4 border-blue-500">
                    <div className="flex items-center">
                        <TrendingUp className="w-6 h-6 text-blue-600" />
                        <div className="ml-3">
                            <p className="text-sm font-medium text-gray-600">Resolved Today</p>
                            <p className="text-xl font-bold text-gray-900">
                                {alerts.filter(a => a.status === 'RESOLVED' &&
                                    new Date(a.resolvedAt || 0).toDateString() === new Date().toDateString()).length}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            {/* Tabs */}
            <div className="bg-white rounded-lg shadow">
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6">
                        {[
                            { id: 'alerts', label: 'Active Alerts', icon: AlertTriangle },
                            { id: 'rules', label: 'Alert Rules', icon: Settings },
                            { id: 'health', label: 'Health Checks', icon: Activity },
                            { id: 'reports', label: 'Reports', icon: Download },
                        ].map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id as any)}
                                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                >
                                    <Icon className="w-4 h-4 mr-2" />
                                    {tab.label}
                                </button>
                            );
                        })}
                    </nav>
                </div>

                <div className="p-6">
                    {activeTab === 'alerts' && (
                        <div className="space-y-4">
                            {alerts.length === 0 ? (
                                <div className="text-center py-8">
                                    <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Alerts</h3>
                                    <p className="text-gray-600">All security systems are operating normally.</p>
                                </div>
                            ) : (
                                <div className="space-y-3">
                                    {alerts.map((alert) => (
                                        <div key={alert.id} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                                            <div className="flex items-start justify-between">
                                                <div className="flex-1">
                                                    <div className="flex items-center space-x-2 mb-2">
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(alert.severity)}`}>
                                                            {alert.severity}
                                                        </span>
                                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(alert.status)}`}>
                                                            {alert.status}
                                                        </span>
                                                        <span className="text-xs text-gray-500">{alert.type}</span>
                                                    </div>
                                                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{alert.title}</h3>
                                                    <p className="text-gray-700 mb-2">{alert.description}</p>
                                                    <div className="text-sm text-gray-600">
                                                        <span>Events: {alert.eventCount}</span>
                                                        <span className="mx-2">•</span>
                                                        <span>Triggered: {formatTimeAgo(alert.triggeredAt)}</span>
                                                        {alert.automatedResponse && (
                                                            <>
                                                                <span className="mx-2">•</span>
                                                                <span>Auto Response: {alert.automatedResponse.type}</span>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                                <div className="flex space-x-2">
                                                    {alert.status === 'ACTIVE' && (
                                                        <>
                                                            <button
                                                                onClick={() => handleResolveAlert(alert.id)}
                                                                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                                                            >
                                                                Resolve
                                                            </button>
                                                            <button
                                                                onClick={() => handleSuppressAlert(alert.id, 60, 'Manual suppression')}
                                                                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
                                                            >
                                                                Suppress
                                                            </button>
                                                        </>
                                                    )}
                                                    <button
                                                        onClick={() => setSelectedAlert(alert)}
                                                        className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                                                    >
                                                        Details
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'rules' && (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Rule
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Type
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Severity
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Threshold
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Actions
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {alertRules.map((rule) => (
                                            <tr key={rule.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900 font-medium">{rule.name}</div>
                                                    <div className="text-sm text-gray-500">{rule.description}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {rule.alertType.replace(/_/g, ' ')}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(rule.severity)}`}>
                                                        {rule.severity}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {rule.threshold.metric} {rule.threshold.operator} {rule.threshold.value}
                                                    <div className="text-xs text-gray-500">
                                                        {rule.threshold.timeWindow}m window
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <button
                                                        onClick={() => handleToggleRule(rule.id, !rule.isEnabled)}
                                                        className={`flex items-center px-2 py-1 rounded-full text-xs font-medium ${rule.isEnabled
                                                                ? 'bg-green-100 text-green-800'
                                                                : 'bg-gray-100 text-gray-800'
                                                            }`}
                                                    >
                                                        {rule.isEnabled ? <Play className="w-3 h-3 mr-1" /> : <Pause className="w-3 h-3 mr-1" />}
                                                        {rule.isEnabled ? 'Enabled' : 'Disabled'}
                                                    </button>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    <button className="text-blue-600 hover:text-blue-900">
                                                        <Settings className="w-4 h-4" />
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {activeTab === 'health' && healthCheck && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {healthCheck.checks.map((check, index) => (
                                    <div key={index} className="border rounded-lg p-4">
                                        <div className="flex items-center justify-between mb-3">
                                            <h3 className="text-lg font-semibold text-gray-900">{check.name}</h3>
                                            {check.status === 'PASS' && <CheckCircle className="w-6 h-6 text-green-600" />}
                                            {check.status === 'WARN' && <AlertTriangle className="w-6 h-6 text-yellow-600" />}
                                            {check.status === 'FAIL' && <XCircle className="w-6 h-6 text-red-600" />}
                                        </div>
                                        <p className="text-gray-700 mb-2">{check.message}</p>
                                        {check.details && (
                                            <div className="text-sm text-gray-600">
                                                <pre className="bg-gray-50 p-2 rounded text-xs overflow-x-auto">
                                                    {JSON.stringify(check.details, null, 2)}
                                                </pre>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>

                            {healthCheck.recommendations.length > 0 && (
                                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <h3 className="text-lg font-semibold text-yellow-900 mb-3">Recommendations</h3>
                                    <ul className="space-y-2">
                                        {healthCheck.recommendations.map((recommendation, index) => (
                                            <li key={index} className="flex items-start">
                                                <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" />
                                                <span className="text-yellow-800">{recommendation}</span>
                                            </li>
                                        ))}
                                    </ul>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'reports' && (
                        <div className="space-y-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="border rounded-lg p-6 text-center">
                                    <Download className="w-8 h-8 text-blue-600 mx-auto mb-3" />
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Daily Report</h3>
                                    <p className="text-gray-600 mb-4">Generate a comprehensive daily security report</p>
                                    <button
                                        onClick={() => generateSecurityReport('DAILY')}
                                        className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                                    >
                                        Generate
                                    </button>
                                </div>
                                <div className="border rounded-lg p-6 text-center">
                                    <Download className="w-8 h-8 text-green-600 mx-auto mb-3" />
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Weekly Report</h3>
                                    <p className="text-gray-600 mb-4">Generate a weekly security summary report</p>
                                    <button
                                        onClick={() => generateSecurityReport('WEEKLY')}
                                        className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                                    >
                                        Generate
                                    </button>
                                </div>
                                <div className="border rounded-lg p-6 text-center">
                                    <Download className="w-8 h-8 text-purple-600 mx-auto mb-3" />
                                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Monthly Report</h3>
                                    <p className="text-gray-600 mb-4">Generate a monthly security analysis report</p>
                                    <button
                                        onClick={() => generateSecurityReport('MONTHLY')}
                                        className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700"
                                    >
                                        Generate
                                    </button>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Alert Details Modal */}
            {selectedAlert && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-xl font-bold text-gray-900">Alert Details</h2>
                            <button
                                onClick={() => setSelectedAlert(null)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <XCircle className="w-6 h-6" />
                            </button>
                        </div>
                        <div className="space-y-4">
                            <div>
                                <h3 className="font-semibold text-gray-900">{selectedAlert.title}</h3>
                                <p className="text-gray-600">{selectedAlert.description}</p>
                            </div>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Severity:</span>
                                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getSeverityColor(selectedAlert.severity)}`}>
                                        {selectedAlert.severity}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Status:</span>
                                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedAlert.status)}`}>
                                        {selectedAlert.status}
                                    </span>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Event Count:</span>
                                    <span className="ml-2 text-sm text-gray-900">{selectedAlert.eventCount}</span>
                                </div>
                                <div>
                                    <span className="text-sm font-medium text-gray-500">Triggered:</span>
                                    <span className="ml-2 text-sm text-gray-900">{formatDate(selectedAlert.triggeredAt)}</span>
                                </div>
                            </div>
                            {selectedAlert.automatedResponse && (
                                <div>
                                    <h4 className="font-semibold text-gray-900 mb-2">Automated Response</h4>
                                    <div className="bg-gray-50 p-3 rounded">
                                        <div className="text-sm">
                                            <div><strong>Type:</strong> {selectedAlert.automatedResponse.type}</div>
                                            {selectedAlert.automatedResponse.executedAt && (
                                                <div><strong>Executed:</strong> {formatDate(selectedAlert.automatedResponse.executedAt)}</div>
                                            )}
                                            <div><strong>Success:</strong> {selectedAlert.automatedResponse.success ? 'Yes' : 'No'}</div>
                                            {selectedAlert.automatedResponse.error && (
                                                <div><strong>Error:</strong> {selectedAlert.automatedResponse.error}</div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                            <div>
                                <h4 className="font-semibold text-gray-900 mb-2">Metadata</h4>
                                <pre className="bg-gray-50 p-3 rounded text-xs overflow-x-auto">
                                    {JSON.stringify(selectedAlert.metadata, null, 2)}
                                </pre>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default SecurityAlertingPage;