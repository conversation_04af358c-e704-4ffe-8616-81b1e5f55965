import React, { useState, useEffect } from 'react';
import {
    Shield,
    AlertTriangle,
    Activity,
    Users,
    Clock,
    TrendingUp,
    TrendingDown,
    Eye,
    Lock,
    Wifi,
    RefreshCw,
    Calendar,
    Filter,
    Download,
    Bell,
    CheckCircle,
    XCircle,
    AlertCircle,
} from 'lucide-react';
import { api } from '../services/api';

interface SecurityMetrics {
    realTimeStats: {
        activeUsers: number;
        activeSessions: number;
        failedLoginsLastHour: number;
        securityViolationsLastHour: number;
        suspiciousActivitiesLastHour: number;
        lastUpdated: Date;
    };
    authenticationMetrics: {
        totalLogins: number;
        successfulLogins: number;
        failedLogins: number;
        successRate: number;
        uniqueUsers: number;
        averageSessionDuration: number;
        timeRange: string;
    };
    sessionMetrics: {
        totalSessions: number;
        activeSessions: number;
        expiredSessions: number;
        invalidatedSessions: number;
        averageSessionsPerUser: number;
        sessionAnomalies: number;
        timeRange: string;
    };
    securityViolations: {
        rateLimitViolations: number;
        unauthorizedAccess: number;
        csrfViolations: number;
        inputValidationFailures: number;
        suspiciousIpActivity: number;
        timeRange: string;
    };
    topRisks: Array<{
        type: string;
        description: string;
        count: number;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
        lastOccurrence: Date;
    }>;
}

interface SecurityEvent {
    id: string;
    eventType: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
    timestamp: Date;
    ipAddress: string;
    userId?: string;
    email?: string;
}

interface SecurityAlert {
    id: string;
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    title: string;
    description: string;
    count: number;
    firstSeen: Date;
    lastSeen: Date;
    status: 'ACTIVE' | 'INVESTIGATING' | 'RESOLVED';
}

interface FailedLoginAttempt {
    id: string;
    email: string;
    ipAddress: string;
    userAgent?: string;
    timestamp: Date;
    reason: string;
    isBlocked: boolean;
    attemptCount: number;
}

interface SessionAnomaly {
    id: string;
    userId: string;
    sessionId: string;
    anomalyType: string;
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    timestamp: Date;
    ipAddress: string;
    previousIp?: string;
    riskScore: number;
}

const SecurityMonitoringPage: React.FC = () => {
    const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
    const [recentEvents, setRecentEvents] = useState<SecurityEvent[]>([]);
    const [alerts, setAlerts] = useState<SecurityAlert[]>([]);
    const [failedLogins, setFailedLogins] = useState<FailedLoginAttempt[]>([]);
    const [sessionAnomalies, setSessionAnomalies] = useState<SessionAnomaly[]>([]);
    const [loading, setLoading] = useState(true);
    const [timeRange, setTimeRange] = useState<'hour' | 'day' | 'week' | 'month'>('day');
    const [activeTab, setActiveTab] = useState<'overview' | 'events' | 'alerts' | 'logins' | 'sessions'>('overview');
    const [autoRefresh, setAutoRefresh] = useState(true);

    useEffect(() => {
        loadSecurityData();

        if (autoRefresh) {
            const interval = setInterval(loadSecurityData, 30000); // Refresh every 30 seconds
            return () => clearInterval(interval);
        }
    }, [timeRange, autoRefresh]);

    const loadSecurityData = async () => {
        try {
            const [dashboardResponse, failedLoginsResponse, anomaliesResponse] = await Promise.all([
                api.getSecurityDashboard(timeRange),
                api.getFailedLoginAttempts(timeRange === 'hour' ? 'hour' : 'day'),
                api.getSessionAnomalies(timeRange === 'hour' ? 'hour' : 'day'),
            ]);

            if (dashboardResponse.success && dashboardResponse.data) {
                const data = dashboardResponse.data;
                setMetrics(data.metrics);
                setRecentEvents(data.recentEvents || []);
                setAlerts(data.alerts || []);
            }

            if (failedLoginsResponse.success && failedLoginsResponse.data) {
                setFailedLogins(failedLoginsResponse.data);
            }

            if (anomaliesResponse.success && anomaliesResponse.data) {
                setSessionAnomalies(anomaliesResponse.data);
            }
        } catch (error) {
            console.error('Failed to load security data:', error);
        } finally {
            setLoading(false);
        }
    };

    const getSeverityColor = (severity: string) => {
        switch (severity) {
            case 'CRITICAL': return 'text-red-600 bg-red-100 border-red-200';
            case 'HIGH': return 'text-orange-600 bg-orange-100 border-orange-200';
            case 'MEDIUM': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
            case 'LOW': return 'text-blue-600 bg-blue-100 border-blue-200';
            default: return 'text-gray-600 bg-gray-100 border-gray-200';
        }
    };

    const getSeverityIcon = (severity: string) => {
        switch (severity) {
            case 'CRITICAL': return <XCircle className="w-4 h-4" />;
            case 'HIGH': return <AlertTriangle className="w-4 h-4" />;
            case 'MEDIUM': return <AlertCircle className="w-4 h-4" />;
            case 'LOW': return <CheckCircle className="w-4 h-4" />;
            default: return <Eye className="w-4 h-4" />;
        }
    };

    const formatDate = (date: Date | string) => {
        return new Date(date).toLocaleString();
    };

    const formatTimeAgo = (date: Date | string) => {
        const now = new Date();
        const past = new Date(date);
        const diffMs = now.getTime() - past.getTime();
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMins / 60);
        const diffDays = Math.floor(diffHours / 24);

        if (diffMins < 1) return 'Just now';
        if (diffMins < 60) return `${diffMins}m ago`;
        if (diffHours < 24) return `${diffHours}h ago`;
        return `${diffDays}d ago`;
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading security monitoring data...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <Shield className="w-8 h-8 text-blue-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Security Monitoring</h1>
                        <p className="text-gray-600">Real-time security metrics and threat detection</p>
                    </div>
                </div>
                <div className="flex items-center space-x-2">
                    <select
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value as any)}
                        className="border border-gray-300 rounded-md px-3 py-2 text-sm"
                    >
                        <option value="hour">Last Hour</option>
                        <option value="day">Last 24 Hours</option>
                        <option value="week">Last Week</option>
                        <option value="month">Last Month</option>
                    </select>
                    <button
                        onClick={() => setAutoRefresh(!autoRefresh)}
                        className={`flex items-center px-3 py-2 rounded-md text-sm ${autoRefresh
                                ? 'bg-green-100 text-green-700 border border-green-200'
                                : 'bg-gray-100 text-gray-700 border border-gray-200'
                            }`}
                    >
                        <Activity className="w-4 h-4 mr-1" />
                        Auto Refresh
                    </button>
                    <button
                        onClick={loadSecurityData}
                        className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700"
                    >
                        <RefreshCw className="w-4 h-4 mr-1" />
                        Refresh
                    </button>
                </div>
            </div>

            {/* Real-time Stats */}
            {metrics && (
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div className="bg-white p-4 rounded-lg shadow border-l-4 border-blue-500">
                        <div className="flex items-center">
                            <Users className="w-6 h-6 text-blue-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Active Users</p>
                                <p className="text-xl font-bold text-gray-900">{metrics.realTimeStats.activeUsers}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
                        <div className="flex items-center">
                            <Wifi className="w-6 h-6 text-green-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Active Sessions</p>
                                <p className="text-xl font-bold text-gray-900">{metrics.realTimeStats.activeSessions}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border-l-4 border-red-500">
                        <div className="flex items-center">
                            <Lock className="w-6 h-6 text-red-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Failed Logins (1h)</p>
                                <p className="text-xl font-bold text-gray-900">{metrics.realTimeStats.failedLoginsLastHour}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border-l-4 border-orange-500">
                        <div className="flex items-center">
                            <AlertTriangle className="w-6 h-6 text-orange-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Violations (1h)</p>
                                <p className="text-xl font-bold text-gray-900">{metrics.realTimeStats.securityViolationsLastHour}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow border-l-4 border-purple-500">
                        <div className="flex items-center">
                            <Eye className="w-6 h-6 text-purple-600" />
                            <div className="ml-3">
                                <p className="text-sm font-medium text-gray-600">Suspicious (1h)</p>
                                <p className="text-xl font-bold text-gray-900">{metrics.realTimeStats.suspiciousActivitiesLastHour}</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Active Alerts */}
            {alerts.length > 0 && (
                <div className="bg-white rounded-lg shadow">
                    <div className="px-6 py-4 border-b border-gray-200">
                        <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                            <Bell className="w-5 h-5 text-red-600 mr-2" />
                            Active Security Alerts ({alerts.length})
                        </h2>
                    </div>
                    <div className="p-6">
                        <div className="space-y-3">
                            {alerts.slice(0, 3).map((alert) => (
                                <div key={alert.id} className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}>
                                    <div className="flex items-start justify-between">
                                        <div className="flex items-start space-x-3">
                                            {getSeverityIcon(alert.severity)}
                                            <div>
                                                <h3 className="font-semibold">{alert.title}</h3>
                                                <p className="text-sm mt-1">{alert.description}</p>
                                                <div className="text-xs mt-2 opacity-75">
                                                    {alert.count} events • Last seen {formatTimeAgo(alert.lastSeen)}
                                                </div>
                                            </div>
                                        </div>
                                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${alert.status === 'ACTIVE' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                                            }`}>
                                            {alert.status}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            )}

            {/* Tabs */}
            <div className="bg-white rounded-lg shadow">
                <div className="border-b border-gray-200">
                    <nav className="flex space-x-8 px-6">
                        {[
                            { id: 'overview', label: 'Overview', icon: Activity },
                            { id: 'events', label: 'Recent Events', icon: Eye },
                            { id: 'alerts', label: 'Security Alerts', icon: AlertTriangle },
                            { id: 'logins', label: 'Failed Logins', icon: Lock },
                            { id: 'sessions', label: 'Session Anomalies', icon: Wifi },
                        ].map((tab) => {
                            const Icon = tab.icon;
                            return (
                                <button
                                    key={tab.id}
                                    onClick={() => setActiveTab(tab.id as any)}
                                    className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm ${activeTab === tab.id
                                            ? 'border-blue-500 text-blue-600'
                                            : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                                        }`}
                                >
                                    <Icon className="w-4 h-4 mr-2" />
                                    {tab.label}
                                </button>
                            );
                        })}
                    </nav>
                </div>

                <div className="p-6">
                    {activeTab === 'overview' && metrics && (
                        <div className="space-y-6">
                            {/* Authentication Metrics */}
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-blue-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-blue-900 mb-4">Authentication</h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-blue-700">Total Logins:</span>
                                            <span className="font-semibold text-blue-900">{metrics.authenticationMetrics.totalLogins}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-blue-700">Success Rate:</span>
                                            <span className="font-semibold text-blue-900">{metrics.authenticationMetrics.successRate}%</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-blue-700">Unique Users:</span>
                                            <span className="font-semibold text-blue-900">{metrics.authenticationMetrics.uniqueUsers}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-blue-700">Avg Session:</span>
                                            <span className="font-semibold text-blue-900">{metrics.authenticationMetrics.averageSessionDuration}m</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-green-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-green-900 mb-4">Sessions</h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-green-700">Total Sessions:</span>
                                            <span className="font-semibold text-green-900">{metrics.sessionMetrics.totalSessions}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-green-700">Currently Active:</span>
                                            <span className="font-semibold text-green-900">{metrics.sessionMetrics.activeSessions}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-green-700">Anomalies:</span>
                                            <span className="font-semibold text-green-900">{metrics.sessionMetrics.sessionAnomalies}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-green-700">Avg per User:</span>
                                            <span className="font-semibold text-green-900">{metrics.sessionMetrics.averageSessionsPerUser}</span>
                                        </div>
                                    </div>
                                </div>

                                <div className="bg-red-50 p-6 rounded-lg">
                                    <h3 className="text-lg font-semibold text-red-900 mb-4">Security Violations</h3>
                                    <div className="space-y-2">
                                        <div className="flex justify-between">
                                            <span className="text-red-700">Rate Limits:</span>
                                            <span className="font-semibold text-red-900">{metrics.securityViolations.rateLimitViolations}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-red-700">Unauthorized:</span>
                                            <span className="font-semibold text-red-900">{metrics.securityViolations.unauthorizedAccess}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-red-700">CSRF Violations:</span>
                                            <span className="font-semibold text-red-900">{metrics.securityViolations.csrfViolations}</span>
                                        </div>
                                        <div className="flex justify-between">
                                            <span className="text-red-700">Suspicious IPs:</span>
                                            <span className="font-semibold text-red-900">{metrics.securityViolations.suspiciousIpActivity}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Top Risks */}
                            {metrics.topRisks.length > 0 && (
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Security Risks</h3>
                                    <div className="space-y-3">
                                        {metrics.topRisks.slice(0, 5).map((risk, index) => (
                                            <div key={index} className={`border rounded-lg p-4 ${getSeverityColor(risk.severity)}`}>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-3">
                                                        {getSeverityIcon(risk.severity)}
                                                        <div>
                                                            <h4 className="font-semibold">{risk.description}</h4>
                                                            <p className="text-sm opacity-75">{risk.type.replace(/_/g, ' ')}</p>
                                                        </div>
                                                    </div>
                                                    <div className="text-right">
                                                        <div className="font-semibold">{risk.count} events</div>
                                                        <div className="text-xs opacity-75">{formatTimeAgo(risk.lastOccurrence)}</div>
                                                    </div>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'events' && (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Event
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                User
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                IP Address
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Severity
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Time
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {recentEvents.map((event) => (
                                            <tr key={event.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{event.description}</div>
                                                    <div className="text-sm text-gray-500">{event.eventType.replace(/_/g, ' ')}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{event.email || 'System'}</div>
                                                    {event.userId && (
                                                        <div className="text-sm text-gray-500">{event.userId}</div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {event.ipAddress}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                                                        {getSeverityIcon(event.severity)}
                                                        <span className="ml-1">{event.severity}</span>
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {formatTimeAgo(event.timestamp)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {activeTab === 'logins' && (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Email
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                IP Address
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Attempts
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Status
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Time
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {failedLogins.map((attempt) => (
                                            <tr key={attempt.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{attempt.email}</div>
                                                    <div className="text-sm text-gray-500">{attempt.reason}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                    {attempt.ipAddress}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${attempt.attemptCount > 3 ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'
                                                        }`}>
                                                        {attempt.attemptCount}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${attempt.isBlocked ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                                                        }`}>
                                                        {attempt.isBlocked ? 'Blocked' : 'Active'}
                                                    </span>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {formatTimeAgo(attempt.timestamp)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}

                    {activeTab === 'sessions' && (
                        <div className="space-y-4">
                            <div className="overflow-x-auto">
                                <table className="min-w-full divide-y divide-gray-200">
                                    <thead className="bg-gray-50">
                                        <tr>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Anomaly Type
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                User
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                IP Address
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Risk Score
                                            </th>
                                            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Time
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="bg-white divide-y divide-gray-200">
                                        {sessionAnomalies.map((anomaly) => (
                                            <tr key={anomaly.id} className="hover:bg-gray-50">
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{anomaly.anomalyType.replace(/_/g, ' ')}</div>
                                                    <div className="text-sm text-gray-500">{anomaly.description}</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{anomaly.userId}</div>
                                                    <div className="text-sm text-gray-500">Session: {anomaly.sessionId.slice(0, 8)}...</div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className="text-sm text-gray-900">{anomaly.ipAddress}</div>
                                                    {anomaly.previousIp && (
                                                        <div className="text-sm text-gray-500">Previous: {anomaly.previousIp}</div>
                                                    )}
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap">
                                                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${anomaly.riskScore >= 80 ? 'bg-red-100 text-red-800' :
                                                            anomaly.riskScore >= 60 ? 'bg-orange-100 text-orange-800' :
                                                                anomaly.riskScore >= 40 ? 'bg-yellow-100 text-yellow-800' :
                                                                    'bg-green-100 text-green-800'
                                                        }`}>
                                                        {anomaly.riskScore}
                                                    </div>
                                                </td>
                                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                    {formatTimeAgo(anomaly.timestamp)}
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default SecurityMonitoringPage;