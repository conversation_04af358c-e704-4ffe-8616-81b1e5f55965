import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { api } from '../services/api';
import { formatDate, formatDuration, formatNumber } from '../utils/formatters';
import {
  PlayIcon,
  PauseIcon,
  StopIcon,
  TrashIcon,
  ArrowPathIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  QueueListIcon,
  ChartBarIcon,
  XMarkIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';

interface QueueStats {
  waiting: number;
  active: number;
  completed: number;
  failed: number;
  delayed: number;
  paused: number;
}

interface QueueJob {
  id: string;
  name: string;
  data: any;
  opts: {
    priority: number;
    delay?: number;
    attempts: number;
    backoff?: any;
  };
  progress: number;
  processedOn?: string;
  finishedOn?: string;
  failedReason?: string;
  returnvalue?: any;
  attemptsMade: number;
  timestamp: string;
  status: 'waiting' | 'active' | 'completed' | 'failed' | 'delayed' | 'paused';
}

interface JobDetailsModalProps {
  job: QueueJob | null;
  isOpen: boolean;
  onClose: () => void;
}

function JobDetailsModal({ job, isOpen, onClose }: JobDetailsModalProps) {
  if (!isOpen || !job) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Job Details</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="space-y-6">
          {/* Basic Info */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Job ID</label>
              <p className="mt-1 text-sm text-gray-900 font-mono">{job.id}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Job Name</label>
              <p className="mt-1 text-sm text-gray-900">{job.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                job.status === 'completed' ? 'bg-green-100 text-green-800' :
                job.status === 'failed' ? 'bg-red-100 text-red-800' :
                job.status === 'active' ? 'bg-blue-100 text-blue-800' :
                job.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {job.status}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Priority</label>
              <p className="mt-1 text-sm text-gray-900">{job.opts.priority}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Progress</label>
              <div className="mt-1 flex items-center">
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${job.progress}%` }}
                  ></div>
                </div>
                <span className="ml-2 text-sm text-gray-600">{job.progress}%</span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Attempts</label>
              <p className="mt-1 text-sm text-gray-900">{job.attemptsMade} / {job.opts.attempts}</p>
            </div>
          </div>

          {/* Timestamps */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Created</label>
              <p className="mt-1 text-sm text-gray-900">{formatDate(job.timestamp)}</p>
            </div>
            {job.processedOn && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Processed</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(job.processedOn)}</p>
              </div>
            )}
            {job.finishedOn && (
              <div>
                <label className="block text-sm font-medium text-gray-700">Finished</label>
                <p className="mt-1 text-sm text-gray-900">{formatDate(job.finishedOn)}</p>
              </div>
            )}
          </div>

          {/* Job Data */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Job Data</label>
            <pre className="bg-gray-50 p-4 rounded-md text-sm overflow-x-auto">
              {JSON.stringify(job.data, null, 2)}
            </pre>
          </div>

          {/* Options */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Job Options</label>
            <pre className="bg-gray-50 p-4 rounded-md text-sm overflow-x-auto">
              {JSON.stringify(job.opts, null, 2)}
            </pre>
          </div>

          {/* Return Value */}
          {job.returnvalue && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Return Value</label>
              <pre className="bg-green-50 p-4 rounded-md text-sm overflow-x-auto">
                {JSON.stringify(job.returnvalue, null, 2)}
              </pre>
            </div>
          )}

          {/* Error */}
          {job.failedReason && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Error</label>
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-700">{job.failedReason}</p>
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
}

export default function QueueManagementPage() {
  const { user } = useAuth();
  const [stats, setStats] = useState<QueueStats>({
    waiting: 0,
    active: 0,
    completed: 0,
    failed: 0,
    delayed: 0,
    paused: 0,
  });
  const [jobs, setJobs] = useState<QueueJob[]>([]);
  const [selectedJobs, setSelectedJobs] = useState<Set<string>>(new Set());
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedJob, setSelectedJob] = useState<QueueJob | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  const isSuperAdmin = user?.role === 'super_admin';

  useEffect(() => {
    if (!isSuperAdmin) {
      setError('Access denied. Super Admin privileges required.');
      setIsLoading(false);
      return;
    }
    
    loadQueueData();
    
    // Set up polling for real-time updates
    const interval = setInterval(loadQueueData, 5000);
    return () => clearInterval(interval);
  }, [isSuperAdmin]);

  const loadQueueData = async () => {
    try {
      setError(null);
      
      // Load queue statistics
      const statsResponse = await api.getQueueStats();
      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data);
      }
      
      // Load jobs
      const jobsResponse = await api.getQueueJobs({ 
        status: statusFilter !== 'all' ? statusFilter : undefined,
        priority: priorityFilter !== 'all' ? parseInt(priorityFilter) : undefined,
        limit: 100 
      });
      if (jobsResponse.success && jobsResponse.data) {
        setJobs(jobsResponse.data);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to load queue data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleJobAction = async (action: string, jobIds: string[]) => {
    try {
      setError(null);
      
      const response = await api.manageQueueJobs(action, jobIds);
      
      if (response.success) {
        setSelectedJobs(new Set());
        loadQueueData();
      } else {
        setError(response.error || `Failed to ${action} jobs`);
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    }
  };

  const handleSelectJob = (jobId: string) => {
    const newSelected = new Set(selectedJobs);
    if (newSelected.has(jobId)) {
      newSelected.delete(jobId);
    } else {
      newSelected.add(jobId);
    }
    setSelectedJobs(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedJobs.size === jobs.length) {
      setSelectedJobs(new Set());
    } else {
      setSelectedJobs(new Set(jobs.map(job => job.id)));
    }
  };

  const handleViewJob = (job: QueueJob) => {
    setSelectedJob(job);
    setIsModalOpen(true);
  };

  const getPriorityLabel = (priority: number) => {
    if (priority >= 8) return { label: 'High', color: 'bg-red-100 text-red-800' };
    if (priority >= 5) return { label: 'Medium', color: 'bg-yellow-100 text-yellow-800' };
    return { label: 'Low', color: 'bg-green-100 text-green-800' };
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-600" />;
      case 'active':
        return <PlayIcon className="h-4 w-4 text-blue-600" />;
      case 'waiting':
        return <ClockIcon className="h-4 w-4 text-yellow-600" />;
      case 'paused':
        return <PauseIcon className="h-4 w-4 text-gray-600" />;
      default:
        return <QueueListIcon className="h-4 w-4 text-gray-600" />;
    }
  };

  if (!isSuperAdmin) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="mt-2 text-sm font-medium text-gray-900">Access Denied</h3>
          <p className="mt-1 text-sm text-gray-500">
            Super Admin privileges are required to access queue management.
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Queue Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Monitor and manage background job queues (Super Admin only)
        </p>
      </div>

      {/* Queue Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {Object.entries(stats).map(([key, value]) => (
          <div key={key} className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`p-3 rounded-md ${
                    key === 'completed' ? 'bg-green-500' :
                    key === 'failed' ? 'bg-red-500' :
                    key === 'active' ? 'bg-blue-500' :
                    key === 'waiting' ? 'bg-yellow-500' :
                    'bg-gray-500'
                  }`}>
                    <ChartBarIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate capitalize">
                      {key}
                    </dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {formatNumber(value)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex space-x-4">
            <div>
              <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700">
                Status
              </label>
              <select
                id="status-filter"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="all">All Status</option>
                <option value="waiting">Waiting</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="failed">Failed</option>
                <option value="delayed">Delayed</option>
                <option value="paused">Paused</option>
              </select>
            </div>

            <div>
              <label htmlFor="priority-filter" className="block text-sm font-medium text-gray-700">
                Priority
              </label>
              <select
                id="priority-filter"
                value={priorityFilter}
                onChange={(e) => setPriorityFilter(e.target.value)}
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              >
                <option value="all">All Priorities</option>
                <option value="10">High (8-10)</option>
                <option value="5">Medium (5-7)</option>
                <option value="1">Low (1-4)</option>
              </select>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={loadQueueData}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Refresh
            </button>

            {selectedJobs.size > 0 && (
              <>
                <button
                  onClick={() => handleJobAction('retry', Array.from(selectedJobs))}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlayIcon className="h-4 w-4 mr-2" />
                  Retry ({selectedJobs.size})
                </button>

                <button
                  onClick={() => handleJobAction('remove', Array.from(selectedJobs))}
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  <TrashIcon className="h-4 w-4 mr-2" />
                  Remove ({selectedJobs.size})
                </button>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Jobs Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {jobs.length === 0 ? (
          <div className="text-center py-12">
            <QueueListIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No jobs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              No jobs match your current filters.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <input
                      type="checkbox"
                      checked={selectedJobs.size === jobs.length && jobs.length > 0}
                      onChange={handleSelectAll}
                      className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Job
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Progress
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attempts
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {jobs.map((job) => {
                  const priorityInfo = getPriorityLabel(job.opts.priority);
                  return (
                    <tr key={job.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedJobs.has(job.id)}
                          onChange={() => handleSelectJob(job.id)}
                          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">{job.name}</div>
                          <div className="text-sm text-gray-500 font-mono">{job.id}</div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(job.status)}
                          <span className={`ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            job.status === 'completed' ? 'bg-green-100 text-green-800' :
                            job.status === 'failed' ? 'bg-red-100 text-red-800' :
                            job.status === 'active' ? 'bg-blue-100 text-blue-800' :
                            job.status === 'waiting' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {job.status}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${priorityInfo.color}`}>
                          {priorityInfo.label}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-1 bg-gray-200 rounded-full h-2 max-w-20">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${job.progress}%` }}
                            ></div>
                          </div>
                          <span className="ml-2 text-sm text-gray-600">{job.progress}%</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {job.attemptsMade} / {job.opts.attempts}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(job.timestamp)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleViewJob(job)}
                          className="text-indigo-600 hover:text-indigo-900 inline-flex items-center"
                        >
                          <EyeIcon className="h-4 w-4 mr-1" />
                          View
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Job Details Modal */}
      <JobDetailsModal
        job={selectedJob}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
}