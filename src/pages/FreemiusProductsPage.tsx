import React, { useState, useEffect } from 'react';
import { api } from '../services/api';
import { FreemiusProduct } from '../types';
import { formatNumber, formatDate } from '../utils/formatters';
import {
    ArrowPathIcon,
    EyeIcon,
    ChartBarIcon,
    CubeIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    ClockIcon,
} from '@heroicons/react/24/outline';

export default function FreemiusProductsPage() {
    const [products, setProducts] = useState<FreemiusProduct[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedProduct, setSelectedProduct] = useState<FreemiusProduct | null>(null);

    useEffect(() => {
        loadProducts();
    }, []);

    const loadProducts = async () => {
        try {
            setIsLoading(true);
            setError(null);
            const response = await api.getProducts();

            if (response.success && response.data) {
                setProducts(response.data);
            } else {
                setError(response.error || 'Failed to load products');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefreshProducts = async () => {
        try {
            setIsRefreshing(true);
            setError(null);
            const response = await api.syncFreemiusData();

            if (response.success) {
                // Reload products after sync
                await loadProducts();
            } else {
                setError(response.error || 'Failed to refresh product data');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred during refresh');
        } finally {
            setIsRefreshing(false);
        }
    };

    const getSyncStatusBadge = (product: FreemiusProduct) => {
        const lastSynced = new Date(product.updated || product.created);
        const hoursAgo = (Date.now() - lastSynced.getTime()) / (1000 * 60 * 60);

        if (hoursAgo < 1) {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <CheckCircleIcon className="w-3 h-3 mr-1" />
                    Fresh
                </span>
            );
        } else if (hoursAgo < 24) {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <ClockIcon className="w-3 h-3 mr-1" />
                    {Math.floor(hoursAgo)}h ago
                </span>
            );
        } else {
            return (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <ExclamationTriangleIcon className="w-3 h-3 mr-1" />
                    {Math.floor(hoursAgo / 24)}d ago
                </span>
            );
        }
    };

    if (isLoading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-2xl font-bold text-gray-900">Freemius Products</h1>
                    <p className="mt-1 text-sm text-gray-600">
                        Manage and monitor your Freemius products and their synchronization status
                    </p>
                </div>
                <button
                    onClick={handleRefreshProducts}
                    disabled={isRefreshing}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <ArrowPathIcon className={`-ml-1 mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    {isRefreshing ? 'Syncing...' : 'Sync Products'}
                </button>
            </div>

            {/* Error Alert */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error</h3>
                            <div className="mt-2 text-sm text-red-700">{error}</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Products Grid */}
            {products.length > 0 ? (
                <div className="grid grid-cols-1 gap-6 lg:grid-cols-2 xl:grid-cols-3">
                    {products.map((product) => (
                        <div key={product.id} className="bg-white overflow-hidden shadow rounded-lg">
                            <div className="p-6">
                                {/* Product Header */}
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center">
                                        {product.icon ? (
                                            <img
                                                src={product.icon}
                                                alt={product.title}
                                                className="h-10 w-10 rounded-lg"
                                            />
                                        ) : (
                                            <div className="h-10 w-10 bg-indigo-100 rounded-lg flex items-center justify-center">
                                                <CubeIcon className="h-6 w-6 text-indigo-600" />
                                            </div>
                                        )}
                                        <div className="ml-3">
                                            <h3 className="text-lg font-medium text-gray-900 truncate">
                                                {product.title}
                                            </h3>
                                            <p className="text-sm text-gray-500">{product.slug}</p>
                                        </div>
                                    </div>
                                    {getSyncStatusBadge(product)}
                                </div>

                                {/* Product Stats */}
                                <div className="grid grid-cols-2 gap-4 mb-4">
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-gray-900">
                                            {formatNumber(product.active_installs_count)}
                                        </div>
                                        <div className="text-xs text-gray-500">Active Installs</div>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-gray-900">
                                            {formatNumber(product.total_purchases)}
                                        </div>
                                        <div className="text-xs text-gray-500">Total Purchases</div>
                                    </div>
                                </div>

                                {/* Product Details */}
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Type:</span>
                                        <span className="font-medium capitalize">{product.type}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Environment:</span>
                                        <span className="font-medium">
                                            {product.environment === 0 ? 'Sandbox' : 'Live'}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Earnings:</span>
                                        <span className="font-medium">${product.earnings}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-gray-500">Last Updated:</span>
                                        <span className="font-medium">
                                            {formatDate(product.updated || product.created)}
                                        </span>
                                    </div>
                                </div>

                                {/* Action Button */}
                                <div className="mt-4">
                                    <button
                                        onClick={() => setSelectedProduct(product)}
                                        className="w-full inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                                    >
                                        <EyeIcon className="h-4 w-4 mr-2" />
                                        View Details
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            ) : (
                <div className="text-center py-12">
                    <CubeIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                        No Freemius products have been synchronized yet.
                    </p>
                    <div className="mt-6">
                        <button
                            onClick={handleRefreshProducts}
                            disabled={isRefreshing}
                            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        >
                            <ArrowPathIcon className={`-ml-1 mr-2 h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                            Sync Products Now
                        </button>
                    </div>
                </div>
            )}

            {/* Product Details Modal */}
            {selectedProduct && (
                <ProductDetailsModal
                    product={selectedProduct}
                    onClose={() => setSelectedProduct(null)}
                />
            )}
        </div>
    );
}

// Product Details Modal Component
interface ProductDetailsModalProps {
    product: FreemiusProduct;
    onClose: () => void;
}

function ProductDetailsModal({ product, onClose }: ProductDetailsModalProps) {
    return (
        <div className="fixed inset-0 z-50 overflow-y-auto">
            <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose} />

                <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                    <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div className="flex justify-between items-start mb-6">
                            <div className="flex items-center">
                                {product.icon ? (
                                    <img
                                        src={product.icon}
                                        alt={product.title}
                                        className="h-12 w-12 rounded-lg"
                                    />
                                ) : (
                                    <div className="h-12 w-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                                        <CubeIcon className="h-8 w-8 text-indigo-600" />
                                    </div>
                                )}
                                <div className="ml-4">
                                    <h3 className="text-lg font-medium text-gray-900">{product.title}</h3>
                                    <p className="text-sm text-gray-500">Product ID: {product.id}</p>
                                </div>
                            </div>
                            <button
                                onClick={onClose}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <span className="sr-only">Close</span>
                                <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        {/* Stats Grid */}
                        <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
                            <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <ChartBarIcon className="h-6 w-6 text-blue-600" />
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">
                                                    Active Installs
                                                </dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    {formatNumber(product.active_installs_count)}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <ChartBarIcon className="h-6 w-6 text-green-600" />
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">
                                                    Total Purchases
                                                </dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    {formatNumber(product.total_purchases)}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <ChartBarIcon className="h-6 w-6 text-purple-600" />
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">
                                                    Subscriptions
                                                </dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    {formatNumber(product.total_subscriptions)}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="bg-gray-50 overflow-hidden shadow rounded-lg">
                                <div className="p-5">
                                    <div className="flex items-center">
                                        <div className="flex-shrink-0">
                                            <ChartBarIcon className="h-6 w-6 text-yellow-600" />
                                        </div>
                                        <div className="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt className="text-sm font-medium text-gray-500 truncate">
                                                    Earnings
                                                </dt>
                                                <dd className="text-lg font-medium text-gray-900">
                                                    ${product.earnings}
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Detailed Information */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div>
                                <h4 className="text-lg font-medium text-gray-900 mb-4">Product Information</h4>
                                <dl className="space-y-3">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Slug</dt>
                                        <dd className="text-sm text-gray-900">{product.slug}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Type</dt>
                                        <dd className="text-sm text-gray-900 capitalize">{product.type}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Environment</dt>
                                        <dd className="text-sm text-gray-900">
                                            {product.environment === 0 ? 'Sandbox' : 'Live'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Developer ID</dt>
                                        <dd className="text-sm text-gray-900">{product.developer_id}</dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Store ID</dt>
                                        <dd className="text-sm text-gray-900">{product.store_id}</dd>
                                    </div>
                                </dl>
                            </div>

                            <div>
                                <h4 className="text-lg font-medium text-gray-900 mb-4">Configuration</h4>
                                <dl className="space-y-3">
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Released</dt>
                                        <dd className="text-sm text-gray-900">
                                            {product.is_released ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">SDK Required</dt>
                                        <dd className="text-sm text-gray-900">
                                            {product.is_sdk_required ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Pricing Visible</dt>
                                        <dd className="text-sm text-gray-900">
                                            {product.is_pricing_visible ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">WP.org Compliant</dt>
                                        <dd className="text-sm text-gray-900">
                                            {product.is_wp_org_compliant ? 'Yes' : 'No'}
                                        </dd>
                                    </div>
                                    <div>
                                        <dt className="text-sm font-medium text-gray-500">Money Back Period</dt>
                                        <dd className="text-sm text-gray-900">{product.money_back_period} days</dd>
                                    </div>
                                </dl>
                            </div>
                        </div>

                        {/* Timestamps */}
                        <div className="mt-6 pt-6 border-t border-gray-200">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">Timestamps</h4>
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Created</dt>
                                    <dd className="text-sm text-gray-900">{formatDate(product.created)}</dd>
                                </div>
                                <div>
                                    <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                                    <dd className="text-sm text-gray-900">
                                        {product.updated ? formatDate(product.updated) : 'Never'}
                                    </dd>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button
                            type="button"
                            onClick={onClose}
                            className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm"
                        >
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}