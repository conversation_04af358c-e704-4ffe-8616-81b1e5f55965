import React, { useState } from 'react';
import { api } from '../services/api';
import { IpData, IpRegistryData } from '../types';
import { formatDate, formatIpAddress } from '../utils/formatters';
import { isValidIpAddress } from '../utils/validation';
import {
  MagnifyingGlassIcon,
  ArrowPathIcon,
  GlobeAltIcon,
  ShieldExclamationIcon,
  ClockIcon,
  BuildingOfficeIcon,
  MapPinIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/outline';

interface SecurityBadgeProps {
  label: string;
  value: boolean;
  type: 'threat' | 'warning' | 'info';
}

function SecurityBadge({ label, value, type }: SecurityBadgeProps) {
  if (!value) return null;

  const colors = {
    threat: 'bg-red-100 text-red-800',
    warning: 'bg-yellow-100 text-yellow-800',
    info: 'bg-blue-100 text-blue-800',
  };

  const icons = {
    threat: ShieldExclamationIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
  };

  const Icon = icons[type];

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[type]}`}>
      <Icon className="h-3 w-3 mr-1" />
      {label}
    </span>
  );
}

interface IpDataDisplayProps {
  data: IpRegistryData;
  lastRefreshed?: string;
  onRefresh: () => void;
  isRefreshing: boolean;
}

function IpDataDisplay({ data, lastRefreshed, onRefresh, isRefreshing }: IpDataDisplayProps) {
  const securityFlags = [
    { key: 'is_threat', label: 'Threat', type: 'threat' as const },
    { key: 'is_abuser', label: 'Abuser', type: 'threat' as const },
    { key: 'is_attacker', label: 'Attacker', type: 'threat' as const },
    { key: 'is_vpn', label: 'VPN', type: 'warning' as const },
    { key: 'is_proxy', label: 'Proxy', type: 'warning' as const },
    { key: 'is_tor', label: 'Tor', type: 'warning' as const },
    { key: 'is_tor_exit', label: 'Tor Exit', type: 'warning' as const },
    { key: 'is_anonymous', label: 'Anonymous', type: 'warning' as const },
    { key: 'is_cloud_provider', label: 'Cloud Provider', type: 'info' as const },
    { key: 'is_relay', label: 'Relay', type: 'info' as const },
    { key: 'is_bogon', label: 'Bogon', type: 'info' as const },
  ];

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <GlobeAltIcon className="h-8 w-8 text-indigo-600 mr-3" />
            <div>
              <h3 className="text-lg font-medium text-gray-900">IP Intelligence Data</h3>
              <p className="text-sm text-gray-500 font-mono">{formatIpAddress(data.ip)}</p>
            </div>
          </div>
          <div className="flex items-center space-x-4">
            {lastRefreshed && (
              <div className="text-sm text-gray-500">
                <ClockIcon className="h-4 w-4 inline mr-1" />
                Last updated: {formatDate(lastRefreshed)}
              </div>
            )}
            <button
              onClick={onRefresh}
              disabled={isRefreshing}
              className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              <ArrowPathIcon className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <InformationCircleIcon className="h-5 w-5 mr-2 text-gray-400" />
              Basic Information
            </h4>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">IP Type:</span>
                <span className="text-sm text-gray-900">{data.type}</span>
              </div>
              {data.hostname && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Hostname:</span>
                  <span className="text-sm text-gray-900 font-mono">{data.hostname}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">ASN:</span>
                <span className="text-sm text-gray-900">{data.connection.asn}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Organization:</span>
                <span className="text-sm text-gray-900">{data.connection.organization}</span>
              </div>
            </div>
          </div>

          {/* Location Information */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <MapPinIcon className="h-5 w-5 mr-2 text-gray-400" />
              Location
            </h4>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Country:</span>
                <span className="text-sm text-gray-900 flex items-center">
                  <span className="mr-2">{data.location.country.flag.emoji}</span>
                  {data.location.country.name}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Region:</span>
                <span className="text-sm text-gray-900">{data.location.region.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">City:</span>
                <span className="text-sm text-gray-900">{data.location.city}</span>
              </div>
              {data.location.postal && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Postal:</span>
                  <span className="text-sm text-gray-900">{data.location.postal}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Coordinates:</span>
                <span className="text-sm text-gray-900 font-mono">
                  {data.location.latitude.toFixed(4)}, {data.location.longitude.toFixed(4)}
                </span>
              </div>
            </div>
          </div>

          {/* Company Information */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <BuildingOfficeIcon className="h-5 w-5 mr-2 text-gray-400" />
              Company & Connection
            </h4>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Company:</span>
                <span className="text-sm text-gray-900">{data.company.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Type:</span>
                <span className="text-sm text-gray-900 capitalize">{data.company.type}</span>
              </div>
              {data.company.domain && (
                <div className="flex justify-between">
                  <span className="text-sm font-medium text-gray-500">Domain:</span>
                  <span className="text-sm text-gray-900">{data.company.domain}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Route:</span>
                <span className="text-sm text-gray-900 font-mono">{data.connection.route}</span>
              </div>
            </div>
          </div>

          {/* Time Zone & Currency */}
          <div className="space-y-4">
            <h4 className="text-md font-medium text-gray-900 flex items-center">
              <ClockIcon className="h-5 w-5 mr-2 text-gray-400" />
              Time Zone & Currency
            </h4>
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Time Zone:</span>
                <span className="text-sm text-gray-900">{data.time_zone.id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Current Time:</span>
                <span className="text-sm text-gray-900 font-mono">{data.time_zone.current_time}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Currency:</span>
                <span className="text-sm text-gray-900">
                  {data.currency.name} ({data.currency.symbol})
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-500">Language:</span>
                <span className="text-sm text-gray-900">{data.location.language.name}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Security Information */}
        <div className="mt-6">
          <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
            <ShieldExclamationIcon className="h-5 w-5 mr-2 text-gray-400" />
            Security Analysis
          </h4>
          <div className="flex flex-wrap gap-2">
            {securityFlags.map(({ key, label, type }) => (
              <SecurityBadge
                key={key}
                label={label}
                value={data.security[key as keyof typeof data.security]}
                type={type}
              />
            ))}
            {securityFlags.every(({ key }) => !data.security[key as keyof typeof data.security]) && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                <CheckCircleIcon className="h-4 w-4 mr-1" />
                Clean IP - No security flags detected
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function IpDataPage() {
  const [searchIp, setSearchIp] = useState('');
  const [ipData, setIpData] = useState<IpData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchError, setSearchError] = useState<string | null>(null);

  const handleSearch = async () => {
    if (!searchIp.trim()) {
      setSearchError('Please enter an IP address');
      return;
    }

    if (!isValidIpAddress(searchIp.trim())) {
      setSearchError('Please enter a valid IP address');
      return;
    }

    setIsLoading(true);
    setError(null);
    setSearchError(null);

    try {
      const response = await api.searchIpData(searchIp.trim());
      
      if (response.success) {
        setIpData(response.data);
        if (!response.data) {
          setError('No data found for this IP address');
        }
      } else {
        setError(response.error || 'Failed to search IP data');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    if (!ipData) return;

    setIsRefreshing(true);
    setError(null);

    try {
      const response = await api.refreshIpData(ipData.ip);
      
      if (response.success && response.data) {
        setIpData(response.data);
      } else {
        setError(response.error || 'Failed to refresh IP data');
      }
    } catch (error: any) {
      setError(error.message || 'An unexpected error occurred');
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">IP Data Management</h1>
        <p className="mt-1 text-sm text-gray-600">
          Search IP addresses and manage cached IP intelligence data
        </p>
      </div>

      {/* Search Section */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="max-w-xl">
          <label htmlFor="ip-search" className="block text-sm font-medium text-gray-700 mb-2">
            Search IP Address
          </label>
          <div className="flex space-x-3">
            <div className="flex-1">
              <input
                type="text"
                id="ip-search"
                value={searchIp}
                onChange={(e) => {
                  setSearchIp(e.target.value);
                  setSearchError(null);
                }}
                onKeyPress={handleKeyPress}
                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                placeholder="Enter IP address (e.g., *********** or 2001:db8::1)"
              />
              {searchError && (
                <p className="mt-1 text-sm text-red-600">{searchError}</p>
              )}
            </div>
            <button
              onClick={handleSearch}
              disabled={isLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Searching...
                </>
              ) : (
                <>
                  <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                  Search
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      {/* Results */}
      {ipData && (
        <IpDataDisplay
          data={ipData.data}
          lastRefreshed={ipData.last_refreshed_at}
          onRefresh={handleRefresh}
          isRefreshing={isRefreshing}
        />
      )}

      {/* No Results State */}
      {!isLoading && !ipData && !error && (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="text-center py-12">
            <GlobeAltIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Search for IP Data</h3>
            <p className="mt-1 text-sm text-gray-500">
              Enter an IP address above to view its intelligence data and security analysis.
            </p>
          </div>
        </div>
      )}
    </div>
  );
}