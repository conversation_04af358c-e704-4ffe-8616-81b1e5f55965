import React, { useState } from 'react';
import { api } from '../services/api';
import { formatDate, formatIpAddress } from '../utils/formatters';
import { isValidIpAddress } from '../utils/validation';
import {
    MagnifyingGlassIcon,
    ArrowPathIcon,
    GlobeAltIcon,
    ShieldExclamationIcon,
    ClockIcon,
    BuildingOfficeIcon,
    MapPinIcon,
    ExclamationTriangleIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    ChartBarIcon,
    ServerIcon,
} from '@heroicons/react/24/outline';

interface IpAnalysisResult {
    ip: string;
    location: {
        country: string;
        region: string;
        city: string;
        coordinates: [number, number];
        timezone: string;
    };
    network: {
        isp: string;
        organization: string;
        asn: string;
    };
    security: {
        riskScore: number;
        recommendation: 'allow' | 'block' | 'review';
        threats: string[];
        flags: {
            isProxy: boolean;
            isVpn: boolean;
            isTor: boolean;
            isMalware: boolean;
            isCloudProvider: boolean;
            isBogon: boolean;
        };
    };
    metadata: {
        usedStoredData: boolean;
        lastUpdated: string;
        processingTime: number;
        dataExpiresAt: string;
    };
}

interface SecurityBadgeProps {
    label: string;
    value: boolean;
    type: 'threat' | 'warning' | 'info';
}

function SecurityBadge({ label, value, type }: SecurityBadgeProps) {
    if (!value) return null;

    const colors = {
        threat: 'bg-red-100 text-red-800',
        warning: 'bg-yellow-100 text-yellow-800',
        info: 'bg-blue-100 text-blue-800',
    };

    const icons = {
        threat: ShieldExclamationIcon,
        warning: ExclamationTriangleIcon,
        info: InformationCircleIcon,
    };

    const Icon = icons[type];

    return (
        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colors[type]}`}>
            <Icon className="h-3 w-3 mr-1" />
            {label}
        </span>
    );
}

interface RiskScoreDisplayProps {
    score: number;
    recommendation: string;
}

function RiskScoreDisplay({ score, recommendation }: RiskScoreDisplayProps) {
    const getScoreColor = (score: number) => {
        if (score >= 80) return 'text-red-600 bg-red-100';
        if (score >= 60) return 'text-orange-600 bg-orange-100';
        if (score >= 40) return 'text-yellow-600 bg-yellow-100';
        return 'text-green-600 bg-green-100';
    };

    const getRecommendationColor = (rec: string) => {
        switch (rec) {
            case 'block': return 'text-red-600 bg-red-100';
            case 'review': return 'text-yellow-600 bg-yellow-100';
            case 'allow': return 'text-green-600 bg-green-100';
            default: return 'text-gray-600 bg-gray-100';
        }
    };

    return (
        <div className="flex items-center space-x-4">
            <div className="flex items-center">
                <span className="text-sm font-medium text-gray-500 mr-2">Risk Score:</span>
                <span className={`px-3 py-1 rounded-full text-sm font-semibold ${getScoreColor(score)}`}>
                    {score}/100
                </span>
            </div>
            <div className="flex items-center">
                <span className="text-sm font-medium text-gray-500 mr-2">Recommendation:</span>
                <span className={`px-3 py-1 rounded-full text-sm font-semibold capitalize ${getRecommendationColor(recommendation)}`}>
                    {recommendation}
                </span>
            </div>
        </div>
    );
}

interface IpAnalysisDisplayProps {
    data: IpAnalysisResult;
    onRefresh: () => void;
    isRefreshing: boolean;
}

function IpAnalysisDisplay({ data, onRefresh, isRefreshing }: IpAnalysisDisplayProps) {
    const securityFlags = [
        { key: 'isMalware', label: 'Malware', type: 'threat' as const },
        { key: 'isTor', label: 'Tor', type: 'threat' as const },
        { key: 'isProxy', label: 'Proxy', type: 'warning' as const },
        { key: 'isVpn', label: 'VPN', type: 'warning' as const },
        { key: 'isCloudProvider', label: 'Cloud Provider', type: 'info' as const },
        { key: 'isBogon', label: 'Bogon', type: 'info' as const },
    ];

    const isDataFresh = new Date(data.metadata.dataExpiresAt) > new Date();

    return (
        <div className="bg-white shadow rounded-lg">
            <div className="px-4 py-5 sm:p-6">
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center">
                        <GlobeAltIcon className="h-8 w-8 text-indigo-600 mr-3" />
                        <div>
                            <h3 className="text-lg font-medium text-gray-900">IP Intelligence Analysis</h3>
                            <p className="text-sm text-gray-500 font-mono">{formatIpAddress(data.ip)}</p>
                        </div>
                    </div>
                    <div className="flex items-center space-x-4">
                        <div className="text-sm text-gray-500">
                            <ClockIcon className="h-4 w-4 inline mr-1" />
                            Last updated: {formatDate(data.metadata.lastUpdated)}
                        </div>
                        <div className={`text-xs px-2 py-1 rounded-full ${isDataFresh ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                            {isDataFresh ? 'Fresh Data' : 'Stale Data'}
                        </div>
                        <button
                            onClick={onRefresh}
                            disabled={isRefreshing}
                            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        >
                            <ArrowPathIcon className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                            {isRefreshing ? 'Refreshing...' : 'Refresh'}
                        </button>
                    </div>
                </div>

                {/* Risk Assessment */}
                <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="text-md font-medium text-gray-900 flex items-center mb-3">
                        <ChartBarIcon className="h-5 w-5 mr-2 text-gray-400" />
                        Risk Assessment
                    </h4>
                    <RiskScoreDisplay
                        score={data.security.riskScore}
                        recommendation={data.security.recommendation}
                    />
                    {data.security.threats.length > 0 && (
                        <div className="mt-3">
                            <span className="text-sm font-medium text-gray-500">Detected Threats:</span>
                            <div className="mt-1 flex flex-wrap gap-1">
                                {data.security.threats.map((threat, index) => (
                                    <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        {threat}
                                    </span>
                                ))}
                            </div>
                        </div>
                    )}
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Location Information */}
                    <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900 flex items-center">
                            <MapPinIcon className="h-5 w-5 mr-2 text-gray-400" />
                            Location
                        </h4>
                        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Country:</span>
                                <span className="text-sm text-gray-900">{data.location.country}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Region:</span>
                                <span className="text-sm text-gray-900">{data.location.region}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">City:</span>
                                <span className="text-sm text-gray-900">{data.location.city}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Coordinates:</span>
                                <span className="text-sm text-gray-900 font-mono">
                                    {data.location.coordinates[0].toFixed(4)}, {data.location.coordinates[1].toFixed(4)}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Timezone:</span>
                                <span className="text-sm text-gray-900">{data.location.timezone}</span>
                            </div>
                        </div>
                    </div>

                    {/* Network Information */}
                    <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900 flex items-center">
                            <ServerIcon className="h-5 w-5 mr-2 text-gray-400" />
                            Network
                        </h4>
                        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">ISP:</span>
                                <span className="text-sm text-gray-900">{data.network.isp}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Organization:</span>
                                <span className="text-sm text-gray-900">{data.network.organization}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">ASN:</span>
                                <span className="text-sm text-gray-900 font-mono">{data.network.asn}</span>
                            </div>
                        </div>
                    </div>

                    {/* Metadata */}
                    <div className="space-y-4">
                        <h4 className="text-md font-medium text-gray-900 flex items-center">
                            <InformationCircleIcon className="h-5 w-5 mr-2 text-gray-400" />
                            Analysis Metadata
                        </h4>
                        <div className="bg-gray-50 rounded-lg p-4 space-y-3">
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Data Source:</span>
                                <span className="text-sm text-gray-900">
                                    {data.metadata.usedStoredData ? 'Cached' : 'Fresh API'}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Processing Time:</span>
                                <span className="text-sm text-gray-900">{data.metadata.processingTime}ms</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-sm font-medium text-gray-500">Data Expires:</span>
                                <span className="text-sm text-gray-900">{formatDate(data.metadata.dataExpiresAt)}</span>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Security Flags */}
                <div className="mt-6">
                    <h4 className="text-md font-medium text-gray-900 flex items-center mb-4">
                        <ShieldExclamationIcon className="h-5 w-5 mr-2 text-gray-400" />
                        Security Flags
                    </h4>
                    <div className="flex flex-wrap gap-2">
                        {securityFlags.map(({ key, label, type }) => (
                            <SecurityBadge
                                key={key}
                                label={label}
                                value={data.security.flags[key as keyof typeof data.security.flags]}
                                type={type}
                            />
                        ))}
                        {securityFlags.every(({ key }) => !data.security.flags[key as keyof typeof data.security.flags]) && (
                            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                                <CheckCircleIcon className="h-4 w-4 mr-1" />
                                Clean IP - No security flags detected
                            </span>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}

export default function IpLookupPage() {
    const [searchIp, setSearchIp] = useState('');
    const [analysisResult, setAnalysisResult] = useState<IpAnalysisResult | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isRefreshing, setIsRefreshing] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [searchError, setSearchError] = useState<string | null>(null);

    const handleLookup = async () => {
        if (!searchIp.trim()) {
            setSearchError('Please enter an IP address');
            return;
        }

        if (!isValidIpAddress(searchIp.trim())) {
            setSearchError('Please enter a valid IP address');
            return;
        }

        setIsLoading(true);
        setError(null);
        setSearchError(null);

        try {
            const response = await api.lookupIp(searchIp.trim());

            if (response.success && response.data) {
                setAnalysisResult(response.data);
            } else {
                setError(response.error || 'Failed to analyze IP address');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsLoading(false);
        }
    };

    const handleRefresh = async () => {
        if (!analysisResult) return;

        setIsRefreshing(true);
        setError(null);

        try {
            const response = await api.refreshIpIntelligence(analysisResult.ip);

            if (response.success && response.data) {
                setAnalysisResult(response.data);
            } else {
                setError(response.error || 'Failed to refresh IP data');
            }
        } catch (error: any) {
            setError(error.message || 'An unexpected error occurred');
        } finally {
            setIsRefreshing(false);
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleLookup();
        }
    };

    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-2xl font-bold text-gray-900">IP Intelligence Lookup</h1>
                <p className="mt-1 text-sm text-gray-600">
                    Analyze IP addresses for security threats, geolocation, and network information
                </p>
            </div>

            {/* Search Section */}
            <div className="bg-white shadow rounded-lg p-6">
                <div className="max-w-xl">
                    <label htmlFor="ip-lookup" className="block text-sm font-medium text-gray-700 mb-2">
                        IP Address Lookup
                    </label>
                    <div className="flex space-x-3">
                        <div className="flex-1">
                            <input
                                type="text"
                                id="ip-lookup"
                                value={searchIp}
                                onChange={(e) => {
                                    setSearchIp(e.target.value);
                                    setSearchError(null);
                                }}
                                onKeyPress={handleKeyPress}
                                className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                                placeholder="Enter IP address (e.g., *********** or 2001:db8::1)"
                            />
                            {searchError && (
                                <p className="mt-1 text-sm text-red-600">{searchError}</p>
                            )}
                        </div>
                        <button
                            onClick={handleLookup}
                            disabled={isLoading}
                            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
                        >
                            {isLoading ? (
                                <>
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                    Analyzing...
                                </>
                            ) : (
                                <>
                                    <MagnifyingGlassIcon className="h-4 w-4 mr-2" />
                                    Analyze
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* Error Display */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                    <div className="flex">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                        <div className="ml-3">
                            <h3 className="text-sm font-medium text-red-800">Error</h3>
                            <div className="mt-2 text-sm text-red-700">{error}</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Results */}
            {analysisResult && (
                <IpAnalysisDisplay
                    data={analysisResult}
                    onRefresh={handleRefresh}
                    isRefreshing={isRefreshing}
                />
            )}

            {/* No Results State */}
            {!isLoading && !analysisResult && !error && (
                <div className="bg-white shadow rounded-lg p-6">
                    <div className="text-center py-12">
                        <GlobeAltIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">Analyze IP Address</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            Enter an IP address above to view its intelligence data, security analysis, and risk assessment.
                        </p>
                    </div>
                </div>
            )}
        </div>
    );
}