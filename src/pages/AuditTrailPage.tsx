import React, { useState, useEffect } from 'react';
import {
    Shield,
    Search,
    Filter,
    Download,
    RefreshCw,
    Calendar,
    Eye,
    AlertTriangle,
    CheckCircle,
    XCircle,
    Clock,
    User,
    Globe,
    Activity,
    Database,
    Settings,
    ChevronLeft,
    ChevronRight,
    ExternalLink
} from 'lucide-react';
import { api } from '../services/api';
import { useAuth } from '../contexts/AuthContext';

interface AuditLogEntry {
    id: string;
    eventType: string;
    userId?: string;
    email?: string;
    ipAddress: string;
    userAgent?: string;
    resource?: string;
    action?: string;
    details?: Record<string, any>;
    success: boolean;
    errorMessage?: string;
    createdAt: string;
}

interface AuditFilters {
    timeRange?: string;
    eventTypes?: string[];
    userId?: string;
    email?: string;
    ipAddress?: string;
    resource?: string;
    action?: string;
    success?: boolean;
    suspiciousOnly?: boolean;
    searchTerm?: string;
    limit?: number;
    offset?: number;
}

interface AuditStats {
    totalEvents: number;
    successfulEvents: number;
    failedEvents: number;
    uniqueUsers: number;
    uniqueIPs: number;
    topEventTypes: Array<{ eventType: string; count: number }>;
}

const AuditTrailPage: React.FC = () => {
    const { user } = useAuth();
    const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
    const [auditStats, setAuditStats] = useState<AuditStats | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [selectedLog, setSelectedLog] = useState<AuditLogEntry | null>(null);
    const [showDetailsModal, setShowDetailsModal] = useState(false);
    const [showFilters, setShowFilters] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [totalItems, setTotalItems] = useState(0);

    const [filters, setFilters] = useState<AuditFilters>({
        timeRange: 'last_24h',
        limit: 50,
        offset: 0,
    });

    const [searchForm, setSearchForm] = useState({
        searchTerm: '',
        eventType: '',
        resource: '',
        userId: '',
        email: '',
        ipAddress: '',
        success: '',
        timeRange: 'last_24h',
        suspiciousOnly: false,
    });

    const itemsPerPage = 50;

    useEffect(() => {
        loadAuditData();
    }, [filters, currentPage]);

    const loadAuditData = async () => {
        setLoading(true);
        setError(null);

        try {
            const auditFilters = {
                ...filters,
                offset: (currentPage - 1) * itemsPerPage,
            };

            const [logsResponse, statsResponse] = await Promise.all([
                api.getAuditLogs(auditFilters),
                api.getAuditStats('day'),
            ]);

            if (logsResponse.success && logsResponse.data) {
                setAuditLogs(logsResponse.data.events || []);
                setTotalItems(logsResponse.data.summary?.totalEvents || 0);
                setTotalPages(Math.ceil(totalItems / itemsPerPage));
            }

            if (statsResponse.success && statsResponse.data) {
                setAuditStats(statsResponse.data);
            }
        } catch (error) {
            console.error('Failed to load audit data:', error);
            setError('Failed to load audit data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const handleSearch = () => {
        const newFilters: AuditFilters = {
            limit: itemsPerPage,
            offset: 0,
        };

        if (searchForm.searchTerm.trim()) newFilters.searchTerm = searchForm.searchTerm.trim();
        if (searchForm.eventType) newFilters.eventTypes = [searchForm.eventType];
        if (searchForm.resource) newFilters.resource = searchForm.resource;
        if (searchForm.userId.trim()) newFilters.userId = searchForm.userId.trim();
        if (searchForm.email.trim()) newFilters.email = searchForm.email.trim();
        if (searchForm.ipAddress.trim()) newFilters.ipAddress = searchForm.ipAddress.trim();
        if (searchForm.success !== '') newFilters.success = searchForm.success === 'true';
        if (searchForm.timeRange) newFilters.timeRange = searchForm.timeRange;
        if (searchForm.suspiciousOnly) newFilters.suspiciousOnly = true;

        setFilters(newFilters);
        setCurrentPage(1);
    };

    const handleClearFilters = () => {
        setSearchForm({
            searchTerm: '',
            eventType: '',
            resource: '',
            userId: '',
            email: '',
            ipAddress: '',
            success: '',
            timeRange: 'last_24h',
            suspiciousOnly: false,
        });
        setFilters({
            timeRange: 'last_24h',
            limit: itemsPerPage,
            offset: 0,
        });
        setCurrentPage(1);
    };

    const exportAuditLogs = async () => {
        try {
            const response = await api.exportAuditLogs(filters);
            if (response.success && response.data) {
                // Create and download CSV file
                const blob = new Blob([response.data], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
        } catch (error) {
            console.error('Failed to export audit logs:', error);
        }
    };

    const getEventTypeIcon = (eventType: string) => {
        if (eventType.includes('LOGIN') || eventType.includes('AUTH')) return <User className="w-4 h-4" />;
        if (eventType.includes('IP_')) return <Globe className="w-4 h-4" />;
        if (eventType.includes('FREEMIUS_')) return <Database className="w-4 h-4" />;
        if (eventType.includes('ADMIN_')) return <Settings className="w-4 h-4" />;
        if (eventType.includes('SESSION_')) return <Clock className="w-4 h-4" />;
        return <Activity className="w-4 h-4" />;
    };

    const getEventTypeColor = (eventType: string, success: boolean) => {
        if (!success) return 'text-red-600 bg-red-100';
        if (eventType.includes('LOGIN')) return 'text-green-600 bg-green-100';
        if (eventType.includes('IP_')) return 'text-blue-600 bg-blue-100';
        if (eventType.includes('FREEMIUS_')) return 'text-purple-600 bg-purple-100';
        if (eventType.includes('ADMIN_')) return 'text-orange-600 bg-orange-100';
        if (eventType.includes('SESSION_')) return 'text-indigo-600 bg-indigo-100';
        return 'text-gray-600 bg-gray-100';
    };

    const getSeverityLevel = (eventType: string, success: boolean) => {
        if (!success) {
            if (eventType.includes('VIOLATION') || eventType.includes('UNAUTHORIZED')) return 'HIGH';
            if (eventType.includes('FAILED') || eventType.includes('ERROR')) return 'MEDIUM';
            return 'LOW';
        }
        if (eventType.includes('ADMIN_') || eventType.includes('ROLE_CHANGED')) return 'MEDIUM';
        return 'LOW';
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString();
    };

    const formatEventType = (eventType: string) => {
        return eventType.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
    };

    const viewLogDetails = (log: AuditLogEntry) => {
        setSelectedLog(log);
        setShowDetailsModal(true);
    };

    if (loading && auditLogs.length === 0) {
        return (
            <div className="flex items-center justify-center h-64">
                <RefreshCw className="w-8 h-8 animate-spin text-blue-600" />
                <span className="ml-2 text-gray-600">Loading audit trail...</span>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                    <Shield className="w-8 h-8 text-blue-600" />
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Audit Trail</h1>
                        <p className="text-gray-600">Comprehensive security and activity monitoring</p>
                    </div>
                </div>
                <div className="flex space-x-2">
                    <button
                        onClick={() => setShowFilters(!showFilters)}
                        className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
                    >
                        <Filter className="w-4 h-4 mr-2" />
                        {showFilters ? 'Hide Filters' : 'Show Filters'}
                    </button>
                    <button
                        onClick={exportAuditLogs}
                        className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
                    >
                        <Download className="w-4 h-4 mr-2" />
                        Export
                    </button>
                    <button
                        onClick={loadAuditData}
                        className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
                    >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Refresh
                    </button>
                </div>
            </div>

            {/* Stats Cards */}
            {auditStats && (
                <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <Activity className="w-8 h-8 text-blue-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Total Events</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.totalEvents.toLocaleString()}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <CheckCircle className="w-8 h-8 text-green-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                                <p className="text-2xl font-bold text-gray-900">
                                    {auditStats.totalEvents > 0
                                        ? Math.round((auditStats.successfulEvents / auditStats.totalEvents) * 100)
                                        : 0}%
                                </p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <XCircle className="w-8 h-8 text-red-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Failed Events</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.failedEvents.toLocaleString()}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <User className="w-8 h-8 text-purple-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Unique Users</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.uniqueUsers}</p>
                            </div>
                        </div>
                    </div>
                    <div className="bg-white p-6 rounded-lg shadow">
                        <div className="flex items-center">
                            <Globe className="w-8 h-8 text-orange-600" />
                            <div className="ml-4">
                                <p className="text-sm font-medium text-gray-600">Unique IPs</p>
                                <p className="text-2xl font-bold text-gray-900">{auditStats.uniqueIPs}</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Advanced Filters */}
            {showFilters && (
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Search Term
                            </label>
                            <div className="relative">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                <input
                                    type="text"
                                    placeholder="Search across all fields..."
                                    value={searchForm.searchTerm}
                                    onChange={(e) => setSearchForm({ ...searchForm, searchTerm: e.target.value })}
                                    className="pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Event Type
                            </label>
                            <select
                                value={searchForm.eventType}
                                onChange={(e) => setSearchForm({ ...searchForm, eventType: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">All Event Types</option>
                                <optgroup label="Authentication">
                                    <option value="LOGIN_SUCCESS">Login Success</option>
                                    <option value="LOGIN_FAILURE">Login Failure</option>
                                    <option value="LOGOUT">Logout</option>
                                    <option value="PASSWORD_CHANGE">Password Change</option>
                                </optgroup>
                                <optgroup label="IP Intelligence">
                                    <option value="IP_ANALYSIS_REQUESTED">IP Analysis Requested</option>
                                    <option value="IP_ANALYSIS_COMPLETED">IP Analysis Completed</option>
                                    <option value="IP_DATA_REFRESHED">IP Data Refreshed</option>
                                    <option value="IP_LOOKUP_PERFORMED">IP Lookup Performed</option>
                                </optgroup>
                                <optgroup label="Freemius">
                                    <option value="FREEMIUS_API_REQUEST">Freemius API Request</option>
                                    <option value="FREEMIUS_SYNC_COMPLETED">Freemius Sync Completed</option>
                                    <option value="FREEMIUS_WEBHOOK_RECEIVED">Freemius Webhook Received</option>
                                </optgroup>
                                <optgroup label="Admin Actions">
                                    <option value="ADMIN_USER_CREATED">Admin User Created</option>
                                    <option value="ADMIN_USER_UPDATED">Admin User Updated</option>
                                    <option value="ADMIN_ROLE_CHANGED">Admin Role Changed</option>
                                </optgroup>
                                <optgroup label="Security">
                                    <option value="UNAUTHORIZED_ACCESS_ATTEMPT">Unauthorized Access</option>
                                    <option value="RATE_LIMIT_EXCEEDED">Rate Limit Exceeded</option>
                                    <option value="SECURITY_VIOLATION">Security Violation</option>
                                </optgroup>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Resource
                            </label>
                            <select
                                value={searchForm.resource}
                                onChange={(e) => setSearchForm({ ...searchForm, resource: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">All Resources</option>
                                <option value="authentication">Authentication</option>
                                <option value="ip_intelligence">IP Intelligence</option>
                                <option value="freemius">Freemius</option>
                                <option value="users">Users</option>
                                <option value="settings">Settings</option>
                                <option value="security">Security</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Time Range
                            </label>
                            <select
                                value={searchForm.timeRange}
                                onChange={(e) => setSearchForm({ ...searchForm, timeRange: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="last_hour">Last Hour</option>
                                <option value="last_24h">Last 24 Hours</option>
                                <option value="last_week">Last Week</option>
                                <option value="last_month">Last Month</option>
                            </select>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                User Email
                            </label>
                            <input
                                type="email"
                                placeholder="<EMAIL>"
                                value={searchForm.email}
                                onChange={(e) => setSearchForm({ ...searchForm, email: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                IP Address
                            </label>
                            <input
                                type="text"
                                placeholder="***********"
                                value={searchForm.ipAddress}
                                onChange={(e) => setSearchForm({ ...searchForm, ipAddress: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Status
                            </label>
                            <select
                                value={searchForm.success}
                                onChange={(e) => setSearchForm({ ...searchForm, success: e.target.value })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            >
                                <option value="">All Status</option>
                                <option value="true">Success</option>
                                <option value="false">Failed</option>
                            </select>
                        </div>

                        <div className="flex items-center">
                            <label className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={searchForm.suspiciousOnly}
                                    onChange={(e) => setSearchForm({ ...searchForm, suspiciousOnly: e.target.checked })}
                                    className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <span className="text-sm text-gray-700">Suspicious Only</span>
                            </label>
                        </div>
                    </div>

                    <div className="flex justify-between items-center mt-6">
                        <button
                            onClick={handleClearFilters}
                            className="text-sm text-gray-600 hover:text-gray-800 flex items-center space-x-1"
                        >
                            <XCircle className="w-4 h-4" />
                            <span>Clear All Filters</span>
                        </button>
                        <button
                            onClick={handleSearch}
                            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                            <Search className="w-4 h-4 mr-2" />
                            Apply Filters
                        </button>
                    </div>
                </div>
            )}

            {/* Error State */}
            {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-2">
                    <AlertTriangle className="w-5 h-5 text-red-600" />
                    <span className="text-red-700">{error}</span>
                </div>
            )}

            {/* Audit Logs Table */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
                <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50">
                            <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Event
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    User
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Resource
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    IP Address
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Status
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Time
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                            {auditLogs.map((log) => (
                                <tr key={log.id} className="hover:bg-gray-50">
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center">
                                            <div className={`p-2 rounded-full ${getEventTypeColor(log.eventType, log.success)}`}>
                                                {getEventTypeIcon(log.eventType)}
                                            </div>
                                            <div className="ml-3">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {formatEventType(log.eventType)}
                                                </div>
                                                <div className="text-xs text-gray-500">
                                                    {log.action && `Action: ${log.action}`}
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="text-sm text-gray-900">{log.email || 'System'}</div>
                                        {log.userId && (
                                            <div className="text-xs text-gray-500">ID: {log.userId}</div>
                                        )}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <span className="text-sm text-gray-900 capitalize">
                                            {log.resource?.replace('_', ' ') || 'N/A'}
                                        </span>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-mono text-gray-900">
                                        {log.ipAddress}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <div className="flex items-center space-x-2">
                                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${log.success ? 'text-green-600 bg-green-100' : 'text-red-600 bg-red-100'
                                                }`}>
                                                {log.success ? 'Success' : 'Failed'}
                                            </span>
                                            {getSeverityLevel(log.eventType, log.success) === 'HIGH' && (
                                                <AlertTriangle className="w-4 h-4 text-red-600" />
                                            )}
                                        </div>
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {formatDate(log.createdAt)}
                                    </td>
                                    <td className="px-6 py-4 whitespace-nowrap">
                                        <button
                                            onClick={() => viewLogDetails(log)}
                                            className="text-blue-600 hover:text-blue-900 text-sm font-medium flex items-center space-x-1"
                                        >
                                            <Eye className="w-4 h-4" />
                                            <span>Details</span>
                                        </button>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {auditLogs.length === 0 && !loading && (
                    <div className="text-center py-12">
                        <Shield className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No audit logs found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            No audit logs match your current filters.
                        </p>
                    </div>
                )}
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
                <div className="flex items-center justify-between bg-white px-4 py-3 border border-gray-200 rounded-lg">
                    <div className="flex-1 flex justify-between sm:hidden">
                        <button
                            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                            disabled={currentPage === 1}
                            className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Previous
                        </button>
                        <button
                            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                            disabled={currentPage === totalPages}
                            className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                            Next
                        </button>
                    </div>
                    <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p className="text-sm text-gray-700">
                                Showing{' '}
                                <span className="font-medium">{(currentPage - 1) * itemsPerPage + 1}</span>
                                {' '}to{' '}
                                <span className="font-medium">
                                    {Math.min(currentPage * itemsPerPage, totalItems)}
                                </span>
                                {' '}of{' '}
                                <span className="font-medium">{totalItems}</span>
                                {' '}results
                            </p>
                        </div>
                        <div>
                            <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                                <button
                                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                    disabled={currentPage === 1}
                                    className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <ChevronLeft className="w-5 h-5" />
                                </button>
                                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                    const page = i + 1;
                                    return (
                                        <button
                                            key={page}
                                            onClick={() => setCurrentPage(page)}
                                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${currentPage === page
                                                    ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                                                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                                }`}
                                        >
                                            {page}
                                        </button>
                                    );
                                })}
                                <button
                                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                    disabled={currentPage === totalPages}
                                    className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                                >
                                    <ChevronRight className="w-5 h-5" />
                                </button>
                            </nav>
                        </div>
                    </div>
                </div>
            )}

            {/* Log Details Modal */}
            {showDetailsModal && selectedLog && (
                <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
                    <div className="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-medium text-gray-900">Audit Log Details</h3>
                            <button
                                onClick={() => setShowDetailsModal(false)}
                                className="text-gray-400 hover:text-gray-600"
                            >
                                <XCircle className="w-6 h-6" />
                            </button>
                        </div>

                        <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Event Type</label>
                                    <p className="mt-1 text-sm text-gray-900">{formatEventType(selectedLog.eventType)}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Resource</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedLog.resource || 'N/A'}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">User</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedLog.email || 'System'}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Status</label>
                                    <div className="mt-1 flex items-center space-x-1">
                                        {selectedLog.success ? (
                                            <CheckCircle className="w-4 h-4 text-green-600" />
                                        ) : (
                                            <XCircle className="w-4 h-4 text-red-600" />
                                        )}
                                        <span className={`text-sm ${selectedLog.success ? 'text-green-600' : 'text-red-600'}`}>
                                            {selectedLog.success ? 'Success' : 'Failed'}
                                        </span>
                                    </div>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">IP Address</label>
                                    <p className="mt-1 text-sm text-gray-900 font-mono">{selectedLog.ipAddress}</p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Timestamp</label>
                                    <p className="mt-1 text-sm text-gray-900">{formatDate(selectedLog.createdAt)}</p>
                                </div>
                            </div>

                            {selectedLog.action && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Action</label>
                                    <p className="mt-1 text-sm text-gray-900">{selectedLog.action}</p>
                                </div>
                            )}

                            {selectedLog.errorMessage && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">Error Message</label>
                                    <p className="mt-1 text-sm text-red-600">{selectedLog.errorMessage}</p>
                                </div>
                            )}

                            {selectedLog.details && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-2">Details</label>
                                    <pre className="bg-gray-50 p-3 rounded-md text-xs overflow-x-auto max-h-64">
                                        {JSON.stringify(selectedLog.details, null, 2)}
                                    </pre>
                                </div>
                            )}

                            {selectedLog.userAgent && (
                                <div>
                                    <label className="block text-sm font-medium text-gray-700">User Agent</label>
                                    <p className="mt-1 text-xs text-gray-600 break-all">{selectedLog.userAgent}</p>
                                </div>
                            )}
                        </div>

                        <div className="mt-6 flex justify-end">
                            <button
                                onClick={() => setShowDetailsModal(false)}
                                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AuditTrailPage;