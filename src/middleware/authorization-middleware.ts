import { Request, Response, NextFunction } from 'express';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { SecurityAlerting } from '../lib/security-alerting';
import { getClientIp } from './rate-limit-middleware';

// Role hierarchy and permissions for GuardGeo Platform
export enum UserRole {
    SUPER_ADMIN = 'SUPER_ADMIN',
    DEV = 'DEV',
    MARKETING = 'MARKETING',
    SALES = 'SALES'
}

export enum Permission {
    // User management permissions
    CREATE_ADMIN_USER = 'CREATE_ADMIN_USER',
    UPDATE_USER = 'UPDATE_USER',
    DELETE_USER = 'DELETE_USER',
    VIEW_USER = 'VIEW_USER',
    MANAGE_USER_ROLES = 'MANAGE_USER_ROLES',

    // System permissions
    VIEW_SYSTEM_SETTINGS = 'VIEW_SYSTEM_SETTINGS',
    UPDATE_SYSTEM_SETTINGS = 'UPDATE_SYSTEM_SETTINGS',
    VIEW_AUDIT_LOGS = 'VIEW_AUDIT_LOGS',
    MANAGE_SESSIONS = 'MANAGE_SESSIONS',

    // Security permissions
    VIEW_SECURITY_METRICS = 'VIEW_SECURITY_METRICS',
    MANAGE_SECURITY_SETTINGS = 'MANAGE_SECURITY_SETTINGS',
    VIEW_FAILED_LOGINS = 'VIEW_FAILED_LOGINS',

    // API permissions
    ACCESS_ADMIN_API = 'ACCESS_ADMIN_API',
    MANAGE_API_KEYS = 'MANAGE_API_KEYS',

    // GuardGeo Platform specific permissions
    // Freemius management
    VIEW_FREEMIUS_DATA = 'VIEW_FREEMIUS_DATA',
    MANAGE_FREEMIUS_SYNC = 'MANAGE_FREEMIUS_SYNC',
    VIEW_FREEMIUS_ANALYTICS = 'VIEW_FREEMIUS_ANALYTICS',

    // IP Intelligence
    VIEW_IP_DATA = 'VIEW_IP_DATA',
    PERFORM_IP_LOOKUP = 'PERFORM_IP_LOOKUP',
    MANAGE_IP_DATA = 'MANAGE_IP_DATA',
    VIEW_IP_ANALYTICS = 'VIEW_IP_ANALYTICS',

    // WordPress Plugin API
    ACCESS_PLUGIN_API = 'ACCESS_PLUGIN_API',
    VIEW_API_LOGS = 'VIEW_API_LOGS',

    // Analytics and Reporting
    VIEW_DASHBOARD = 'VIEW_DASHBOARD',
    VIEW_ANALYTICS = 'VIEW_ANALYTICS',
    EXPORT_DATA = 'EXPORT_DATA',

    // System monitoring
    VIEW_SYSTEM_HEALTH = 'VIEW_SYSTEM_HEALTH',
    MANAGE_SYSTEM_MAINTENANCE = 'MANAGE_SYSTEM_MAINTENANCE'
}

// Role-based permissions mapping for GuardGeo Platform
const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
    [UserRole.SUPER_ADMIN]: [
        // Super admin has all permissions
        Permission.CREATE_ADMIN_USER,
        Permission.UPDATE_USER,
        Permission.DELETE_USER,
        Permission.VIEW_USER,
        Permission.MANAGE_USER_ROLES,
        Permission.VIEW_SYSTEM_SETTINGS,
        Permission.UPDATE_SYSTEM_SETTINGS,
        Permission.VIEW_AUDIT_LOGS,
        Permission.MANAGE_SESSIONS,
        Permission.VIEW_SECURITY_METRICS,
        Permission.MANAGE_SECURITY_SETTINGS,
        Permission.VIEW_FAILED_LOGINS,
        Permission.ACCESS_ADMIN_API,
        Permission.MANAGE_API_KEYS,

        // GuardGeo Platform permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.MANAGE_FREEMIUS_SYNC,
        Permission.VIEW_FREEMIUS_ANALYTICS,
        Permission.VIEW_IP_DATA,
        Permission.PERFORM_IP_LOOKUP,
        Permission.MANAGE_IP_DATA,
        Permission.VIEW_IP_ANALYTICS,
        Permission.ACCESS_PLUGIN_API,
        Permission.VIEW_API_LOGS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA,
        Permission.VIEW_SYSTEM_HEALTH,
        Permission.MANAGE_SYSTEM_MAINTENANCE
    ],

    [UserRole.DEV]: [
        // Dev role has technical and development-related permissions
        Permission.VIEW_USER,
        Permission.VIEW_SYSTEM_SETTINGS,
        Permission.UPDATE_SYSTEM_SETTINGS,
        Permission.VIEW_AUDIT_LOGS,
        Permission.VIEW_SECURITY_METRICS,
        Permission.ACCESS_ADMIN_API,
        Permission.MANAGE_API_KEYS,

        // Technical GuardGeo permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.MANAGE_FREEMIUS_SYNC,
        Permission.VIEW_IP_DATA,
        Permission.PERFORM_IP_LOOKUP,
        Permission.MANAGE_IP_DATA,
        Permission.ACCESS_PLUGIN_API,
        Permission.VIEW_API_LOGS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA,
        Permission.VIEW_SYSTEM_HEALTH,
        Permission.MANAGE_SYSTEM_MAINTENANCE
    ],

    [UserRole.MARKETING]: [
        // Marketing role has analytics and reporting permissions
        Permission.VIEW_USER,
        Permission.ACCESS_ADMIN_API,

        // Marketing-focused GuardGeo permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.VIEW_FREEMIUS_ANALYTICS,
        Permission.VIEW_IP_ANALYTICS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA
    ],

    [UserRole.SALES]: [
        // Sales role has customer and revenue-focused permissions
        Permission.VIEW_USER,
        Permission.ACCESS_ADMIN_API,

        // Sales-focused GuardGeo permissions
        Permission.VIEW_FREEMIUS_DATA,
        Permission.VIEW_FREEMIUS_ANALYTICS,
        Permission.VIEW_DASHBOARD,
        Permission.VIEW_ANALYTICS,
        Permission.EXPORT_DATA
    ]
};

// Resource-level permissions for specific operations
export interface ResourcePermission {
    resource: string;
    action: string;
    requiredPermissions: Permission[];
    ownershipCheck?: (req: Request) => boolean;
    customCheck?: (req: Request) => Promise<boolean>;
}

// Pre-defined resource permissions for GuardGeo Platform
export const RESOURCE_PERMISSIONS: ResourcePermission[] = [
    // User management resources
    {
        resource: '/api/auth/admin-users',
        action: 'POST',
        requiredPermissions: [Permission.CREATE_ADMIN_USER]
    },
    {
        resource: '/api/users/:id',
        action: 'PUT',
        requiredPermissions: [Permission.UPDATE_USER],
        ownershipCheck: (req) => req.user?.id === req.params.id
    },
    {
        resource: '/api/users/:id',
        action: 'DELETE',
        requiredPermissions: [Permission.DELETE_USER]
    },
    {
        resource: '/api/users',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_USER]
    },

    // System settings resources
    {
        resource: '/api/system/settings',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_SYSTEM_SETTINGS]
    },
    {
        resource: '/api/system/settings',
        action: 'PUT',
        requiredPermissions: [Permission.UPDATE_SYSTEM_SETTINGS]
    },

    // Security resources
    {
        resource: '/api/security/audit-logs',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_AUDIT_LOGS]
    },
    {
        resource: '/api/security/metrics',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_SECURITY_METRICS]
    },
    {
        resource: '/api/sessions',
        action: 'GET',
        requiredPermissions: [Permission.MANAGE_SESSIONS]
    },
    {
        resource: '/api/sessions/:id',
        action: 'DELETE',
        requiredPermissions: [Permission.MANAGE_SESSIONS],
        ownershipCheck: (req) => req.session?.userId === req.user?.id
    },

    // GuardGeo Platform specific resources

    // Dashboard and analytics
    {
        resource: '/api/v1/admin/dashboard',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_DASHBOARD]
    },
    {
        resource: '/api/v1/admin/analytics',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_ANALYTICS]
    },

    // Freemius management
    {
        resource: '/api/v1/admin/freemius/products',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_FREEMIUS_DATA]
    },
    {
        resource: '/api/v1/admin/freemius/installations',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_FREEMIUS_DATA]
    },
    {
        resource: '/api/v1/admin/freemius/sync',
        action: 'POST',
        requiredPermissions: [Permission.MANAGE_FREEMIUS_SYNC]
    },
    {
        resource: '/api/v1/admin/freemius/analytics',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_FREEMIUS_ANALYTICS]
    },

    // IP Intelligence management
    {
        resource: '/api/v1/admin/ip-data',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_IP_DATA]
    },
    {
        resource: '/api/v1/admin/ip-lookup',
        action: 'POST',
        requiredPermissions: [Permission.PERFORM_IP_LOOKUP]
    },
    {
        resource: '/api/v1/admin/ip-requests/history',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_IP_DATA]
    },
    {
        resource: '/api/v1/admin/ip-analytics',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_IP_ANALYTICS]
    },

    // API logs and monitoring
    {
        resource: '/api/v1/admin/api-logs',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_API_LOGS]
    },
    {
        resource: '/api/v1/admin/system-health',
        action: 'GET',
        requiredPermissions: [Permission.VIEW_SYSTEM_HEALTH]
    },

    // WordPress Plugin API (public endpoint - no permissions required)
    {
        resource: '/api/v1/analyze',
        action: 'POST',
        requiredPermissions: [] // Public endpoint with its own validation
    },

    // Webhook endpoints (public with signature validation)
    {
        resource: '/api/v1/webhooks/freemius',
        action: 'POST',
        requiredPermissions: [] // Public endpoint with signature validation
    },

    // Data export
    {
        resource: '/api/v1/admin/export',
        action: 'GET',
        requiredPermissions: [Permission.EXPORT_DATA]
    },
    {
        resource: '/api/v1/admin/export',
        action: 'POST',
        requiredPermissions: [Permission.EXPORT_DATA]
    }
];

export interface AuthorizationOptions {
    permissions?: Permission[];
    roles?: UserRole[];
    resource?: string;
    allowOwnership?: boolean;
    customCheck?: (req: Request) => Promise<boolean>;
    onForbidden?: (req: Request, res: Response, next: NextFunction) => void;
}

/**
 * Check if user has specific permission
 */
export function hasPermission(userRole: string, permission: Permission): boolean {
    const role = userRole as UserRole;
    const rolePermissions = ROLE_PERMISSIONS[role] || [];
    return rolePermissions.includes(permission);
}

/**
 * Check if user has any of the required permissions
 */
export function hasAnyPermission(userRole: string, permissions: Permission[]): boolean {
    return permissions.some(permission => hasPermission(userRole, permission));
}

/**
 * Check if user has all required permissions
 */
export function hasAllPermissions(userRole: string, permissions: Permission[]): boolean {
    return permissions.every(permission => hasPermission(userRole, permission));
}

/**
 * Get all permissions for a role
 */
export function getRolePermissions(role: UserRole): Permission[] {
    return ROLE_PERMISSIONS[role] || [];
}

/**
 * Check role hierarchy for GuardGeo Platform (higher roles include lower role permissions)
 */
export function hasRoleOrHigher(userRole: string, minimumRole: UserRole): boolean {
    const roleHierarchy = {
        [UserRole.SUPER_ADMIN]: 4,
        [UserRole.DEV]: 3,
        [UserRole.MARKETING]: 2,
        [UserRole.SALES]: 1
    };

    const userLevel = roleHierarchy[userRole as UserRole] || 0;
    const requiredLevel = roleHierarchy[minimumRole] || 0;

    return userLevel >= requiredLevel;
}

/**
 * Find matching resource permission
 */
function findResourcePermission(path: string, method: string): ResourcePermission | null {
    return RESOURCE_PERMISSIONS.find(rp => {
        // Simple path matching - can be enhanced with path-to-regexp for complex patterns
        const resourcePattern = rp.resource.replace(/:[\w]+/g, '[^/]+');
        const regex = new RegExp(`^${resourcePattern}$`);
        return regex.test(path) && rp.action === method;
    }) || null;
}

/**
 * Authorization middleware
 */
export function authorizationMiddleware(options: AuthorizationOptions = {}) {
    const {
        permissions = [],
        roles = [],
        resource,
        allowOwnership = false,
        customCheck,
        onForbidden = (req: Request, res: Response) => {
            res.status(403).json({
                error: 'Forbidden',
                message: 'Insufficient permissions to access this resource'
            });
        }
    } = options;

    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const clientIp = getClientIp(req);
            const userAgent = req.headers['user-agent'] || '';
            const path = req.path;
            const method = req.method;

            // User must be authenticated for authorization checks
            if (!req.user) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHORIZATION_FAILED,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        reason: 'no_authenticated_user',
                        endpoint: path
                    }
                });

                return res.status(401).json({
                    error: 'Unauthorized',
                    message: 'Authentication required'
                });
            }

            const userRole = req.user.role as UserRole;
            let authorized = false;
            let authorizationReason = '';

            // Check role-based authorization
            if (roles.length > 0) {
                authorized = roles.includes(userRole);
                authorizationReason = authorized ? 'role_match' : 'insufficient_role';
            }

            // Check permission-based authorization
            if (!authorized && permissions.length > 0) {
                authorized = hasAnyPermission(userRole, permissions);
                authorizationReason = authorized ? 'permission_match' : 'insufficient_permissions';
            }

            // Check resource-specific permissions
            if (!authorized && (resource || path)) {
                const resourcePath = resource || path;
                const resourcePermission = findResourcePermission(resourcePath, method);

                if (resourcePermission) {
                    // Check required permissions for this resource
                    authorized = hasAnyPermission(userRole, resourcePermission.requiredPermissions);

                    // If not authorized by permissions, check ownership
                    if (!authorized && allowOwnership && resourcePermission.ownershipCheck) {
                        authorized = resourcePermission.ownershipCheck(req);
                        authorizationReason = authorized ? 'ownership_check' : 'not_owner';
                    }

                    // Custom resource check
                    if (!authorized && resourcePermission.customCheck) {
                        authorized = await resourcePermission.customCheck(req);
                        authorizationReason = authorized ? 'custom_check' : 'custom_check_failed';
                    }

                    if (!authorizationReason) {
                        authorizationReason = authorized ? 'resource_permission_match' : 'insufficient_resource_permissions';
                    }
                }
            }

            // Custom authorization check
            if (!authorized && customCheck) {
                authorized = await customCheck(req);
                authorizationReason = authorized ? 'custom_authorization' : 'custom_authorization_failed';
            }

            // If no specific authorization rules, allow (authentication was already checked)
            if (permissions.length === 0 && roles.length === 0 && !resource && !customCheck) {
                authorized = true;
                authorizationReason = 'no_specific_requirements';
            }

            if (!authorized) {
                // Log authorization failure
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHORIZATION_FAILED,
                    userId: req.user.id,
                    email: req.user.email,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        reason: authorizationReason,
                        user_role: userRole,
                        required_permissions: permissions,
                        required_roles: roles,
                        endpoint: path
                    }
                });

                return onForbidden(req, res, next);
            }

            // Log successful authorization
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUTHORIZATION_SUCCESS,
                userId: req.user.id,
                email: req.user.email,
                ipAddress: clientIp,
                userAgent,
                resource: path,
                action: method,
                success: true,
                details: {
                    reason: authorizationReason,
                    user_role: userRole,
                    endpoint: path
                }
            });

            next();
        } catch (error) {
            console.error('Authorization middleware error:', error);

            // Log middleware error
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'] || '',
                resource: req.path,
                action: req.method,
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown authorization error',
                details: {
                    component: 'authorization_middleware',
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            });

            return onForbidden(req, res, next);
        }
    };
}

/**
 * Pre-configured authorization middleware for GuardGeo Platform
 */
export const authorize = {
    // Super admin only
    superAdmin: authorizationMiddleware({
        roles: [UserRole.SUPER_ADMIN]
    }),

    // Dev and above (technical roles)
    dev: authorizationMiddleware({
        roles: [UserRole.SUPER_ADMIN, UserRole.DEV]
    }),

    // Marketing role access
    marketing: authorizationMiddleware({
        roles: [UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING]
    }),

    // Sales role access
    sales: authorizationMiddleware({
        roles: [UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING, UserRole.SALES]
    }),

    // User management permissions
    userManagement: authorizationMiddleware({
        permissions: [Permission.VIEW_USER, Permission.UPDATE_USER, Permission.DELETE_USER]
    }),

    // Admin user creation (super admin only)
    adminUserCreation: authorizationMiddleware({
        permissions: [Permission.CREATE_ADMIN_USER]
    }),

    // System settings access
    systemSettings: authorizationMiddleware({
        permissions: [Permission.VIEW_SYSTEM_SETTINGS, Permission.UPDATE_SYSTEM_SETTINGS]
    }),

    // Security monitoring
    securityMonitoring: authorizationMiddleware({
        permissions: [Permission.VIEW_SECURITY_METRICS, Permission.VIEW_AUDIT_LOGS]
    }),

    // Session management
    sessionManagement: authorizationMiddleware({
        permissions: [Permission.MANAGE_SESSIONS],
        allowOwnership: true
    }),

    // GuardGeo Platform specific authorizations

    // Dashboard access
    dashboard: authorizationMiddleware({
        permissions: [Permission.VIEW_DASHBOARD]
    }),

    // Freemius data access
    freemiusData: authorizationMiddleware({
        permissions: [Permission.VIEW_FREEMIUS_DATA]
    }),

    // Freemius management (dev and super admin only)
    freemiusManagement: authorizationMiddleware({
        permissions: [Permission.MANAGE_FREEMIUS_SYNC]
    }),

    // IP data access
    ipData: authorizationMiddleware({
        permissions: [Permission.VIEW_IP_DATA]
    }),

    // IP lookup functionality
    ipLookup: authorizationMiddleware({
        permissions: [Permission.PERFORM_IP_LOOKUP]
    }),

    // Analytics access
    analytics: authorizationMiddleware({
        permissions: [Permission.VIEW_ANALYTICS]
    }),

    // API logs access
    apiLogs: authorizationMiddleware({
        permissions: [Permission.VIEW_API_LOGS]
    }),

    // System health monitoring
    systemHealth: authorizationMiddleware({
        permissions: [Permission.VIEW_SYSTEM_HEALTH]
    }),

    // Data export functionality
    dataExport: authorizationMiddleware({
        permissions: [Permission.EXPORT_DATA]
    })
};

/**
 * Resource-specific authorization
 */
export function authorizeResource(resourcePath: string, allowOwnership: boolean = false) {
    return authorizationMiddleware({
        resource: resourcePath,
        allowOwnership
    });
}

/**
 * Permission-based authorization
 */
export function requirePermissions(...permissions: Permission[]) {
    return authorizationMiddleware({
        permissions
    });
}

/**
 * Role-based authorization
 */
export function requireRoles(...roles: UserRole[]) {
    return authorizationMiddleware({
        roles
    });
}

/**
 * Custom authorization check
 */
export function customAuthorization(checkFunction: (req: Request) => Promise<boolean>) {
    return authorizationMiddleware({
        customCheck: checkFunction
    });
}

/**
 * Ownership-based authorization (user can only access their own resources)
 */
export function requireOwnership(ownershipCheck: (req: Request) => boolean) {
    return authorizationMiddleware({
        allowOwnership: true,
        customCheck: async (req) => ownershipCheck(req)
    });
}

/**
 * Enhanced security monitoring is now handled by SecurityAlerting service
 */

/**
 * Enhanced authorization middleware with improved error responses and security monitoring
 */
export function enhancedAuthorizationMiddleware(options: AuthorizationOptions = {}) {
    const baseMiddleware = authorizationMiddleware(options);

    return async (req: Request, res: Response, next: NextFunction) => {
        // Wrap the base middleware to add enhanced error handling
        const originalOnForbidden = options.onForbidden || ((req: Request, res: Response) => {
            res.status(403).json({
                error: 'Forbidden',
                message: 'Insufficient permissions to access this resource'
            });
        });

        const enhancedOptions = {
            ...options,
            onForbidden: async (req: Request, res: Response, next: NextFunction) => {
                const clientIp = getClientIp(req);
                const userAgent = req.headers['user-agent'] || '';

                // Track unauthorized attempt for security monitoring
                await SecurityAlerting.trackUnauthorizedAttempt(
                    req.user?.id,
                    clientIp,
                    req.path,
                    userAgent,
                    options.roles?.join(' or '),
                    options.permissions?.map(p => p.toString())
                );

                // Enhanced error response with more context
                const errorResponse = {
                    error: 'Forbidden',
                    message: 'Insufficient permissions to access this resource',
                    code: 'INSUFFICIENT_PERMISSIONS',
                    resource: req.path,
                    method: req.method,
                    timestamp: new Date().toISOString(),
                    requestId: req.headers['x-request-id'] || `req_${Date.now()}`
                };

                // Add role information if user is authenticated
                if (req.user) {
                    (errorResponse as any).userRole = req.user.role;
                    (errorResponse as any).requiredPermissions = options.permissions || [];
                    (errorResponse as any).requiredRoles = options.roles || [];
                }

                res.status(403).json(errorResponse);
            }
        };

        // Create new middleware with enhanced options
        const enhancedMiddleware = authorizationMiddleware(enhancedOptions);
        return enhancedMiddleware(req, res, next);
    };
}

/**
 * Cleanup is now handled by SecurityAlerting service
 */
export function cleanupUnauthorizedAttempts(): void {
    // This function is kept for backward compatibility but cleanup
    // is now handled by the SecurityAlerting service
}