import { Request, Response, NextFunction } from 'express';
import {
    auth,
    rateLimitMiddleware,
    csrf,
    validate,
    Permission,
    UserRole,
    requireRoles,
    requirePermissions,
    customAuthorization
} from './index';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { getClientIp } from './rate-limit-middleware';

/**
 * Route protection configurations for different endpoint types
 */
export interface RouteProtectionConfig {
    authentication: 'none' | 'optional' | 'required';
    authorization?: {
        roles?: UserRole[];
        permissions?: Permission[];
        allowOwnership?: boolean;
        customCheck?: (req: Request) => Promise<boolean>;
    };
    rateLimit: 'none' | 'general' | 'auth' | 'admin' | 'passwordReset' | 'strict';
    csrf: 'none' | 'web' | 'api' | 'admin';
    validation?: string; // validation schema name
    customMiddleware?: Array<(req: Request, res: Response, next: NextFunction) => void>;
}

/**
 * Pre-defined route protection configurations
 */
export const routeProtections = {
    // Public routes (no protection)
    public: {
        authentication: 'none' as const,
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // Authentication routes
    login: {
        authentication: 'none' as const,
        rateLimit: 'auth' as const,
        csrf: 'none' as const,
        validation: 'login'
    },

    logout: {
        authentication: 'optional' as const,
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    passwordReset: {
        authentication: 'none' as const,
        rateLimit: 'passwordReset' as const,
        csrf: 'none' as const,
        validation: 'passwordResetRequest'
    },

    // User profile routes
    userProfile: {
        authentication: 'required' as const,
        rateLimit: 'general' as const,
        csrf: 'web' as const,
        authorization: {
            allowOwnership: true
        }
    },

    changePassword: {
        authentication: 'required' as const,
        rateLimit: 'general' as const,
        csrf: 'web' as const,
        validation: 'changePassword'
    },

    // Admin user management
    adminUserCreation: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.CREATE_ADMIN_USER]
        },
        rateLimit: 'admin' as const,
        csrf: 'admin' as const,
        validation: 'createAdminUser'
    },

    userManagement: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_USER, Permission.UPDATE_USER]
        },
        rateLimit: 'admin' as const,
        csrf: 'web' as const
    },

    // Session management
    sessionManagement: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.MANAGE_SESSIONS],
            allowOwnership: true
        },
        rateLimit: 'general' as const,
        csrf: 'web' as const
    },

    // System settings
    systemSettings: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_SYSTEM_SETTINGS, Permission.UPDATE_SYSTEM_SETTINGS]
        },
        rateLimit: 'admin' as const,
        csrf: 'admin' as const
    },

    // Security monitoring
    securityMonitoring: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_SECURITY_METRICS, Permission.VIEW_AUDIT_LOGS]
        },
        rateLimit: 'admin' as const,
        csrf: 'none' as const
    },

    // Super admin only routes
    superAdminOnly: {
        authentication: 'required' as const,
        authorization: {
            roles: [UserRole.SUPER_ADMIN]
        },
        rateLimit: 'strict' as const,
        csrf: 'admin' as const
    },

    // Dev and above routes (technical roles)
    devAndAbove: {
        authentication: 'required' as const,
        authorization: {
            roles: [UserRole.SUPER_ADMIN, UserRole.DEV]
        },
        rateLimit: 'admin' as const,
        csrf: 'admin' as const
    },

    // Marketing and above routes
    marketingAndAbove: {
        authentication: 'required' as const,
        authorization: {
            roles: [UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING]
        },
        rateLimit: 'general' as const,
        csrf: 'web' as const
    },

    // Sales and above routes (all authenticated users)
    salesAndAbove: {
        authentication: 'required' as const,
        authorization: {
            roles: [UserRole.SUPER_ADMIN, UserRole.DEV, UserRole.MARKETING, UserRole.SALES]
        },
        rateLimit: 'general' as const,
        csrf: 'web' as const
    },

    // GuardGeo Platform specific route protections

    // Dashboard access (all authenticated users)
    dashboard: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_DASHBOARD]
        },
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // Freemius data access
    freemiusData: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_FREEMIUS_DATA]
        },
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // Freemius management (dev and super admin only)
    freemiusManagement: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.MANAGE_FREEMIUS_SYNC]
        },
        rateLimit: 'admin' as const,
        csrf: 'admin' as const
    },

    // IP data access
    ipData: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_IP_DATA]
        },
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // IP lookup functionality
    ipLookup: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.PERFORM_IP_LOOKUP]
        },
        rateLimit: 'admin' as const,
        csrf: 'admin' as const
    },

    // Analytics access
    analytics: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_ANALYTICS]
        },
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // API logs access
    apiLogs: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_API_LOGS]
        },
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // System health monitoring
    systemHealth: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.VIEW_SYSTEM_HEALTH]
        },
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // Data export functionality
    dataExport: {
        authentication: 'required' as const,
        authorization: {
            permissions: [Permission.EXPORT_DATA]
        },
        rateLimit: 'admin' as const,
        csrf: 'admin' as const
    },

    // WordPress Plugin API (public endpoint with its own validation)
    pluginApi: {
        authentication: 'none' as const,
        rateLimit: 'general' as const,
        csrf: 'none' as const
    },

    // Webhook endpoints (public with signature validation)
    webhook: {
        authentication: 'none' as const,
        rateLimit: 'general' as const,
        csrf: 'none' as const
    }
};

/**
 * Apply route protection middleware based on configuration
 */
export function applyRouteProtection(config: RouteProtectionConfig) {
    const middlewares: Array<(req: Request, res: Response, next: NextFunction) => void> = [];

    // 1. Rate limiting (should be first)
    if (config.rateLimit !== 'none') {
        switch (config.rateLimit) {
            case 'general':
                middlewares.push(rateLimitMiddleware.general);
                break;
            case 'auth':
                middlewares.push(rateLimitMiddleware.auth);
                break;
            case 'admin':
                middlewares.push(rateLimitMiddleware.admin);
                break;
            case 'passwordReset':
                middlewares.push(rateLimitMiddleware.passwordReset);
                break;
            case 'strict':
                middlewares.push(rateLimitMiddleware.strict);
                break;
        }
    }

    // 2. Authentication
    if (config.authentication !== 'none') {
        switch (config.authentication) {
            case 'optional':
                middlewares.push(auth.optional);
                break;
            case 'required':
                middlewares.push(auth.required);
                break;
        }
    }

    // 3. Authorization
    if (config.authorization) {
        if (config.authorization.roles) {
            middlewares.push(requireRoles(...config.authorization.roles));
        } else if (config.authorization.permissions) {
            middlewares.push(requirePermissions(...config.authorization.permissions));
        }

        if (config.authorization.customCheck) {
            middlewares.push(customAuthorization(config.authorization.customCheck));
        }
    }

    // 4. CSRF protection
    if (config.csrf !== 'none') {
        switch (config.csrf) {
            case 'web':
                middlewares.push(csrf.web);
                break;
            case 'api':
                middlewares.push(csrf.api);
                break;
            case 'admin':
                middlewares.push(csrf.admin);
                break;
        }
    }

    // 5. Input validation
    if (config.validation) {
        const validationMiddleware = (validate as any)[config.validation];
        if (validationMiddleware) {
            middlewares.push(validationMiddleware);
        }
    }

    // 6. Custom middleware
    if (config.customMiddleware) {
        middlewares.push(...config.customMiddleware);
    }

    return middlewares;
}

/**
 * Create route protection middleware from predefined configuration
 */
export function protectRoute(protectionType: keyof typeof routeProtections) {
    const config = routeProtections[protectionType];
    return applyRouteProtection(config);
}

/**
 * Hierarchical permission middleware that logs access attempts
 */
export function hierarchicalPermissionCheck(
    minimumRole: UserRole,
    requiredPermissions: Permission[] = []
) {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const clientIp = getClientIp(req);
            const userAgent = req.headers['user-agent'] || '';
            const path = req.path;
            const method = req.method;

            if (!req.user) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHORIZATION_FAILED,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        reason: 'no_authenticated_user',
                        required_role: minimumRole,
                        required_permissions: requiredPermissions,
                        endpoint: path
                    }
                });

                return res.status(401).json({
                    error: 'Unauthorized',
                    message: 'Authentication required'
                });
            }

            const userRole = req.user.role as UserRole;

            // Check role hierarchy for GuardGeo Platform
            const roleHierarchy = {
                [UserRole.SUPER_ADMIN]: 4,
                [UserRole.DEV]: 3,
                [UserRole.MARKETING]: 2,
                [UserRole.SALES]: 1
            };

            const userLevel = roleHierarchy[userRole] || 0;
            const requiredLevel = roleHierarchy[minimumRole] || 0;

            const hasRequiredRole = userLevel >= requiredLevel;

            // Check specific permissions if provided
            let hasRequiredPermissions = true;
            if (requiredPermissions.length > 0) {
                const { hasAnyPermission } = await import('./authorization-middleware');
                hasRequiredPermissions = hasAnyPermission(userRole, requiredPermissions);
            }

            if (!hasRequiredRole || !hasRequiredPermissions) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHORIZATION_FAILED,
                    userId: req.user.id,
                    email: req.user.email,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        reason: !hasRequiredRole ? 'insufficient_role' : 'insufficient_permissions',
                        user_role: userRole,
                        user_level: userLevel,
                        required_role: minimumRole,
                        required_level: requiredLevel,
                        required_permissions: requiredPermissions,
                        endpoint: path
                    }
                });

                return res.status(403).json({
                    error: 'Forbidden',
                    message: 'Insufficient permissions to access this resource'
                });
            }

            // Log successful authorization
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUTHORIZATION_SUCCESS,
                userId: req.user.id,
                email: req.user.email,
                ipAddress: clientIp,
                userAgent,
                resource: path,
                action: method,
                success: true,
                details: {
                    user_role: userRole,
                    user_level: userLevel,
                    required_role: minimumRole,
                    required_level: requiredLevel,
                    endpoint: path
                }
            });

            next();
        } catch (error) {
            console.error('Hierarchical permission check error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'] || '',
                resource: req.path,
                action: req.method,
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown permission check error',
                details: {
                    component: 'hierarchical_permission_check',
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            });

            return res.status(500).json({
                error: 'Internal Server Error',
                message: 'Permission check failed'
            });
        }
    };
}

/**
 * Resource ownership validation middleware
 */
export function validateResourceOwnership(
    resourceIdParam: string = 'id'
) {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const clientIp = getClientIp(req);
            const userAgent = req.headers['user-agent'] || '';
            const path = req.path;
            const method = req.method;

            if (!req.user) {
                return res.status(401).json({
                    error: 'Unauthorized',
                    message: 'Authentication required'
                });
            }

            const resourceId = req.params[resourceIdParam];
            const userId = req.user.id;

            // For user resources, check if the user is accessing their own resource
            if (resourceIdParam === 'id' && resourceId === userId) {
                return next();
            }

            // For other resources, you would typically check database ownership
            // This is a simplified version - in practice, you'd query the database
            // to check if the user owns the resource

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUTHORIZATION_FAILED,
                userId: req.user.id,
                email: req.user.email,
                ipAddress: clientIp,
                userAgent,
                resource: path,
                action: method,
                success: false,
                details: {
                    reason: 'resource_ownership_check_failed',
                    resource_id: resourceId,
                    user_id: userId,
                    endpoint: path
                }
            });

            return res.status(403).json({
                error: 'Forbidden',
                message: 'You can only access your own resources'
            });
        } catch (error) {
            console.error('Resource ownership validation error:', error);
            return res.status(500).json({
                error: 'Internal Server Error',
                message: 'Ownership validation failed'
            });
        }
    };
}

/**
 * Permission denial logging middleware
 */
export function logPermissionDenial() {
    return async (req: Request, res: Response, next: NextFunction) => {
        // Override the response methods to log permission denials
        const originalStatus = res.status;

        res.status = function (code: number) {
            if (code === 403 || code === 401) {
                // Log permission denial
                SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHORIZATION_FAILED,
                    userId: req.user?.id,
                    email: req.user?.email,
                    ipAddress: getClientIp(req),
                    userAgent: req.headers['user-agent'] || '',
                    resource: req.path,
                    action: req.method,
                    success: false,
                    details: {
                        reason: 'permission_denied',
                        status_code: code,
                        endpoint: req.path
                    }
                }).catch(console.error);
            }
            return originalStatus.call(this, code);
        };

        next();
    };
}

/**
 * Export convenience functions for common protection patterns
 */
export const protect = {
    // Basic authentication and user management
    public: () => protectRoute('public'),
    login: () => protectRoute('login'),
    logout: () => protectRoute('logout'),
    passwordReset: () => protectRoute('passwordReset'),
    userProfile: () => protectRoute('userProfile'),
    changePassword: () => protectRoute('changePassword'),
    adminUserCreation: () => protectRoute('adminUserCreation'),
    userManagement: () => protectRoute('userManagement'),
    sessionManagement: () => protectRoute('sessionManagement'),
    systemSettings: () => protectRoute('systemSettings'),
    securityMonitoring: () => protectRoute('securityMonitoring'),

    // Role-based protection for GuardGeo Platform
    superAdminOnly: () => protectRoute('superAdminOnly'),
    devAndAbove: () => protectRoute('devAndAbove'),
    marketingAndAbove: () => protectRoute('marketingAndAbove'),
    salesAndAbove: () => protectRoute('salesAndAbove'),

    // GuardGeo Platform specific protections
    dashboard: () => protectRoute('dashboard'),
    freemiusData: () => protectRoute('freemiusData'),
    freemiusManagement: () => protectRoute('freemiusManagement'),
    ipData: () => protectRoute('ipData'),
    ipLookup: () => protectRoute('ipLookup'),
    analytics: () => protectRoute('analytics'),
    apiLogs: () => protectRoute('apiLogs'),
    systemHealth: () => protectRoute('systemHealth'),
    dataExport: () => protectRoute('dataExport'),
    pluginApi: () => protectRoute('pluginApi'),
    webhook: () => protectRoute('webhook'),

    // Hierarchical protection
    requireRole: (role: UserRole) => hierarchicalPermissionCheck(role),
    requirePermissions: (permissions: Permission[]) => hierarchicalPermissionCheck(UserRole.SALES, permissions),
    requireRoleAndPermissions: (role: UserRole, permissions: Permission[]) =>
        hierarchicalPermissionCheck(role, permissions),

    // Ownership protection
    ownResource: (resourceIdParam?: string) => validateResourceOwnership(resourceIdParam),

    // Custom protection
    custom: (config: RouteProtectionConfig) => applyRouteProtection(config)
};