import { Request, Response, NextFunction } from 'express';
import { rateLimitHelpers, rateLimitConfigs, RateLimitResult } from '../lib/rate-limiter';

// Extend Express Request interface to include rate limit info
declare global {
    namespace Express {
        interface Request {
            rateLimit?: {
                limit: number;
                remaining: number;
                reset: Date;
                retryAfter?: number;
            };
            clientIp?: string;
        }
    }
}

export interface RateLimitMiddlewareOptions {
    windowMs?: number;
    maxRequests?: number;
    keyGenerator?: (req: Request) => string;
    skipSuccessfulRequests?: boolean;
    skipFailedRequests?: boolean;
    onLimitReached?: (req: Request, res: Response) => void;
    standardHeaders?: boolean; // Add standard rate limit headers
    legacyHeaders?: boolean; // Add legacy X-RateLimit headers
}

/**
 * Get client IP address from request
 */
function getClientIp(req: Request): string {
    // Check various headers for the real IP
    const forwarded = req.headers['x-forwarded-for'];
    const realIp = req.headers['x-real-ip'];
    const cfConnectingIp = req.headers['cf-connecting-ip']; // Cloudflare

    if (typeof forwarded === 'string') {
        // X-Forwarded-For can contain multiple IPs, take the first one
        return forwarded.split(',')[0].trim();
    }

    if (typeof realIp === 'string') {
        return realIp;
    }

    if (typeof cfConnectingIp === 'string') {
        return cfConnectingIp;
    }

    // Fallback to connection remote address
    return req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        (req.connection as any)?.socket?.remoteAddress ||
        '127.0.0.1';
}

/**
 * Add rate limit headers to response
 */
function addRateLimitHeaders(
    res: Response,
    result: RateLimitResult,
    maxRequests: number,
    options: RateLimitMiddlewareOptions
): void {
    if (options.standardHeaders !== false) {
        // Standard rate limit headers (RFC 6585)
        res.set('RateLimit-Limit', maxRequests.toString());
        res.set('RateLimit-Remaining', result.remaining.toString());
        res.set('RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000).toString());

        if (result.retryAfter) {
            res.set('Retry-After', result.retryAfter.toString());
        }
    }

    if (options.legacyHeaders !== false) {
        // Legacy X-RateLimit headers for backward compatibility
        res.set('X-RateLimit-Limit', maxRequests.toString());
        res.set('X-RateLimit-Remaining', result.remaining.toString());
        res.set('X-RateLimit-Reset', Math.ceil(result.resetTime.getTime() / 1000).toString());
    }
}

/**
 * Create rate limiting middleware
 */
export function createRateLimitMiddleware(options: RateLimitMiddlewareOptions = {}) {
    const {
        windowMs = 60 * 1000, // 1 minute default
        maxRequests = 100,
        keyGenerator = (req: Request) => getClientIp(req),
        skipSuccessfulRequests = false,
        skipFailedRequests = false,
        onLimitReached,
        standardHeaders = true,
        legacyHeaders = true
    } = options;

    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const key = keyGenerator(req);
            const clientIp = getClientIp(req);

            // Store client IP in request for other middleware
            req.clientIp = clientIp;

            // Check rate limit
            const result = await rateLimitHelpers.checkGeneralLimit(clientIp);

            // Add rate limit info to request
            req.rateLimit = {
                limit: maxRequests,
                remaining: result.remaining,
                reset: result.resetTime,
                retryAfter: result.retryAfter
            };

            // Add headers
            addRateLimitHeaders(res, result, maxRequests, options);

            if (!result.allowed) {
                // Rate limit exceeded
                if (onLimitReached) {
                    onLimitReached(req, res);
                }

                return res.status(429).json({
                    error: 'Too Many Requests',
                    message: 'Rate limit exceeded. Please try again later.',
                    retryAfter: result.retryAfter
                });
            }

            // Record the request if not skipping
            if (!skipSuccessfulRequests && !skipFailedRequests) {
                await rateLimitHelpers.recordGeneralRequest(clientIp);
            } else {
                // Handle conditional recording based on response
                const originalSend = res.send;
                res.send = function (body) {
                    const statusCode = res.statusCode;
                    const isSuccess = statusCode >= 200 && statusCode < 400;
                    const shouldRecord = (!skipSuccessfulRequests && isSuccess) ||
                        (!skipFailedRequests && !isSuccess);

                    if (shouldRecord) {
                        rateLimitHelpers.recordGeneralRequest(clientIp).catch(console.error);
                    }

                    return originalSend.call(this, body);
                };
            }

            next();
        } catch (error) {
            console.error('Rate limiting middleware error:', error);
            // In case of error, allow the request to proceed
            next();
        }
    };
}

/**
 * Pre-configured rate limiting middleware for different endpoints
 */
export const rateLimitMiddleware = {
    // General API rate limiting: 100 requests per minute
    general: createRateLimitMiddleware({
        windowMs: rateLimitConfigs.general.windowMs,
        maxRequests: rateLimitConfigs.general.maxRequests,
        keyGenerator: (req: Request) => rateLimitConfigs.general.keyGenerator(getClientIp(req))
    }),

    // Authentication endpoints: 10 requests per minute
    auth: createRateLimitMiddleware({
        windowMs: rateLimitConfigs.auth.windowMs,
        maxRequests: rateLimitConfigs.auth.maxRequests,
        keyGenerator: (req: Request) => rateLimitConfigs.auth.keyGenerator(getClientIp(req)),
        onLimitReached: (req: Request, res: Response) => {
            console.warn(`Authentication rate limit exceeded for IP: ${getClientIp(req)}`);
        }
    }),

    // Admin operations: 50 requests per minute
    admin: createRateLimitMiddleware({
        windowMs: rateLimitConfigs.admin.windowMs,
        maxRequests: rateLimitConfigs.admin.maxRequests,
        keyGenerator: (req: Request) => rateLimitConfigs.admin.keyGenerator(getClientIp(req))
    }),

    // Password reset: 3 requests per hour
    passwordReset: createRateLimitMiddleware({
        windowMs: rateLimitConfigs.passwordReset.windowMs,
        maxRequests: rateLimitConfigs.passwordReset.maxRequests,
        keyGenerator: (req: Request) => rateLimitConfigs.passwordReset.keyGenerator(getClientIp(req)),
        onLimitReached: (req: Request, res: Response) => {
            console.warn(`Password reset rate limit exceeded for IP: ${getClientIp(req)}`);
        }
    }),

    // Strict rate limiting for sensitive operations
    strict: createRateLimitMiddleware({
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 5,
        keyGenerator: (req: Request) => `strict:${getClientIp(req)}`,
        onLimitReached: (req: Request, res: Response) => {
            console.warn(`Strict rate limit exceeded for IP: ${getClientIp(req)}`);
        }
    })
};

// Helper function to create custom rate limiters
export function createCustomRateLimit(
    windowMs: number,
    maxRequests: number,
    keyPrefix: string = 'custom'
) {
    return createRateLimitMiddleware({
        windowMs,
        maxRequests,
        keyGenerator: (req: Request) => `${keyPrefix}:${getClientIp(req)}`
    });
}

// Export utility functions
export { getClientIp };