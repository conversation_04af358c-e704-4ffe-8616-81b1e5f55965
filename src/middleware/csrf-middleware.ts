// Express types - install @types/express when adding Express.js backend
interface Request {
    headers: Record<string, string | string[] | undefined>;
    body?: any;
    query?: any;
    params?: any;
    path: string;
    method: string;
    user?: {
        id: string;
        email: string;
        role: string;
    };
    csrfToken?: () => string;
    clientIp?: string;
}

interface Response {
    status(code: number): Response;
    json(data: any): Response;
    set(name: string, value: string): Response;
    cookie(name: string, value: string, options?: any): Response;
}

interface NextFunction {
    (error?: any): void;
}

import { randomBytes, createHash, timingSafeEqual } from 'crypto';
import { prisma } from '../lib/prisma';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { getClientIp } from './rate-limit-middleware';

export interface CSRFOptions {
    tokenLength?: number;
    headerName?: string;
    bodyField?: string;
    queryField?: string;
    tokenExpiration?: number;
    protectedMethods?: string[];
    skipPaths?: string[];
    onError?: (req: Request, res: Response, next: NextFunction) => void;
    cookie?: {
        name?: string;
        httpOnly?: boolean;
        secure?: boolean;
        sameSite?: 'strict' | 'lax' | 'none';
        maxAge?: number;
    };
}

const defaultCSRFOptions: Required<CSRFOptions> = {
    tokenLength: 32,
    headerName: 'x-csrf-token',
    bodyField: '_csrf',
    queryField: '_csrf',
    tokenExpiration: 60 * 60 * 1000,
    protectedMethods: ['POST', 'PUT', 'PATCH', 'DELETE'],
    skipPaths: ['/api/auth/login', '/api/health'],
    onError: (_req: Request, res: Response) => {
        res.status(403).json({
            error: 'Forbidden',
            message: 'Invalid CSRF token'
        });
    },
    cookie: {
        name: 'csrf-token',
        httpOnly: false,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 60 * 60 * 1000
    }
};

function generateToken(length: number): string {
    return randomBytes(length).toString('hex');
}

function generateCSRFToken(secret: string, tokenLength: number): string {
    const token = generateToken(tokenLength);
    const hash = createHash('sha256').update(secret + token).digest('hex');
    return `${token}.${hash}`;
}

function verifyCSRFToken(token: string, secret: string): boolean {
    try {
        const [tokenPart, hashPart] = token.split('.');
        if (!tokenPart || !hashPart) {
            return false;
        }

        const expectedHash = createHash('sha256').update(secret + tokenPart).digest('hex');
        const expectedBuffer = Buffer.from(expectedHash, 'hex');
        const actualBuffer = Buffer.from(hashPart, 'hex');

        if (expectedBuffer.length !== actualBuffer.length) {
            return false;
        }

        return timingSafeEqual(expectedBuffer, actualBuffer);
    } catch {
        return false;
    }
}

async function getOrCreateCSRFSecret(userId: string): Promise<string> {
    try {
        const existingToken = await prisma.csrfToken.findFirst({
            where: {
                user_id: userId,
                expires_at: { gt: new Date() }
            },
            orderBy: { created_at: 'desc' }
        });

        if (existingToken) {
            return existingToken.token;
        }

        const secret = generateToken(32);
        const expiresAt = new Date(Date.now() + 60 * 60 * 1000);

        await prisma.csrfToken.create({
            data: {
                token: secret,
                user_id: userId,
                expires_at: expiresAt
            }
        });

        return secret;
    } catch (error) {
        console.error('Error managing CSRF secret:', error);
        return generateToken(32);
    }
}

function extractTokenFromRequest(req: Request, options: Required<CSRFOptions>): string | null {
    const headerToken = req.headers[options.headerName.toLowerCase()] as string;
    if (headerToken) return headerToken;

    if (req.body && req.body[options.bodyField]) {
        return req.body[options.bodyField];
    }

    if (req.query && req.query[options.queryField]) {
        return req.query[options.queryField] as string;
    }

    return null;
}

function shouldSkipPath(path: string, skipPaths: string[]): boolean {
    return skipPaths.some(skipPath => {
        if (skipPath.includes('*')) {
            const pattern = skipPath.replace(/\*/g, '.*');
            return new RegExp(`^${pattern}$`).test(path);
        }
        return path === skipPath;
    });
}

export function csrfProtection(options: CSRFOptions = {}) {
    const config = { ...defaultCSRFOptions, ...options };
    config.cookie = { ...defaultCSRFOptions.cookie, ...options.cookie };

    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const clientIp = getClientIp(req);
            const method = req.method.toUpperCase();
            const path = req.path;
            const userAgent = req.headers['user-agent'] as string || '';

            if (shouldSkipPath(path, config.skipPaths)) {
                return next();
            }

            if (!config.protectedMethods.includes(method)) {
                if (req.user?.id) {
                    const secret = await getOrCreateCSRFSecret(req.user.id);
                    req.csrfToken = () => generateCSRFToken(secret, config.tokenLength);

                    const token = req.csrfToken();
                    res.cookie(config.cookie.name || 'csrf-token', token, {
                        httpOnly: config.cookie.httpOnly,
                        secure: config.cookie.secure,
                        sameSite: config.cookie.sameSite,
                        maxAge: config.cookie.maxAge
                    });
                }
                return next();
            }

            if (!req.user?.id) {
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.CSRF_TOKEN_INVALID,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        violationType: 'csrf_no_user',
                        endpoint: path
                    }
                });

                return config.onError(req, res, next);
            }

            const providedToken = extractTokenFromRequest(req, config);
            if (!providedToken) {
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.CSRF_TOKEN_INVALID,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        violationType: 'csrf_missing_token',
                        endpoint: path
                    }
                });

                return config.onError(req, res, next);
            }

            const secret = await getOrCreateCSRFSecret(req.user.id);

            if (!verifyCSRFToken(providedToken, secret)) {
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.CSRF_TOKEN_INVALID,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        violationType: 'csrf_invalid_token',
                        endpoint: path,
                        providedToken: providedToken.substring(0, 8) + '...'
                    }
                });

                return config.onError(req, res, next);
            }

            req.csrfToken = () => generateCSRFToken(secret, config.tokenLength);

            if (Math.random() < 0.01) {
                prisma.csrfToken.deleteMany({
                    where: { expires_at: { lt: new Date() } }
                }).catch(console.error);
            }

            next();
        } catch (error) {
            console.error('CSRF middleware error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'] as string || '',
                resource: req.path,
                action: req.method,
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown CSRF error',
                details: {
                    component: 'csrf_middleware',
                    error: error instanceof Error ? error.message : 'Unknown error'
                }
            });

            return config.onError(req, res, next);
        }
    };
}

export function csrfTokenProvider() {
    return (req: Request, res: Response, next: NextFunction) => {
        if (req.csrfToken) {
            res.set('X-CSRF-Token', req.csrfToken());
        }
        next();
    };
}

export const csrf = {
    web: csrfProtection({
        headerName: 'x-csrf-token',
        bodyField: '_csrf',
        cookie: {
            name: 'csrf-token',
            httpOnly: false,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict'
        }
    }),

    api: csrfProtection({
        headerName: 'x-csrf-token',
        bodyField: undefined,
        queryField: undefined,
        cookie: {
            name: 'api-csrf-token',
            httpOnly: false,
            secure: process.env.NODE_ENV === 'production',
            sameSite: 'strict'
        }
    }),

    admin: csrfProtection({
        headerName: 'x-admin-csrf-token',
        tokenExpiration: 30 * 60 * 1000,
        protectedMethods: ['POST', 'PUT', 'PATCH', 'DELETE'],
        skipPaths: [],
        cookie: {
            name: 'admin-csrf-token',
            httpOnly: false,
            secure: true,
            sameSite: 'strict',
            maxAge: 30 * 60 * 1000
        }
    })
};

export async function validateCSRFToken(token: string, userId: string): Promise<boolean> {
    try {
        const secret = await getOrCreateCSRFSecret(userId);
        return verifyCSRFToken(token, secret);
    } catch {
        return false;
    }
}

export async function generateCSRFTokenForUser(userId: string): Promise<string> {
    const secret = await getOrCreateCSRFSecret(userId);
    return generateCSRFToken(secret, 32);
}