import { Request, Response, NextFunction } from 'express';
import { sessionManager } from '../lib/session-manager';
import { PrismaSessionStore } from '../lib/prisma-session-store';
import { SessionValidator, validateSession, ActivityTrackingData } from '../lib/session-validator';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { getClientIp } from './rate-limit-middleware';
import { Logger } from '../lib/logger';

// Extend Express Request interface to include user and session info
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                firstName: string;
                lastName: string;
                role: string;
                isActive: boolean;
                requiresPasswordChange: boolean;
            };
            session?: {
                sessionToken: string;
                userId: string;
                expiresAt: Date;
                ipAddress: string;
                userAgent?: string;
                deviceFingerprint?: string;
                location?: string;
                securityFlags?: Record<string, any>;
            };
            clientIp?: string;
        }
    }
}

export interface AuthMiddlewareOptions {
    required?: boolean; // Whether authentication is required
    roles?: string[]; // Required roles for access
    skipPaths?: string[]; // Paths to skip authentication
    onUnauthorized?: (req: Request, res: Response, next: NextFunction) => void;
    onForbidden?: (req: Request, res: Response, next: NextFunction) => void;
}

/**
 * Extract session token from request
 */
function extractSessionToken(req: Request): string | null {
    // Check Authorization header (Bearer token)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }

    // Check session cookie
    const sessionCookie = req.cookies?.['session-token'];
    if (sessionCookie) {
        return sessionCookie;
    }

    // Check custom header
    const sessionHeader = req.headers['x-session-token'] as string;
    if (sessionHeader) {
        return sessionHeader;
    }

    return null;
}

/**
 * Check if path should be skipped
 */
function shouldSkipPath(path: string, skipPaths: string[]): boolean {
    return skipPaths.some(skipPath => {
        if (skipPath.includes('*')) {
            const pattern = skipPath.replace(/\*/g, '.*');
            return new RegExp(`^${pattern}$`).test(path);
        }
        return path === skipPath;
    });
}

/**
 * Enhanced authentication middleware with PrismaSessionStore and SessionValidator integration
 */
export function authMiddleware(options: AuthMiddlewareOptions = {}) {
    const {
        required = true,
        roles = [],
        skipPaths = ['/api/auth/login', '/api/health'],
        onUnauthorized = (req: Request, res: Response) => {
            res.status(401).json({
                error: 'Unauthorized',
                message: 'Authentication required'
            });
        },
        onForbidden = (req: Request, res: Response) => {
            res.status(403).json({
                error: 'Forbidden',
                message: 'Insufficient permissions'
            });
        }
    } = options;

    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const clientIp = getClientIp(req);
            const userAgent = req.headers['user-agent'] || '';
            const path = req.path;
            const method = req.method;
            const requestId = req.headers['x-request-id'] as string || `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            // Store client IP in request
            req.clientIp = clientIp;

            // Skip authentication for specified paths
            if (shouldSkipPath(path, skipPaths)) {
                return next();
            }

            // Extract session token
            const sessionToken = extractSessionToken(req);

            if (!sessionToken) {
                if (!required) {
                    return next();
                }

                // Log unauthorized access attempt
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHENTICATION_FAILED,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        reason: 'no_session_token',
                        endpoint: path,
                        requestId
                    }
                });

                return onUnauthorized(req, res, next);
            }

            // Prepare activity tracking data
            const activityData: ActivityTrackingData = {
                endpoint: path,
                method: method,
                userAgent: userAgent,
                ipAddress: clientIp,
                requestId: requestId,
                metadata: {
                    referer: req.headers.referer,
                    acceptLanguage: req.headers['accept-language'],
                    contentType: req.headers['content-type']
                }
            };

            // Validate session using enhanced SessionValidator
            const validationResult = await validateSession(sessionToken, clientIp, undefined, activityData);

            if (!validationResult.isValid) {
                // Log failed session validation with detailed information
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.SESSION_INVALID,
                    userId: validationResult.user?.id,
                    email: validationResult.user?.email,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    errorMessage: validationResult.securityAlert,
                    details: {
                        reason: validationResult.securityAlert || 'session_validation_failed',
                        endpoint: path,
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        validationErrors: validationResult.validationErrors,
                        securityEvents: validationResult.securityEvents,
                        requestId
                    }
                });

                if (!required) {
                    return next();
                }

                // Return appropriate error response based on validation result
                if (validationResult.securityAlert?.includes('expired')) {
                    return res.status(401).json({
                        error: 'Session Expired',
                        message: 'Your session has expired. Please log in again.',
                        code: 'SESSION_EXPIRED'
                    });
                } else if (validationResult.securityAlert?.includes('IP address change')) {
                    return res.status(401).json({
                        error: 'Security Alert',
                        message: 'Session invalidated due to security concerns. Please log in again.',
                        code: 'SECURITY_VIOLATION'
                    });
                } else {
                    return onUnauthorized(req, res, next);
                }
            }

            // Attach enhanced user and session data to request
            req.user = {
                id: validationResult.user!.id,
                email: validationResult.user!.email,
                firstName: validationResult.user!.first_name,
                lastName: validationResult.user!.last_name,
                role: validationResult.user!.role,
                isActive: validationResult.user!.is_active,
                requiresPasswordChange: validationResult.user!.requires_password_change
            };

            req.session = validationResult.session;

            // Check role-based access if roles are specified
            if (roles.length > 0 && !roles.includes(req.user.role)) {
                // Log authorization failure
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.AUTHORIZATION_FAILED,
                    userId: req.user.id,
                    email: req.user.email,
                    ipAddress: clientIp,
                    userAgent,
                    resource: path,
                    action: method,
                    success: false,
                    details: {
                        reason: 'insufficient_role',
                        required_roles: roles,
                        user_role: req.user.role,
                        endpoint: path,
                        requestId
                    }
                });

                return onForbidden(req, res, next);
            }

            // Check if user requires password change
            if (req.user.requiresPasswordChange && !path.includes('/auth/change-password')) {
                return res.status(403).json({
                    error: 'Password Change Required',
                    message: 'You must change your password before accessing other resources',
                    requiresPasswordChange: true,
                    code: 'PASSWORD_CHANGE_REQUIRED'
                });
            }

            // Log successful authentication with enhanced details
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUTHENTICATION_SUCCESS,
                userId: req.user.id,
                email: req.user.email,
                ipAddress: clientIp,
                userAgent,
                resource: path,
                action: method,
                success: true,
                details: {
                    endpoint: path,
                    user_role: req.user.role,
                    sessionAge: validationResult.session ?
                        Date.now() - validationResult.session.createdAt.getTime() : 0,
                    requiresRefresh: validationResult.requiresRefresh,
                    requestId
                }
            });

            // Handle session refresh if needed
            if (validationResult.requiresRefresh) {
                try {
                    const sessionValidator = new SessionValidator();
                    const refreshResult = await sessionValidator.renewSession(sessionToken, {
                        extendIdleTime: true,
                        extendAbsoluteTime: true,
                        updateLastActivity: true,
                        logRenewal: true
                    });

                    if (refreshResult.success && refreshResult.newExpiresAt) {
                        // Update session cookie with new expiration
                        res.cookie('session-token', sessionToken, {
                            httpOnly: true,
                            secure: process.env.NODE_ENV === 'production',
                            sameSite: 'strict',
                            expires: refreshResult.newExpiresAt,
                            path: '/'
                        });

                        Logger.info('Session refreshed during authentication', {
                            userId: req.user.id,
                            sessionToken: sessionToken.substring(0, 8) + '...',
                            newExpiresAt: refreshResult.newExpiresAt
                        });
                    }
                } catch (error) {
                    Logger.error('Session refresh error during authentication', error, {
                        userId: req.user.id,
                        sessionToken: sessionToken.substring(0, 8) + '...'
                    });
                    // Continue without refresh - session is still valid
                }
            }

            // Log security events if any were detected during validation
            if (validationResult.securityEvents && validationResult.securityEvents.length > 0) {
                Logger.warn('Security events detected during authentication', {
                    userId: req.user.id,
                    email: req.user.email,
                    securityEvents: validationResult.securityEvents.map(e => ({
                        type: e.type,
                        severity: e.severity,
                        description: e.description
                    }))
                });
            }

            next();
        } catch (error) {
            Logger.error('Authentication middleware error', error, {
                path: req.path,
                method: req.method,
                clientIp: getClientIp(req)
            });

            // Log middleware error
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'] || '',
                resource: req.path,
                action: req.method,
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown auth middleware error',
                details: {
                    component: 'auth_middleware',
                    error: error instanceof Error ? error.message : 'Unknown error',
                    stack: error instanceof Error ? error.stack : undefined
                }
            });

            if (!required) {
                return next();
            }

            return res.status(500).json({
                error: 'Authentication Error',
                message: 'An error occurred during authentication',
                code: 'AUTH_SYSTEM_ERROR'
            });
        }
    };
}

/**
 * Pre-configured authentication middleware for different use cases
 */
export const auth = {
    // Optional authentication - sets user if session exists but doesn't require it
    optional: authMiddleware({
        required: false
    }),

    // Required authentication - user must be authenticated
    required: authMiddleware({
        required: true
    }),

    // Admin only - requires authentication and admin role
    admin: authMiddleware({
        required: true,
        roles: ['SUPER_ADMIN', 'ADMIN']
    }),

    // Super admin only - requires authentication and super admin role
    superAdmin: authMiddleware({
        required: true,
        roles: ['SUPER_ADMIN']
    }),

    // Moderator and above - requires authentication and moderator+ role
    moderator: authMiddleware({
        required: true,
        roles: ['SUPER_ADMIN', 'ADMIN', 'MODERATOR']
    }),

    // Any authenticated user
    user: authMiddleware({
        required: true,
        roles: [] // No specific role required, just authentication
    })
};

/**
 * Role hierarchy validation
 */
export function hasRole(userRole: string, requiredRoles: string[]): boolean {
    if (requiredRoles.length === 0) {
        return true; // No specific role required
    }

    return requiredRoles.includes(userRole);
}

/**
 * Role hierarchy check with inheritance
 */
export function hasRoleOrHigher(userRole: string, minimumRole: string): boolean {
    const roleHierarchy = {
        'SUPER_ADMIN': 4,
        'ADMIN': 3,
        'MODERATOR': 2,
        'USER': 1
    };

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[minimumRole as keyof typeof roleHierarchy] || 0;

    return userLevel >= requiredLevel;
}

/**
 * Create custom authentication middleware with specific requirements
 */
export function createAuthMiddleware(options: AuthMiddlewareOptions) {
    return authMiddleware(options);
}

/**
 * Enhanced session validation middleware (lighter than full auth) using SessionValidator
 */
export function sessionValidationMiddleware() {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const sessionToken = extractSessionToken(req);
            const clientIp = getClientIp(req);
            const userAgent = req.headers['user-agent'] || '';

            if (sessionToken) {
                // Prepare minimal activity data for session validation
                const activityData: ActivityTrackingData = {
                    endpoint: req.path,
                    method: req.method,
                    userAgent: userAgent,
                    ipAddress: clientIp,
                    requestId: req.headers['x-request-id'] as string || `val_${Date.now()}`
                };

                const validationResult = await validateSession(sessionToken, clientIp, undefined, activityData);

                if (validationResult.isValid && validationResult.user) {
                    req.user = {
                        id: validationResult.user.id,
                        email: validationResult.user.email,
                        firstName: validationResult.user.first_name,
                        lastName: validationResult.user.last_name,
                        role: validationResult.user.role,
                        isActive: validationResult.user.is_active,
                        requiresPasswordChange: validationResult.user.requires_password_change
                    };
                    req.session = validationResult.session;

                    // Log any security events detected during validation
                    if (validationResult.securityEvents && validationResult.securityEvents.length > 0) {
                        Logger.warn('Security events detected during session validation', {
                            userId: req.user.id,
                            email: req.user.email,
                            endpoint: req.path,
                            securityEvents: validationResult.securityEvents.map(e => ({
                                type: e.type,
                                severity: e.severity,
                                description: e.description
                            }))
                        });
                    }
                } else if (validationResult.securityAlert) {
                    // Log validation failure for monitoring
                    Logger.warn('Session validation failed in middleware', {
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        securityAlert: validationResult.securityAlert,
                        endpoint: req.path,
                        clientIp
                    });
                }
            }

            next();
        } catch (error) {
            Logger.error('Session validation middleware error', error, {
                path: req.path,
                clientIp: getClientIp(req)
            });
            // Continue without session info
            next();
        }
    };
}
/**

 * Create and configure PrismaSessionStore for Express sessions
 */
export function createPrismaSessionStore(options?: {
    ttl?: number;
    disableTouch?: boolean;
}): PrismaSessionStore {
    return new PrismaSessionStore({
        ttl: options?.ttl || 24 * 60 * 60, // 24 hours default
        disableTouch: options?.disableTouch || false,
        createDatabaseTable: false, // We use existing Prisma schema
        serializer: {
            stringify: JSON.stringify,
            parse: JSON.parse
        }
    });
}

/**
 * Express session configuration with PrismaSessionStore
 */
export function getSessionConfig(sessionStore?: PrismaSessionStore) {
    const store = sessionStore || createPrismaSessionStore();

    return {
        store,
        secret: process.env.SESSION_SECRET || 'your-secret-key-change-in-production',
        resave: false,
        saveUninitialized: false,
        name: 'session-token',
        cookie: {
            secure: process.env.NODE_ENV === 'production',
            httpOnly: true,
            maxAge: 24 * 60 * 60 * 1000, // 24 hours
            sameSite: 'strict' as const,
            path: '/'
        },
        rolling: true, // Reset expiration on activity
        unset: 'destroy' // Destroy session when unset
    };
}

/**
 * Middleware to cleanup expired sessions periodically
 */
export function sessionCleanupMiddleware(intervalMinutes: number = 60) {
    let lastCleanup = Date.now();
    const intervalMs = intervalMinutes * 60 * 1000;

    return async (req: Request, res: Response, next: NextFunction) => {
        const now = Date.now();

        // Only run cleanup if enough time has passed
        if (now - lastCleanup > intervalMs) {
            try {
                const sessionStore = new PrismaSessionStore();
                const cleanupResult = await sessionStore.cleanupExpiredSessions();

                if (cleanupResult.totalCleaned > 0) {
                    Logger.info('Automatic session cleanup completed', {
                        expiredSessions: cleanupResult.expiredSessions,
                        idleTimeoutSessions: cleanupResult.idleTimeoutSessions,
                        totalCleaned: cleanupResult.totalCleaned
                    });
                }

                lastCleanup = now;
            } catch (error) {
                Logger.error('Session cleanup error', error);
            }
        }

        next();
    };
}