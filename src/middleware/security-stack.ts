// Express types - install @types/express when adding Express.js backend
interface Request {
    headers: Record<string, string | string[] | undefined>;
    body?: any;
    query?: any;
    params?: any;
    path: string;
    method: string;
    clientIp?: string;
}

interface Response {
    status(code: number): Response;
    json(data: any): Response;
    set(name: string, value: string): Response;
    cookie(name: string, value: string, options?: any): Response;
}

interface NextFunction {
    (error?: any): void;
}
import { rateLimitMiddleware } from './rate-limit-middleware';
import { getSecurityHeadersForEnvironment } from './security-headers-middleware';
import { csrf } from './csrf-middleware';
import { validate } from './input-validation-middleware';

export interface SecurityStackOptions {
    // Environment configuration
    environment?: 'development' | 'production' | 'test';

    // Rate limiting configuration
    rateLimit?: {
        general?: boolean;
        auth?: boolean;
        admin?: boolean;
        passwordReset?: boolean;
    };

    // Security headers
    securityHeaders?: boolean;

    // CSRF protection
    csrf?: {
        enabled?: boolean;
        type?: 'web' | 'api' | 'admin';
    };

    // Input validation (applied per route)
    validation?: boolean;

    // Custom middleware to include
    customMiddleware?: Array<(req: Request, res: Response, next: NextFunction) => void>;
}

/**
 * Create a comprehensive security middleware stack
 */
export function createSecurityMiddlewareStack(options: SecurityStackOptions = {}) {
    const {
        environment = process.env.NODE_ENV || 'development',
        rateLimit = { general: true },
        securityHeaders = true,
        csrf: csrfOptions = { enabled: true, type: 'web' },
        customMiddleware = []
    } = options;

    const middlewareStack: Array<(req: Request, res: Response, next: NextFunction) => void> = [];

    // 1. Security headers (should be first)
    if (securityHeaders) {
        middlewareStack.push(getSecurityHeadersForEnvironment(environment));
    }

    // 2. Rate limiting
    if (rateLimit.general) {
        middlewareStack.push(rateLimitMiddleware.general);
    }

    // 3. Custom middleware
    middlewareStack.push(...customMiddleware);

    // 4. CSRF protection (should be after rate limiting but before route handlers)
    if (csrfOptions.enabled) {
        switch (csrfOptions.type) {
            case 'api':
                middlewareStack.push(csrf.api);
                break;
            case 'admin':
                middlewareStack.push(csrf.admin);
                break;
            case 'web':
            default:
                middlewareStack.push(csrf.web);
                break;
        }
    }

    return middlewareStack;
}

/**
 * Pre-configured security stacks for different application types
 */
export const securityStacks = {
    // Full web application with all security features
    web: createSecurityMiddlewareStack({
        rateLimit: { general: true },
        securityHeaders: true,
        csrf: { enabled: true, type: 'web' }
    }),

    // API-only application
    api: createSecurityMiddlewareStack({
        rateLimit: { general: true },
        securityHeaders: true,
        csrf: { enabled: true, type: 'api' }
    }),

    // Admin panel with strict security
    admin: createSecurityMiddlewareStack({
        rateLimit: { general: true, admin: true },
        securityHeaders: true,
        csrf: { enabled: true, type: 'admin' }
    }),

    // Development environment with relaxed security
    development: createSecurityMiddlewareStack({
        environment: 'development',
        rateLimit: { general: false }, // Disable rate limiting in dev
        securityHeaders: true,
        csrf: { enabled: false } // Disable CSRF in dev for easier testing
    }),

    // Production environment with maximum security
    production: createSecurityMiddlewareStack({
        environment: 'production',
        rateLimit: { general: true, admin: true },
        securityHeaders: true,
        csrf: { enabled: true, type: 'web' }
    })
};

/**
 * Route-specific security middleware combinations
 */
export const routeSecurity = {
    // Authentication routes
    auth: {
        middleware: [
            rateLimitMiddleware.auth,
            validate.login
        ]
    },

    // Admin user management routes
    adminUserManagement: {
        middleware: [
            rateLimitMiddleware.admin,
            csrf.admin,
            validate.createUser
        ]
    },

    // Password reset routes
    passwordReset: {
        middleware: [
            rateLimitMiddleware.passwordReset,
            validate.passwordResetRequest
        ]
    },

    // General API routes with pagination
    apiWithPagination: {
        middleware: [
            rateLimitMiddleware.general,
            validate.pagination
        ]
    },

    // Search endpoints
    search: {
        middleware: [
            rateLimitMiddleware.general,
            validate.searchWithPagination
        ]
    },

    // User profile routes
    userProfile: {
        middleware: [
            rateLimitMiddleware.general,
            csrf.web,
            validate.userIdParam
        ]
    }
};

/**
 * Apply security middleware to Express app
 */
export function applySecurityMiddleware(
    app: any, // Express app
    stackType: keyof typeof securityStacks = 'web'
) {
    const stack = securityStacks[stackType];

    // Apply each middleware in the stack
    stack.forEach(middleware => {
        app.use(middleware);
    });

    console.log(`Applied ${stackType} security middleware stack with ${stack.length} middleware functions`);
}

/**
 * Create route-specific security middleware
 */
export function createRouteSecurityMiddleware(
    routeType: keyof typeof routeSecurity,
    additionalMiddleware: Array<(req: Request, res: Response, next: NextFunction) => void> = []
) {
    const routeConfig = routeSecurity[routeType];
    return [...routeConfig.middleware, ...additionalMiddleware];
}

/**
 * Security middleware for different HTTP methods
 */
export const methodSecurity = {
    // GET requests - minimal security
    GET: [
        rateLimitMiddleware.general
    ],

    // POST requests - full security
    POST: [
        rateLimitMiddleware.general,
        csrf.web
    ],

    // PUT/PATCH requests - full security
    PUT: [
        rateLimitMiddleware.general,
        csrf.web
    ],

    PATCH: [
        rateLimitMiddleware.general,
        csrf.web
    ],

    // DELETE requests - strict security
    DELETE: [
        rateLimitMiddleware.admin,
        csrf.admin
    ]
};

/**
 * Environment-aware security configuration
 */
export function getSecurityConfigForEnvironment(env: string = process.env.NODE_ENV || 'development') {
    switch (env) {
        case 'production':
            return {
                rateLimit: { general: true, auth: true, admin: true, passwordReset: true },
                securityHeaders: true,
                csrf: { enabled: true, type: 'web' as const },
                validation: true
            };

        case 'staging':
            return {
                rateLimit: { general: true, auth: true, admin: true, passwordReset: true },
                securityHeaders: true,
                csrf: { enabled: true, type: 'web' as const },
                validation: true
            };

        case 'development':
            return {
                rateLimit: { general: false, auth: false, admin: false, passwordReset: false },
                securityHeaders: true,
                csrf: { enabled: false, type: 'web' as const },
                validation: true
            };

        case 'test':
            return {
                rateLimit: { general: false, auth: false, admin: false, passwordReset: false },
                securityHeaders: false,
                csrf: { enabled: false, type: 'web' as const },
                validation: true
            };

        default:
            return {
                rateLimit: { general: true, auth: true, admin: true, passwordReset: true },
                securityHeaders: true,
                csrf: { enabled: true, type: 'web' as const },
                validation: true
            };
    }
}