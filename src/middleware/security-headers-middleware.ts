import { Request, Response, NextFunction } from 'express';

export interface SecurityHeadersOptions {
    // Content Security Policy
    contentSecurityPolicy?: {
        directives?: Record<string, string | string[]>;
        reportOnly?: boolean;
    };

    // HTTP Strict Transport Security
    hsts?: {
        maxAge?: number;
        includeSubDomains?: boolean;
        preload?: boolean;
    };

    // X-Frame-Options
    frameOptions?: 'DENY' | 'SAMEORIGIN' | string;

    // X-Content-Type-Options
    noSniff?: boolean;

    // X-XSS-Protection
    xssProtection?: boolean | string;

    // Referrer Policy
    referrerPolicy?: string;

    // Permissions Policy (formerly Feature Policy)
    permissionsPolicy?: Record<string, string[]>;

    // Cross-Origin Embedder Policy
    crossOriginEmbedderPolicy?: 'unsafe-none' | 'require-corp';

    // Cross-Origin Opener Policy
    crossOriginOpenerPolicy?: 'unsafe-none' | 'same-origin-allow-popups' | 'same-origin';

    // Cross-Origin Resource Policy
    crossOriginResourcePolicy?: 'same-site' | 'same-origin' | 'cross-origin';

    // Custom headers
    customHeaders?: Record<string, string>;
}

/**
 * Default security configuration for admin platform
 */
const defaultSecurityConfig: SecurityHeadersOptions = {
    contentSecurityPolicy: {
        directives: {
            'default-src': ["'self'"],
            'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Needed for React dev
            'style-src': ["'self'", "'unsafe-inline'"], // Needed for CSS-in-JS
            'img-src': ["'self'", 'data:', 'https:'],
            'font-src': ["'self'", 'https:', 'data:'],
            'connect-src': ["'self'"],
            'media-src': ["'self'"],
            'object-src': ["'none'"],
            'child-src': ["'self'"],
            'worker-src': ["'self'"],
            'frame-ancestors': ["'none'"],
            'form-action': ["'self'"],
            'base-uri': ["'self'"],
            'manifest-src': ["'self'"]
        },
        reportOnly: false
    },

    hsts: {
        maxAge: 31536000, // 1 year
        includeSubDomains: true,
        preload: true
    },

    frameOptions: 'DENY',
    noSniff: true,
    xssProtection: '1; mode=block',
    referrerPolicy: 'strict-origin-when-cross-origin',

    permissionsPolicy: {
        'camera': [],
        'microphone': [],
        'geolocation': [],
        'interest-cohort': [], // Disable FLoC
        'payment': [],
        'usb': [],
        'bluetooth': [],
        'magnetometer': [],
        'gyroscope': [],
        'accelerometer': []
    },

    crossOriginEmbedderPolicy: 'unsafe-none', // More permissive for admin tools
    crossOriginOpenerPolicy: 'same-origin-allow-popups',
    crossOriginResourcePolicy: 'same-origin'
};

/**
 * Build Content Security Policy header value
 */
function buildCSPHeader(directives: Record<string, string | string[]>): string {
    return Object.entries(directives)
        .map(([directive, sources]) => {
            const sourceList = Array.isArray(sources) ? sources.join(' ') : sources;
            return `${directive} ${sourceList}`;
        })
        .join('; ');
}

/**
 * Build Permissions Policy header value
 */
function buildPermissionsPolicyHeader(policies: Record<string, string[]>): string {
    return Object.entries(policies)
        .map(([feature, allowlist]) => {
            if (allowlist.length === 0) {
                return `${feature}=()`;
            }
            const origins = allowlist.map(origin => `"${origin}"`).join(' ');
            return `${feature}=(${origins})`;
        })
        .join(', ');
}

/**
 * Security headers middleware
 */
export function securityHeadersMiddleware(options: SecurityHeadersOptions = {}) {
    // Merge with defaults
    const config = {
        ...defaultSecurityConfig,
        ...options,
        contentSecurityPolicy: {
            ...defaultSecurityConfig.contentSecurityPolicy,
            ...options.contentSecurityPolicy,
            directives: {
                ...defaultSecurityConfig.contentSecurityPolicy?.directives,
                ...options.contentSecurityPolicy?.directives
            }
        },
        hsts: {
            ...defaultSecurityConfig.hsts,
            ...options.hsts
        },
        permissionsPolicy: {
            ...defaultSecurityConfig.permissionsPolicy,
            ...options.permissionsPolicy
        }
    };

    return (req: Request, res: Response, next: NextFunction) => {
        try {
            // Content Security Policy
            if (config.contentSecurityPolicy?.directives) {
                const cspHeader = buildCSPHeader(config.contentSecurityPolicy.directives);
                const headerName = config.contentSecurityPolicy.reportOnly
                    ? 'Content-Security-Policy-Report-Only'
                    : 'Content-Security-Policy';
                res.set(headerName, cspHeader);
            }

            // HTTP Strict Transport Security (only for HTTPS)
            if (config.hsts && (req.secure || req.headers['x-forwarded-proto'] === 'https')) {
                let hstsValue = `max-age=${config.hsts.maxAge}`;
                if (config.hsts.includeSubDomains) {
                    hstsValue += '; includeSubDomains';
                }
                if (config.hsts.preload) {
                    hstsValue += '; preload';
                }
                res.set('Strict-Transport-Security', hstsValue);
            }

            // X-Frame-Options
            if (config.frameOptions) {
                res.set('X-Frame-Options', config.frameOptions);
            }

            // X-Content-Type-Options
            if (config.noSniff) {
                res.set('X-Content-Type-Options', 'nosniff');
            }

            // X-XSS-Protection
            if (config.xssProtection) {
                const xssValue = typeof config.xssProtection === 'string'
                    ? config.xssProtection
                    : '1; mode=block';
                res.set('X-XSS-Protection', xssValue);
            }

            // Referrer Policy
            if (config.referrerPolicy) {
                res.set('Referrer-Policy', config.referrerPolicy);
            }

            // Permissions Policy
            if (config.permissionsPolicy) {
                const permissionsPolicyValue = buildPermissionsPolicyHeader(config.permissionsPolicy);
                res.set('Permissions-Policy', permissionsPolicyValue);
            }

            // Cross-Origin Embedder Policy
            if (config.crossOriginEmbedderPolicy) {
                res.set('Cross-Origin-Embedder-Policy', config.crossOriginEmbedderPolicy);
            }

            // Cross-Origin Opener Policy
            if (config.crossOriginOpenerPolicy) {
                res.set('Cross-Origin-Opener-Policy', config.crossOriginOpenerPolicy);
            }

            // Cross-Origin Resource Policy
            if (config.crossOriginResourcePolicy) {
                res.set('Cross-Origin-Resource-Policy', config.crossOriginResourcePolicy);
            }

            // Custom headers
            if (config.customHeaders) {
                Object.entries(config.customHeaders).forEach(([name, value]) => {
                    res.set(name, value);
                });
            }

            // Additional security headers
            res.set('X-Powered-By', ''); // Remove X-Powered-By header
            res.set('Server', ''); // Remove Server header

            next();
        } catch (error) {
            console.error('Security headers middleware error:', error);
            // Don't block the request if header setting fails
            next();
        }
    };
}

/**
 * Pre-configured security headers for different environments
 */
export const securityHeaders = {
    // Production configuration with strict security
    production: securityHeadersMiddleware({
        contentSecurityPolicy: {
            directives: {
                'default-src': ["'self'"],
                'script-src': ["'self'"],
                'style-src': ["'self'", "'unsafe-inline'"], // Allow inline styles for CSS frameworks
                'img-src': ["'self'", 'data:', 'https:'],
                'font-src': ["'self'", 'https:', 'data:'],
                'connect-src': ["'self'"],
                'media-src': ["'self'"],
                'object-src': ["'none'"],
                'child-src': ["'none'"],
                'worker-src': ["'self'"],
                'frame-ancestors': ["'none'"],
                'form-action': ["'self'"],
                'base-uri': ["'self'"],
                'upgrade-insecure-requests': []
            }
        },
        hsts: {
            maxAge: 31536000, // 1 year
            includeSubDomains: true,
            preload: true
        }
    }),

    // Development configuration with relaxed CSP
    development: securityHeadersMiddleware({
        contentSecurityPolicy: {
            directives: {
                'default-src': ["'self'"],
                'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'", 'localhost:*'],
                'style-src': ["'self'", "'unsafe-inline'"],
                'img-src': ["'self'", 'data:', 'https:', 'http:'],
                'font-src': ["'self'", 'https:', 'data:'],
                'connect-src': ["'self'", 'ws:', 'wss:', 'localhost:*'],
                'media-src': ["'self'"],
                'object-src': ["'none'"],
                'child-src': ["'self'"],
                'worker-src': ["'self'", 'blob:'],
                'frame-ancestors': ["'none'"],
                'form-action': ["'self'"],
                'base-uri': ["'self'"]
            }
        },
        hsts: {
            maxAge: 0 // Disable HSTS in development
        }
    }),

    // API-only configuration
    api: securityHeadersMiddleware({
        contentSecurityPolicy: {
            directives: {
                'default-src': ["'none'"],
                'frame-ancestors': ["'none'"]
            }
        },
        frameOptions: 'DENY',
        noSniff: true,
        customHeaders: {
            'X-API-Version': '1.0',
            'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
        }
    })
};

/**
 * Environment-aware security headers
 */
export function getSecurityHeadersForEnvironment(env: string = process.env.NODE_ENV || 'development') {
    switch (env) {
        case 'production':
            return securityHeaders.production;
        case 'development':
            return securityHeaders.development;
        case 'test':
            return securityHeaders.development; // Use development config for tests
        default:
            return securityHeaders.development;
    }
}