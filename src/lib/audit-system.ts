import { prisma } from './prisma';
import { SecurityLogger, SecurityEventType, AuditLogFilters, AuditLogEntry } from './security-logger';
import crypto from 'crypto';

// Advanced audit log query interfaces
export interface AdvancedAuditFilters extends AuditLogFilters {
    // Time-based filters
    timeRange?: 'last_hour' | 'last_24h' | 'last_week' | 'last_month' | 'custom';
    customStartDate?: Date;
    customEndDate?: Date;

    // Pattern matching
    searchTerm?: string; // Search across multiple fields
    userAgentPattern?: string;
    detailsContains?: string;

    // Risk-based filtering
    riskLevel?: 'low' | 'medium' | 'high' | 'critical';
    suspiciousOnly?: boolean;

    // Aggregation options
    groupBy?: 'user' | 'ip' | 'event_type' | 'hour' | 'day';
    includeStats?: boolean;
}

export interface AuditLogReport {
    summary: {
        totalEvents: number;
        timeRange: string;
        uniqueUsers: number;
        uniqueIPs: number;
        successRate: number;
        riskScore: number;
    };
    events: AuditLogEntry[];
    statistics?: {
        eventsByType: Array<{ type: string; count: number; percentage: number }>;
        eventsByHour: Array<{ hour: string; count: number }>;
        topUsers: Array<{ userId: string; email?: string; eventCount: number }>;
        topIPs: Array<{ ip: string; eventCount: number; riskLevel: string }>;
        failureReasons: Array<{ reason: string; count: number }>;
    };
    securityAlerts: SecurityAlert[];
}

export interface SecurityAlert {
    id: string;
    type: 'BRUTE_FORCE' | 'SUSPICIOUS_IP' | 'UNUSUAL_ACTIVITY' | 'PRIVILEGE_ESCALATION' | 'DATA_BREACH_ATTEMPT';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    title: string;
    description: string;
    affectedUsers: string[];
    affectedIPs: string[];
    eventCount: number;
    timeWindow: string;
    firstSeen: Date;
    lastSeen: Date;
    status: 'ACTIVE' | 'INVESTIGATING' | 'RESOLVED' | 'FALSE_POSITIVE';
    recommendedActions: string[];
}

export interface LogIntegrityCheck {
    isValid: boolean;
    totalLogs: number;
    checkedLogs: number;
    tamperedLogs: number;
    missingLogs: number;
    lastCheck: Date;
    issues: Array<{
        logId: string;
        issue: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH';
    }>;
}

export interface RetentionPolicy {
    id: string;
    name: string;
    description: string;
    retentionDays: number;
    eventTypes: SecurityEventType[];
    archiveBeforeDelete: boolean;
    isActive: boolean;
    lastExecuted?: Date;
    nextExecution: Date;
}

/**
 * Advanced Audit Log Query and Reporting System
 * 
 * Provides sophisticated querying, security violation detection,
 * log integrity protection, and retention management.
 */
export class AuditSystem {
    /**
     * Advanced audit log query with sophisticated filtering
     */
    static async queryAuditLogs(filters: AdvancedAuditFilters): Promise<AuditLogReport> {
        try {
            // Build time range filter
            const timeFilter = this.buildTimeFilter(filters);

            // Build advanced where clause
            const where = this.buildAdvancedWhereClause(filters, timeFilter);

            // Execute main query
            const events = await prisma.securityAuditLog.findMany({
                where,
                orderBy: { created_at: 'desc' },
                take: filters.limit || 1000,
                skip: filters.offset || 0,
            });

            // Get summary statistics
            const summary = await this.generateSummaryStats(where, timeFilter);

            // Generate detailed statistics if requested
            const statistics = filters.includeStats ?
                await this.generateDetailedStats(where) : undefined;

            // Detect security alerts
            const securityAlerts = await this.detectSecurityAlerts(where);

            return {
                summary,
                events: events.map((log: any) => ({
                    id: log.id,
                    eventType: log.event_type,
                    userId: log.user_id || undefined,
                    email: log.email || undefined,
                    ipAddress: log.ip_address,
                    userAgent: log.user_agent || undefined,
                    resource: log.resource || undefined,
                    action: log.action || undefined,
                    details: log.details as Record<string, any> || undefined,
                    success: log.success,
                    errorMessage: log.error_message || undefined,
                    createdAt: log.created_at,
                })),
                statistics,
                securityAlerts,
            };
        } catch (error) {
            console.error('Failed to query audit logs:', error);
            throw new Error('Failed to generate audit log report');
        }
    }

    /**
     * Detect security violations and generate alerts
     */
    static async detectSecurityAlerts(where: any): Promise<SecurityAlert[]> {
        const alerts: SecurityAlert[] = [];
        const now = new Date();

        try {
            // Detect brute force attacks (multiple failed logins from same IP)
            const bruteForceAttempts = await prisma.securityAuditLog.groupBy({
                by: ['ip_address'],
                where: {
                    ...where,
                    event_type: SecurityEventType.LOGIN_FAILURE,
                    created_at: { gte: new Date(now.getTime() - 60 * 60 * 1000) }, // Last hour
                },
                _count: { id: true },
                having: { id: { _count: { gt: 5 } } },
            });

            for (const attempt of bruteForceAttempts) {
                alerts.push({
                    id: crypto.randomUUID(),
                    type: 'BRUTE_FORCE',
                    severity: 'HIGH',
                    title: 'Brute Force Attack Detected',
                    description: `Multiple failed login attempts from IP ${attempt.ip_address}`,
                    affectedUsers: [],
                    affectedIPs: [attempt.ip_address],
                    eventCount: (attempt as any)._count.id,
                    timeWindow: '1 hour',
                    firstSeen: new Date(now.getTime() - 60 * 60 * 1000),
                    lastSeen: now,
                    status: 'ACTIVE',
                    recommendedActions: [
                        'Block IP address',
                        'Review affected user accounts',
                        'Enable additional monitoring'
                    ],
                });
            }

            // Detect suspicious IP activity (high volume from single IP)
            const suspiciousIPs = await prisma.securityAuditLog.groupBy({
                by: ['ip_address'],
                where: {
                    ...where,
                    created_at: { gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) }, // Last 24 hours
                },
                _count: { id: true },
                having: { id: { _count: { gt: 100 } } },
            });

            for (const suspiciousIP of suspiciousIPs) {
                alerts.push({
                    id: crypto.randomUUID(),
                    type: 'SUSPICIOUS_IP',
                    severity: 'MEDIUM',
                    title: 'Suspicious IP Activity',
                    description: `High volume of activity from IP ${suspiciousIP.ip_address}`,
                    affectedUsers: [],
                    affectedIPs: [suspiciousIP.ip_address],
                    eventCount: (suspiciousIP as any)._count.id,
                    timeWindow: '24 hours',
                    firstSeen: new Date(now.getTime() - 24 * 60 * 60 * 1000),
                    lastSeen: now,
                    status: 'ACTIVE',
                    recommendedActions: [
                        'Investigate IP reputation',
                        'Review activity patterns',
                        'Consider rate limiting'
                    ],
                });
            }

            // Detect privilege escalation attempts
            const privilegeEscalation = await prisma.securityAuditLog.findMany({
                where: {
                    ...where,
                    event_type: SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                    created_at: { gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) },
                },
                take: 50,
            });

            if (privilegeEscalation.length > 0) {
                const affectedUsers = [...new Set(privilegeEscalation.map((log: any) => log.user_id).filter(Boolean))] as string[];
                const affectedIPs = [...new Set(privilegeEscalation.map((log: any) => log.ip_address))] as string[];

                alerts.push({
                    id: crypto.randomUUID(),
                    type: 'PRIVILEGE_ESCALATION',
                    severity: 'HIGH',
                    title: 'Privilege Escalation Attempts',
                    description: 'Multiple unauthorized access attempts detected',
                    affectedUsers,
                    affectedIPs,
                    eventCount: privilegeEscalation.length,
                    timeWindow: '24 hours',
                    firstSeen: new Date(Math.min(...privilegeEscalation.map((log: any) => log.created_at.getTime()))),
                    lastSeen: new Date(Math.max(...privilegeEscalation.map((log: any) => log.created_at.getTime()))),
                    status: 'ACTIVE',
                    recommendedActions: [
                        'Review user permissions',
                        'Audit access controls',
                        'Investigate affected accounts'
                    ],
                });
            }

            return alerts;
        } catch (error) {
            console.error('Failed to detect security alerts:', error);
            return [];
        }
    }

    /**
     * Verify log integrity and detect tampering
     */
    static async verifyLogIntegrity(): Promise<LogIntegrityCheck> {
        try {
            const totalLogs = await prisma.securityAuditLog.count();
            const sampleSize = Math.min(1000, totalLogs); // Check up to 1000 logs

            const logs = await prisma.securityAuditLog.findMany({
                orderBy: { created_at: 'desc' },
                take: sampleSize,
            });

            const issues: Array<{ logId: string; issue: string; severity: 'LOW' | 'MEDIUM' | 'HIGH' }> = [];
            let tamperedLogs = 0;
            let missingLogs = 0;

            // Check for sequential ID gaps (potential missing logs)
            for (let i = 1; i < logs.length; i++) {
                const currentLog = logs[i];
                const previousLog = logs[i - 1];

                // Check for suspicious time gaps
                const timeDiff = previousLog.created_at.getTime() - currentLog.created_at.getTime();
                if (timeDiff > 24 * 60 * 60 * 1000) { // More than 24 hours gap
                    issues.push({
                        logId: currentLog.id,
                        issue: `Suspicious time gap of ${Math.round(timeDiff / (60 * 60 * 1000))} hours`,
                        severity: 'MEDIUM'
                    });
                }

                // Check for malformed data
                if (!currentLog.event_type || !currentLog.ip_address) {
                    issues.push({
                        logId: currentLog.id,
                        issue: 'Missing required fields',
                        severity: 'HIGH'
                    });
                    tamperedLogs++;
                }

                // Check for suspicious patterns in details
                if (currentLog.details && typeof currentLog.details === 'object') {
                    const detailsStr = JSON.stringify(currentLog.details);
                    if (detailsStr.includes('<script>') || detailsStr.includes('DROP TABLE')) {
                        issues.push({
                            logId: currentLog.id,
                            issue: 'Suspicious content in log details',
                            severity: 'HIGH'
                        });
                        tamperedLogs++;
                    }
                }
            }

            return {
                isValid: issues.length === 0,
                totalLogs,
                checkedLogs: sampleSize,
                tamperedLogs,
                missingLogs,
                lastCheck: new Date(),
                issues,
            };
        } catch (error) {
            console.error('Failed to verify log integrity:', error);
            throw new Error('Failed to verify log integrity');
        }
    }

    /**
     * Implement log retention and archival policies
     */
    static async executeRetentionPolicy(policy: RetentionPolicy): Promise<{
        archivedCount: number;
        deletedCount: number;
        errors: string[];
    }> {
        const errors: string[] = [];
        let archivedCount = 0;
        let deletedCount = 0;

        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - policy.retentionDays);

            const where: any = {
                created_at: { lt: cutoffDate }
            };

            if (policy.eventTypes.length > 0) {
                where.event_type = { in: policy.eventTypes };
            }

            // Get logs to be processed
            const logsToProcess = await prisma.securityAuditLog.findMany({
                where,
                orderBy: { created_at: 'asc' },
            });

            if (policy.archiveBeforeDelete) {
                // Archive logs before deletion
                try {
                    await this.archiveLogs(logsToProcess, policy.name);
                    archivedCount = logsToProcess.length;
                } catch (error) {
                    errors.push(`Failed to archive logs: ${error}`);
                    return { archivedCount: 0, deletedCount: 0, errors };
                }
            }

            // Delete logs
            const deleteResult = await prisma.securityAuditLog.deleteMany({ where });
            deletedCount = deleteResult.count;

            // Log the retention policy execution
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                ipAddress: 'system',
                resource: 'audit_logs',
                action: 'retention_policy_executed',
                success: true,
                details: {
                    policyName: policy.name,
                    archivedCount,
                    deletedCount,
                    retentionDays: policy.retentionDays,
                },
            });

            return { archivedCount, deletedCount, errors };
        } catch (error) {
            errors.push(`Retention policy execution failed: ${error}`);
            return { archivedCount, deletedCount, errors };
        }
    }

    /**
     * Generate security compliance report
     */
    static async generateComplianceReport(timeRange: 'week' | 'month' | 'quarter' | 'year'): Promise<{
        period: string;
        totalEvents: number;
        securityMetrics: {
            authenticationEvents: number;
            failedLogins: number;
            successfulLogins: number;
            passwordChanges: number;
            accountLockouts: number;
            sessionAnomalies: number;
            adminActions: number;
            securityViolations: number;
        };
        complianceScore: number;
        recommendations: string[];
        riskAssessment: {
            level: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
            factors: string[];
        };
    }> {
        try {
            const now = new Date();
            let startDate: Date;
            let period: string;

            switch (timeRange) {
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    period = 'Last 7 days';
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    period = 'Last 30 days';
                    break;
                case 'quarter':
                    startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                    period = 'Last 90 days';
                    break;
                case 'year':
                    startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                    period = 'Last 365 days';
                    break;
            }

            const baseWhere = { created_at: { gte: startDate } };

            const [
                totalEvents,
                authEvents,
                failedLogins,
                successfulLogins,
                passwordChanges,
                accountLockouts,
                sessionAnomalies,
                adminActions,
                securityViolations,
            ] = await Promise.all([
                prisma.securityAuditLog.count({ where: baseWhere }),
                prisma.securityAuditLog.count({
                    where: {
                        ...baseWhere,
                        event_type: {
                            in: [
                                SecurityEventType.LOGIN_SUCCESS,
                                SecurityEventType.LOGIN_FAILURE,
                                SecurityEventType.LOGOUT,
                            ]
                        },
                    },
                }),
                prisma.securityAuditLog.count({
                    where: { ...baseWhere, event_type: SecurityEventType.LOGIN_FAILURE },
                }),
                prisma.securityAuditLog.count({
                    where: { ...baseWhere, event_type: SecurityEventType.LOGIN_SUCCESS },
                }),
                prisma.securityAuditLog.count({
                    where: { ...baseWhere, event_type: SecurityEventType.PASSWORD_CHANGE },
                }),
                prisma.securityAuditLog.count({
                    where: { ...baseWhere, event_type: SecurityEventType.ACCOUNT_LOCKED },
                }),
                prisma.securityAuditLog.count({
                    where: {
                        ...baseWhere,
                        event_type: {
                            in: [
                                SecurityEventType.SESSION_IP_MISMATCH,
                                SecurityEventType.SUSPICIOUS_SESSION_ACTIVITY,
                            ]
                        },
                    },
                }),
                prisma.securityAuditLog.count({
                    where: {
                        ...baseWhere,
                        event_type: {
                            in: [
                                SecurityEventType.ADMIN_USER_CREATED,
                                SecurityEventType.ADMIN_USER_UPDATED,
                                SecurityEventType.ADMIN_ROLE_CHANGED,
                            ]
                        },
                    },
                }),
                prisma.securityAuditLog.count({
                    where: {
                        ...baseWhere,
                        event_type: {
                            in: [
                                SecurityEventType.RATE_LIMIT_EXCEEDED,
                                SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                                SecurityEventType.CSRF_TOKEN_INVALID,
                            ]
                        },
                    },
                }),
            ]);

            // Calculate compliance score (0-100)
            let complianceScore = 100;
            const recommendations: string[] = [];
            const riskFactors: string[] = [];

            // Deduct points for security issues
            if (failedLogins > successfulLogins * 0.1) {
                complianceScore -= 10;
                recommendations.push('High failed login rate detected - consider implementing stronger authentication');
                riskFactors.push('High authentication failure rate');
            }

            if (accountLockouts > 0) {
                complianceScore -= 5;
                recommendations.push('Account lockouts detected - review security policies');
                riskFactors.push('Account lockouts occurred');
            }

            if (sessionAnomalies > 0) {
                complianceScore -= 15;
                recommendations.push('Session anomalies detected - investigate suspicious activities');
                riskFactors.push('Session security anomalies');
            }

            if (securityViolations > totalEvents * 0.05) {
                complianceScore -= 20;
                recommendations.push('High security violation rate - strengthen security controls');
                riskFactors.push('High security violation rate');
            }

            if (passwordChanges < adminActions * 0.1) {
                complianceScore -= 5;
                recommendations.push('Low password change frequency - encourage regular password updates');
            }

            // Determine risk level
            let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
            if (complianceScore >= 90) riskLevel = 'LOW';
            else if (complianceScore >= 70) riskLevel = 'MEDIUM';
            else if (complianceScore >= 50) riskLevel = 'HIGH';
            else riskLevel = 'CRITICAL';

            return {
                period,
                totalEvents,
                securityMetrics: {
                    authenticationEvents: authEvents,
                    failedLogins,
                    successfulLogins,
                    passwordChanges,
                    accountLockouts,
                    sessionAnomalies,
                    adminActions,
                    securityViolations,
                },
                complianceScore: Math.max(0, complianceScore),
                recommendations,
                riskAssessment: {
                    level: riskLevel,
                    factors: riskFactors,
                },
            };
        } catch (error) {
            console.error('Failed to generate compliance report:', error);
            throw new Error('Failed to generate compliance report');
        }
    }

    // Private helper methods
    private static buildTimeFilter(filters: AdvancedAuditFilters): any {
        const now = new Date();

        if (filters.timeRange) {
            switch (filters.timeRange) {
                case 'last_hour':
                    return { gte: new Date(now.getTime() - 60 * 60 * 1000) };
                case 'last_24h':
                    return { gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) };
                case 'last_week':
                    return { gte: new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000) };
                case 'last_month':
                    return { gte: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000) };
                case 'custom':
                    const filter: any = {};
                    if (filters.customStartDate) filter.gte = filters.customStartDate;
                    if (filters.customEndDate) filter.lte = filters.customEndDate;
                    return filter;
            }
        }

        if (filters.startDate || filters.endDate) {
            const filter: any = {};
            if (filters.startDate) filter.gte = filters.startDate;
            if (filters.endDate) filter.lte = filters.endDate;
            return filter;
        }

        return undefined;
    }

    private static buildAdvancedWhereClause(filters: AdvancedAuditFilters, timeFilter: any): any {
        const where: any = {};

        if (timeFilter) {
            where.created_at = timeFilter;
        }

        if (filters.eventTypes && filters.eventTypes.length > 0) {
            where.event_type = { in: filters.eventTypes };
        }

        if (filters.userId) {
            where.user_id = filters.userId;
        }

        if (filters.email) {
            where.email = { contains: filters.email, mode: 'insensitive' };
        }

        if (filters.ipAddress) {
            where.ip_address = filters.ipAddress;
        }

        if (filters.success !== undefined) {
            where.success = filters.success;
        }

        if (filters.suspiciousOnly) {
            where.OR = [
                {
                    event_type: {
                        in: [
                            SecurityEventType.LOGIN_FAILURE,
                            SecurityEventType.ACCOUNT_LOCKED,
                            SecurityEventType.SESSION_IP_MISMATCH,
                            SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                            SecurityEventType.RATE_LIMIT_EXCEEDED,
                        ]
                    }
                },
                { success: false },
            ];
        }

        if (filters.searchTerm) {
            where.OR = [
                { email: { contains: filters.searchTerm, mode: 'insensitive' } },
                { user_agent: { contains: filters.searchTerm, mode: 'insensitive' } },
                { resource: { contains: filters.searchTerm, mode: 'insensitive' } },
                { action: { contains: filters.searchTerm, mode: 'insensitive' } },
                { error_message: { contains: filters.searchTerm, mode: 'insensitive' } },
            ];
        }

        return where;
    }

    private static async generateSummaryStats(where: any, timeFilter: any): Promise<any> {
        const [totalEvents, successfulEvents, uniqueUsers, uniqueIPs] = await Promise.all([
            prisma.securityAuditLog.count({ where }),
            prisma.securityAuditLog.count({ where: { ...where, success: true } }),
            prisma.securityAuditLog.findMany({
                where: { ...where, user_id: { not: null } },
                select: { user_id: true },
                distinct: ['user_id'],
            }),
            prisma.securityAuditLog.findMany({
                where,
                select: { ip_address: true },
                distinct: ['ip_address'],
            }),
        ]);

        const successRate = totalEvents > 0 ? (successfulEvents / totalEvents) * 100 : 0;
        const riskScore = Math.max(0, 100 - (100 - successRate) * 2); // Simple risk calculation

        return {
            totalEvents,
            timeRange: timeFilter ? 'filtered' : 'all_time',
            uniqueUsers: uniqueUsers.length,
            uniqueIPs: uniqueIPs.length,
            successRate: Math.round(successRate * 100) / 100,
            riskScore: Math.round(riskScore * 100) / 100,
        };
    }

    private static async generateDetailedStats(where: any): Promise<any> {
        const [eventsByType, topUsers, topIPs, failureReasons] = await Promise.all([
            prisma.securityAuditLog.groupBy({
                by: ['event_type'],
                where,
                _count: { event_type: true },
                orderBy: { _count: { event_type: 'desc' } },
            }),
            prisma.securityAuditLog.groupBy({
                by: ['user_id', 'email'],
                where: { ...where, user_id: { not: null } },
                _count: { user_id: true },
                orderBy: { _count: { user_id: 'desc' } },
                take: 10,
            }),
            prisma.securityAuditLog.groupBy({
                by: ['ip_address'],
                where,
                _count: { ip_address: true },
                orderBy: { _count: { ip_address: 'desc' } },
                take: 10,
            }),
            prisma.securityAuditLog.groupBy({
                by: ['error_message'],
                where: { ...where, success: false, error_message: { not: null } },
                _count: { error_message: true },
                orderBy: { _count: { error_message: 'desc' } },
                take: 10,
            }),
        ]);

        const totalEvents = eventsByType.reduce((sum: number, item: any) => sum + item._count.event_type, 0);

        return {
            eventsByType: eventsByType.map((item: any) => ({
                type: item.event_type,
                count: item._count.event_type,
                percentage: Math.round((item._count.event_type / totalEvents) * 10000) / 100,
            })),
            topUsers: topUsers.map((item: any) => ({
                userId: item.user_id,
                email: item.email,
                eventCount: item._count.user_id,
            })),
            topIPs: topIPs.map((item: any) => ({
                ip: item.ip_address,
                eventCount: item._count.ip_address,
                riskLevel: item._count.ip_address > 100 ? 'HIGH' : item._count.ip_address > 50 ? 'MEDIUM' : 'LOW',
            })),
            failureReasons: failureReasons.map((item: any) => ({
                reason: item.error_message,
                count: item._count.error_message,
            })),
        };
    }

    private static async archiveLogs(logs: any[], policyName: string): Promise<void> {
        // In a real implementation, this would archive logs to external storage
        // For now, we'll just log the archival action
        console.log(`[AUDIT] Archiving ${logs.length} logs for policy: ${policyName}`);

        // Could implement:
        // - Export to JSON/CSV files
        // - Upload to cloud storage (S3, etc.)
        // - Store in separate archive database
        // - Compress and encrypt archived data
    }
}

// Export convenience functions for common audit operations
export const generateSecurityReport = (timeRange: 'week' | 'month' | 'quarter' = 'month') =>
    AuditSystem.generateComplianceReport(timeRange);

export const detectSecurityThreats = (timeWindow: 'hour' | 'day' | 'week' = 'day') =>
    AuditSystem.queryAuditLogs({
        timeRange: timeWindow === 'hour' ? 'last_hour' : timeWindow === 'day' ? 'last_24h' : 'last_week',
        suspiciousOnly: true,
        includeStats: true,
    });

export const auditUserActivity = (userId: string, days: number = 30) =>
    AuditSystem.queryAuditLogs({
        userId,
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
        includeStats: true,
    });

export const auditIPActivity = (ipAddress: string, days: number = 7) =>
    AuditSystem.queryAuditLogs({
        ipAddress,
        startDate: new Date(Date.now() - days * 24 * 60 * 60 * 1000),
        includeStats: true,
    });