import { prisma } from './prisma';
import { UserRole } from '../generated/prisma';

export interface ValidationResult {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    summary: {
        totalUsers: number;
        validUsers: number;
        invalidUsers: number;
        orphanedRecords: number;
        expiredData: number;
    };
}

export class DataValidator {
    /**
     * Comprehensive data validation and integrity check
     */
    async validateDatabase(): Promise<ValidationResult> {
        const errors: string[] = [];
        const warnings: string[] = [];

        try {
            // Validate user data integrity
            const userValidation = await this.validateUsers();
            errors.push(...userValidation.errors);
            warnings.push(...userValidation.warnings);

            // Validate Freemius data integrity
            const freemiusValidation = await this.validateFreemiusData();
            errors.push(...freemiusValidation.errors);
            warnings.push(...freemiusValidation.warnings);

            // Validate IP data integrity
            const ipValidation = await this.validateIpData();
            errors.push(...ipValidation.errors);
            warnings.push(...ipValidation.warnings);

            // Validate foreign key relationships
            const relationshipValidation = await this.validateRelationships();
            errors.push(...relationshipValidation.errors);
            warnings.push(...relationshipValidation.warnings);

            // Get summary statistics
            const summary = await this.getSummaryStatistics();

            return {
                isValid: errors.length === 0,
                errors,
                warnings,
                summary
            };
        } catch (error) {
            return {
                isValid: false,
                errors: [`Database validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
                warnings: [],
                summary: {
                    totalUsers: 0,
                    validUsers: 0,
                    invalidUsers: 0,
                    orphanedRecords: 0,
                    expiredData: 0
                }
            };
        }
    }

    /**
     * Validate user data and roles
     */
    private async validateUsers(): Promise<{ errors: string[]; warnings: string[] }> {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Check for users with invalid roles
        const users = await prisma.user.findMany();
        const validRoles = Object.values(UserRole);

        for (const user of users) {
            if (!validRoles.includes(user.role as UserRole)) {
                errors.push(`User ${user.email} has invalid role: ${user.role}`);
            }

            // Check email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(user.email)) {
                errors.push(`User ${user.id} has invalid email format: ${user.email}`);
            }

            // Check for users without profile settings
            const profileSettings = await prisma.userProfileSettings.findUnique({
                where: { user_id: user.id }
            });
            if (!profileSettings) {
                warnings.push(`User ${user.email} has no profile settings`);
            }

            // Check for inactive users with active sessions
            if (!user.is_active) {
                const activeSessions = await prisma.userSession.count({
                    where: { user_id: user.id, is_active: true }
                });
                if (activeSessions > 0) {
                    warnings.push(`Inactive user ${user.email} has ${activeSessions} active sessions`);
                }
            }
        }

        return { errors, warnings };
    }

    /**
     * Validate Freemius data integrity
     */
    private async validateFreemiusData(): Promise<{ errors: string[]; warnings: string[] }> {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Check for orphaned installations by trying to find installations where the product doesn't exist
        const allInstallations = await prisma.freemiusInstallation.findMany({
            include: { product: true }
        });
        const orphanedInstallations = allInstallations.filter(installation => !installation.product);

        for (const installation of orphanedInstallations) {
            errors.push(`Installation ${installation.id} references non-existent product ${installation.plugin_id}`);
        }

        // Check for orphaned events by trying to find events where the product doesn't exist
        const allEvents = await prisma.freemiusEvent.findMany({
            include: { product: true }
        });
        const orphanedEvents = allEvents.filter(event => event.plugin_id && !event.product);

        for (const event of orphanedEvents) {
            errors.push(`Event ${event.id} references non-existent product ${event.plugin_id}`);
        }

        // Check for stale sync data
        const staleThreshold = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
        const staleProducts = await prisma.freemiusProduct.count({
            where: {
                last_synced_at: { lt: staleThreshold }
            }
        });

        if (staleProducts > 0) {
            warnings.push(`${staleProducts} products have not been synced in over 7 days`);
        }

        return { errors, warnings };
    }

    /**
     * Validate IP data integrity
     */
    private async validateIpData(): Promise<{ errors: string[]; warnings: string[] }> {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Check for expired IP data
        const now = new Date();
        const expiredIpData = await prisma.ipRegistryData.findMany({
            where: {
                data_expires_at: { lt: now }
            }
        });

        if (expiredIpData.length > 0) {
            warnings.push(`${expiredIpData.length} IP records have expired data`);
        }

        // Check for invalid IP addresses
        const ipData = await prisma.ipRegistryData.findMany();
        const ipv4Regex = /^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$/;
        const ipv6Regex = /^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

        for (const record of ipData) {
            if (!ipv4Regex.test(record.ip_address) && !ipv6Regex.test(record.ip_address)) {
                errors.push(`Invalid IP address format: ${record.ip_address}`);
            }
        }

        // Check for orphaned analysis requests
        const allRequests = await prisma.ipAnalysisRequest.findMany({
            include: { ip_data: true }
        });
        const orphanedRequests = allRequests.filter(request => request.ip_address && !request.ip_data);

        for (const request of orphanedRequests) {
            warnings.push(`Analysis request ${request.id} references non-existent IP data ${request.ip_address}`);
        }

        return { errors, warnings };
    }

    /**
     * Validate foreign key relationships
     */
    private async validateRelationships(): Promise<{ errors: string[]; warnings: string[] }> {
        const errors: string[] = [];
        const warnings: string[] = [];

        // Check user sessions for non-existent users
        const allSessions = await prisma.userSession.findMany({
            include: { user: true }
        });
        const invalidSessions = allSessions.filter(session => !session.user);

        for (const session of invalidSessions) {
            errors.push(`Session ${session.id} references non-existent user ${session.user_id}`);
        }

        // Check admin activities for non-existent users
        const allActivities = await prisma.adminActivity.findMany({
            include: { user: true }
        });
        const invalidActivities = allActivities.filter(activity => !activity.user);

        for (const activity of invalidActivities) {
            errors.push(`Admin activity ${activity.id} references non-existent user ${activity.user_id}`);
        }

        return { errors, warnings };
    }

    /**
     * Get summary statistics
     */
    private async getSummaryStatistics() {
        const totalUsers = await prisma.user.count();
        const validUsers = await prisma.user.count({
            where: {
                role: { in: Object.values(UserRole) }
            }
        });

        // Count orphaned records using the same logic as validation
        const allInstallationsForCount = await prisma.freemiusInstallation.findMany({
            include: { product: true }
        });
        const orphanedInstallations = allInstallationsForCount.filter(installation => !installation.product).length;

        const allEventsForCount = await prisma.freemiusEvent.findMany({
            include: { product: true }
        });
        const orphanedEvents = allEventsForCount.filter(event => event.plugin_id && !event.product).length;

        const expiredIpData = await prisma.ipRegistryData.count({
            where: {
                data_expires_at: { lt: new Date() }
            }
        });

        return {
            totalUsers,
            validUsers,
            invalidUsers: totalUsers - validUsers,
            orphanedRecords: orphanedInstallations + orphanedEvents,
            expiredData: expiredIpData
        };
    }

    /**
     * Fix common data integrity issues
     */
    async fixDataIntegrityIssues(): Promise<{
        fixed: string[];
        errors: string[];
    }> {
        const fixed: string[] = [];
        const errors: string[] = [];

        try {
            // Clean up expired sessions
            const expiredSessions = await prisma.userSession.deleteMany({
                where: {
                    expires_at: { lt: new Date() }
                }
            });
            if (expiredSessions.count > 0) {
                fixed.push(`Cleaned up ${expiredSessions.count} expired sessions`);
            }

            // Clean up expired CSRF tokens
            const expiredTokens = await prisma.csrfToken.deleteMany({
                where: {
                    expires_at: { lt: new Date() }
                }
            });
            if (expiredTokens.count > 0) {
                fixed.push(`Cleaned up ${expiredTokens.count} expired CSRF tokens`);
            }

            // Clean up expired password reset tokens
            const expiredResetTokens = await prisma.passwordResetToken.deleteMany({
                where: {
                    expires_at: { lt: new Date() }
                }
            });
            if (expiredResetTokens.count > 0) {
                fixed.push(`Cleaned up ${expiredResetTokens.count} expired password reset tokens`);
            }

            // Clean up expired rate limit tracking
            const expiredRateLimit = await prisma.rateLimitTracking.deleteMany({
                where: {
                    expires_at: { lt: new Date() }
                }
            });
            if (expiredRateLimit.count > 0) {
                fixed.push(`Cleaned up ${expiredRateLimit.count} expired rate limit records`);
            }

            // Create missing profile settings for users
            const usersWithoutProfiles = await prisma.user.findMany({
                where: {
                    profile_settings: null
                }
            });

            for (const user of usersWithoutProfiles) {
                await prisma.userProfileSettings.create({
                    data: {
                        user_id: user.id
                    }
                });
                fixed.push(`Created profile settings for user ${user.email}`);
            }

        } catch (error) {
            errors.push(`Error fixing data integrity issues: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        return { fixed, errors };
    }
}

export const dataValidator = new DataValidator();