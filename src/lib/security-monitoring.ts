import { prisma } from './prisma';
import { SecurityLogger, SecurityEventType } from './security-logger';
import { AuditSystem } from './audit-system';

// Security monitoring interfaces
export interface SecurityMetrics {
    realTimeStats: {
        activeUsers: number;
        activeSessions: number;
        failedLoginsLastHour: number;
        securityViolationsLastHour: number;
        suspiciousActivitiesLastHour: number;
        lastUpdated: Date;
    };
    authenticationMetrics: {
        totalLogins: number;
        successfulLogins: number;
        failedLogins: number;
        successRate: number;
        uniqueUsers: number;
        averageSessionDuration: number;
        timeRange: string;
    };
    sessionMetrics: {
        totalSessions: number;
        activeSessions: number;
        expiredSessions: number;
        invalidatedSessions: number;
        averageSessionsPerUser: number;
        sessionAnomalies: number;
        timeRange: string;
    };
    securityViolations: {
        rateLimitViolations: number;
        unauthorizedAccess: number;
        csrfViolations: number;
        inputValidationFailures: number;
        suspiciousIpActivity: number;
        timeRange: string;
    };
    topRisks: Array<{
        type: string;
        description: string;
        count: number;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
        lastOccurrence: Date;
    }>;
}

export interface SecurityDashboardData {
    metrics: SecurityMetrics;
    recentEvents: Array<{
        id: string;
        eventType: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
        description: string;
        timestamp: Date;
        ipAddress: string;
        userId?: string;
        email?: string;
    }>;
    alerts: Array<{
        id: string;
        type: string;
        severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
        title: string;
        description: string;
        count: number;
        firstSeen: Date;
        lastSeen: Date;
        status: 'ACTIVE' | 'INVESTIGATING' | 'RESOLVED';
    }>;
    trends: {
        authenticationTrend: Array<{ time: string; successful: number; failed: number }>;
        sessionTrend: Array<{ time: string; created: number; expired: number }>;
        violationTrend: Array<{ time: string; count: number; type: string }>;
    };
}

export interface FailedLoginAttempt {
    id: string;
    email: string;
    ipAddress: string;
    userAgent?: string;
    timestamp: Date;
    reason: string;
    isBlocked: boolean;
    attemptCount: number;
}

export interface SessionAnomaly {
    id: string;
    userId: string;
    sessionId: string;
    anomalyType: 'IP_CHANGE' | 'LOCATION_CHANGE' | 'DEVICE_CHANGE' | 'UNUSUAL_ACTIVITY' | 'CONCURRENT_LIMIT';
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    timestamp: Date;
    ipAddress: string;
    previousIp?: string;
    userAgent?: string;
    riskScore: number;
}

/**
 * Security Monitoring Service
 * 
 * Provides real-time security metrics collection, failed authentication tracking,
 * session anomaly detection, and security dashboard data aggregation.
 */
export class SecurityMonitoring {
    /**
     * Get comprehensive security dashboard data
     */
    static async getSecurityDashboard(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<SecurityDashboardData> {
        try {
            const [metrics, recentEvents, alerts, trends] = await Promise.all([
                this.getSecurityMetrics(timeRange),
                this.getRecentSecurityEvents(50),
                this.getActiveSecurityAlerts(),
                this.getSecurityTrends(timeRange),
            ]);

            return {
                metrics,
                recentEvents,
                alerts,
                trends,
            };
        } catch (error) {
            console.error('Failed to get security dashboard data:', error);
            throw new Error('Failed to retrieve security dashboard data');
        }
    }

    /**
     * Get real-time security metrics
     */
    static async getSecurityMetrics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<SecurityMetrics> {
        try {
            const now = new Date();
            let startDate: Date;
            let timeRangeLabel: string;

            switch (timeRange) {
                case 'hour':
                    startDate = new Date(now.getTime() - 60 * 60 * 1000);
                    timeRangeLabel = 'Last Hour';
                    break;
                case 'day':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    timeRangeLabel = 'Last 24 Hours';
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    timeRangeLabel = 'Last 7 Days';
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    timeRangeLabel = 'Last 30 Days';
                    break;
            }

            const lastHour = new Date(now.getTime() - 60 * 60 * 1000);

            // Get real-time stats
            const [
                activeUsers,
                activeSessions,
                failedLoginsLastHour,
                securityViolationsLastHour,
                suspiciousActivitiesLastHour,
            ] = await Promise.all([
                // Active users (users with sessions in last hour)
                prisma.userSession.findMany({
                    where: {
                        expires_at: { gt: now },
                        created_at: { gte: lastHour },
                    },
                    select: { user_id: true },
                    distinct: ['user_id'],
                }),

                // Active sessions
                prisma.userSession.count({
                    where: { expires_at: { gt: now } },
                }),

                // Failed logins in last hour
                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.LOGIN_FAILURE,
                        created_at: { gte: lastHour },
                    },
                }),

                // Security violations in last hour
                prisma.securityAuditLog.count({
                    where: {
                        event_type: {
                            in: [
                                SecurityEventType.RATE_LIMIT_EXCEEDED,
                                SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                                SecurityEventType.CSRF_TOKEN_INVALID,
                                SecurityEventType.INPUT_VALIDATION_FAILED,
                            ],
                        },
                        created_at: { gte: lastHour },
                    },
                }),

                // Suspicious activities in last hour
                prisma.securityAuditLog.count({
                    where: {
                        event_type: {
                            in: [
                                SecurityEventType.SESSION_IP_MISMATCH,
                                SecurityEventType.SUSPICIOUS_SESSION_ACTIVITY,
                                SecurityEventType.ACCOUNT_LOCKED,
                            ],
                        },
                        created_at: { gte: lastHour },
                    },
                }),
            ]);

            // Get authentication metrics for the time range
            const [
                totalLogins,
                successfulLogins,
                failedLogins,
                uniqueUsers,
                sessionStats,
            ] = await Promise.all([
                prisma.securityAuditLog.count({
                    where: {
                        event_type: {
                            in: [SecurityEventType.LOGIN_SUCCESS, SecurityEventType.LOGIN_FAILURE],
                        },
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.LOGIN_SUCCESS,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.LOGIN_FAILURE,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.findMany({
                    where: {
                        event_type: SecurityEventType.LOGIN_SUCCESS,
                        created_at: { gte: startDate },
                        user_id: { not: null },
                    },
                    select: { user_id: true },
                    distinct: ['user_id'],
                }),

                prisma.userSession.aggregate({
                    where: {
                        created_at: { gte: startDate },
                    },
                    _avg: { max_idle_time: true },
                    _count: { id: true },
                }),
            ]);

            // Get session metrics
            const [
                totalSessions,
                currentActiveSessions,
                expiredSessions,
                invalidatedSessions,
                sessionAnomalies,
            ] = await Promise.all([
                prisma.userSession.count({
                    where: { created_at: { gte: startDate } },
                }),

                prisma.userSession.count({
                    where: { expires_at: { gt: now } },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.SESSION_EXPIRED,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.SESSION_INVALIDATED,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: {
                            in: [
                                SecurityEventType.SESSION_IP_MISMATCH,
                                SecurityEventType.SUSPICIOUS_SESSION_ACTIVITY,
                            ],
                        },
                        created_at: { gte: startDate },
                    },
                }),
            ]);

            // Get security violation metrics
            const [
                rateLimitViolations,
                unauthorizedAccess,
                csrfViolations,
                inputValidationFailures,
                suspiciousIpActivity,
            ] = await Promise.all([
                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.RATE_LIMIT_EXCEEDED,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.CSRF_TOKEN_INVALID,
                        created_at: { gte: startDate },
                    },
                }),

                prisma.securityAuditLog.count({
                    where: {
                        event_type: SecurityEventType.INPUT_VALIDATION_FAILED,
                        created_at: { gte: startDate },
                    },
                }),

                // Suspicious IP activity (high volume from single IPs)
                prisma.securityAuditLog.groupBy({
                    by: ['ip_address'],
                    where: { created_at: { gte: startDate } },
                    _count: { ip_address: true },
                    having: { ip_address: { _count: { gt: 50 } } },
                }),
            ]);

            // Get top risks
            const topRisks = await this.getTopSecurityRisks(startDate);

            const successRate = totalLogins > 0 ? (successfulLogins / totalLogins) * 100 : 0;
            const averageSessionDuration = sessionStats._avg.max_idle_time || 0;
            const averageSessionsPerUser = uniqueUsers.length > 0 ? totalSessions / uniqueUsers.length : 0;

            return {
                realTimeStats: {
                    activeUsers: activeUsers.length,
                    activeSessions: activeSessions,
                    failedLoginsLastHour,
                    securityViolationsLastHour,
                    suspiciousActivitiesLastHour,
                    lastUpdated: now,
                },
                authenticationMetrics: {
                    totalLogins,
                    successfulLogins,
                    failedLogins,
                    successRate: Math.round(successRate * 100) / 100,
                    uniqueUsers: uniqueUsers.length,
                    averageSessionDuration: Math.round(averageSessionDuration / 60), // Convert to minutes
                    timeRange: timeRangeLabel,
                },
                sessionMetrics: {
                    totalSessions,
                    activeSessions: currentActiveSessions,
                    expiredSessions,
                    invalidatedSessions,
                    averageSessionsPerUser: Math.round(averageSessionsPerUser * 100) / 100,
                    sessionAnomalies,
                    timeRange: timeRangeLabel,
                },
                securityViolations: {
                    rateLimitViolations,
                    unauthorizedAccess,
                    csrfViolations,
                    inputValidationFailures,
                    suspiciousIpActivity: suspiciousIpActivity.length,
                    timeRange: timeRangeLabel,
                },
                topRisks,
            };
        } catch (error) {
            console.error('Failed to get security metrics:', error);
            throw new Error('Failed to retrieve security metrics');
        }
    }

    /**
     * Track and analyze failed login attempts
     */
    static async getFailedLoginAttempts(
        timeRange: 'hour' | 'day' | 'week' = 'day',
        limit: number = 100
    ): Promise<FailedLoginAttempt[]> {
        try {
            const now = new Date();
            let startDate: Date;

            switch (timeRange) {
                case 'hour':
                    startDate = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case 'day':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
            }

            const failedLogins = await prisma.securityAuditLog.findMany({
                where: {
                    event_type: SecurityEventType.LOGIN_FAILURE,
                    created_at: { gte: startDate },
                },
                orderBy: { created_at: 'desc' },
                take: limit,
            });

            // Group by email and IP to get attempt counts
            const attemptCounts = new Map<string, number>();
            const blockedEmails = new Set<string>();

            // Get account lockout information
            const lockouts = await prisma.accountLockout.findMany({
                where: {
                    locked_until: { gt: now },
                },
            });

            lockouts.forEach(lockout => {
                blockedEmails.add(lockout.email);
            });

            // Count attempts per email+IP combination
            failedLogins.forEach(log => {
                const key = `${log.email}:${log.ip_address}`;
                attemptCounts.set(key, (attemptCounts.get(key) || 0) + 1);
            });

            return failedLogins.map((log: any) => {
                const key = `${log.email}:${log.ip_address}`;
                const attemptCount = attemptCounts.get(key) || 1;
                const isBlocked = blockedEmails.has(log.email);

                return {
                    id: log.id,
                    email: log.email,
                    ipAddress: log.ip_address,
                    userAgent: log.user_agent,
                    timestamp: log.created_at,
                    reason: log.error_message || 'Authentication failed',
                    isBlocked,
                    attemptCount,
                };
            });
        } catch (error) {
            console.error('Failed to get failed login attempts:', error);
            throw new Error('Failed to retrieve failed login attempts');
        }
    }

    /**
     * Detect and report session anomalies
     */
    static async detectSessionAnomalies(
        timeRange: 'hour' | 'day' | 'week' = 'day',
        limit: number = 50
    ): Promise<SessionAnomaly[]> {
        try {
            const now = new Date();
            let startDate: Date;

            switch (timeRange) {
                case 'hour':
                    startDate = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case 'day':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
            }

            const anomalies: SessionAnomaly[] = [];

            // Get session-related security events
            const sessionEvents = await prisma.securityAuditLog.findMany({
                where: {
                    event_type: {
                        in: [
                            SecurityEventType.SESSION_IP_MISMATCH,
                            SecurityEventType.SUSPICIOUS_SESSION_ACTIVITY,
                            SecurityEventType.CONCURRENT_SESSION_LIMIT,
                        ],
                    },
                    created_at: { gte: startDate },
                },
                orderBy: { created_at: 'desc' },
                take: limit,
            });

            for (const event of sessionEvents) {
                let anomalyType: SessionAnomaly['anomalyType'];
                let severity: SessionAnomaly['severity'];
                let riskScore: number;

                switch (event.event_type) {
                    case SecurityEventType.SESSION_IP_MISMATCH:
                        anomalyType = 'IP_CHANGE';
                        severity = 'HIGH';
                        riskScore = 80;
                        break;
                    case SecurityEventType.SUSPICIOUS_SESSION_ACTIVITY:
                        anomalyType = 'UNUSUAL_ACTIVITY';
                        severity = 'MEDIUM';
                        riskScore = 60;
                        break;
                    case SecurityEventType.CONCURRENT_SESSION_LIMIT:
                        anomalyType = 'CONCURRENT_LIMIT';
                        severity = 'MEDIUM';
                        riskScore = 50;
                        break;
                    default:
                        continue;
                }

                const details = event.details as any;
                anomalies.push({
                    id: event.id,
                    userId: event.user_id || 'unknown',
                    sessionId: details?.sessionId || 'unknown',
                    anomalyType,
                    description: this.generateAnomalyDescription(anomalyType, details),
                    severity,
                    timestamp: event.created_at,
                    ipAddress: event.ip_address,
                    previousIp: details?.previousIp,
                    userAgent: event.user_agent,
                    riskScore,
                });
            }

            return anomalies;
        } catch (error) {
            console.error('Failed to detect session anomalies:', error);
            throw new Error('Failed to detect session anomalies');
        }
    }

    /**
     * Get recent security events for dashboard display
     */
    private static async getRecentSecurityEvents(limit: number = 50): Promise<SecurityDashboardData['recentEvents']> {
        try {
            const events = await prisma.securityAuditLog.findMany({
                orderBy: { created_at: 'desc' },
                take: limit,
            });

            return events.map((event: any) => ({
                id: event.id,
                eventType: event.event_type,
                severity: this.getEventSeverity(event.event_type, event.success),
                description: this.generateEventDescription(event),
                timestamp: event.created_at,
                ipAddress: event.ip_address,
                userId: event.user_id,
                email: event.email,
            }));
        } catch (error) {
            console.error('Failed to get recent security events:', error);
            return [];
        }
    }

    /**
     * Get active security alerts
     */
    private static async getActiveSecurityAlerts(): Promise<SecurityDashboardData['alerts']> {
        try {
            // Use the AuditSystem to detect security alerts
            const auditReport = await AuditSystem.queryAuditLogs({
                timeRange: 'last_24h',
                includeStats: false,
            });

            return auditReport.securityAlerts.map(alert => ({
                id: alert.id,
                type: alert.type,
                severity: alert.severity,
                title: alert.title,
                description: alert.description,
                count: alert.eventCount,
                firstSeen: alert.firstSeen,
                lastSeen: alert.lastSeen,
                status: alert.status,
            }));
        } catch (error) {
            console.error('Failed to get active security alerts:', error);
            return [];
        }
    }

    /**
     * Get security trends for visualization
     */
    private static async getSecurityTrends(timeRange: 'hour' | 'day' | 'week' | 'month'): Promise<SecurityDashboardData['trends']> {
        try {
            const now = new Date();
            let startDate: Date;
            let intervalMinutes: number;

            switch (timeRange) {
                case 'hour':
                    startDate = new Date(now.getTime() - 60 * 60 * 1000);
                    intervalMinutes = 5; // 5-minute intervals
                    break;
                case 'day':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    intervalMinutes = 60; // 1-hour intervals
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    intervalMinutes = 6 * 60; // 6-hour intervals
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    intervalMinutes = 24 * 60; // 1-day intervals
                    break;
            }

            // Generate time buckets
            const buckets: string[] = [];
            const current = new Date(startDate);
            while (current <= now) {
                buckets.push(current.toISOString());
                current.setMinutes(current.getMinutes() + intervalMinutes);
            }

            // Get authentication trends
            const authTrend = await Promise.all(
                buckets.map(async (time, index) => {
                    const bucketStart = new Date(time);
                    const bucketEnd = new Date(bucketStart.getTime() + intervalMinutes * 60 * 1000);

                    const [successful, failed] = await Promise.all([
                        prisma.securityAuditLog.count({
                            where: {
                                event_type: SecurityEventType.LOGIN_SUCCESS,
                                created_at: { gte: bucketStart, lt: bucketEnd },
                            },
                        }),
                        prisma.securityAuditLog.count({
                            where: {
                                event_type: SecurityEventType.LOGIN_FAILURE,
                                created_at: { gte: bucketStart, lt: bucketEnd },
                            },
                        }),
                    ]);

                    return {
                        time: bucketStart.toISOString(),
                        successful,
                        failed,
                    };
                })
            );

            // Get session trends
            const sessionTrend = await Promise.all(
                buckets.map(async (time) => {
                    const bucketStart = new Date(time);
                    const bucketEnd = new Date(bucketStart.getTime() + intervalMinutes * 60 * 1000);

                    const [created, expired] = await Promise.all([
                        prisma.securityAuditLog.count({
                            where: {
                                event_type: SecurityEventType.SESSION_CREATED,
                                created_at: { gte: bucketStart, lt: bucketEnd },
                            },
                        }),
                        prisma.securityAuditLog.count({
                            where: {
                                event_type: SecurityEventType.SESSION_EXPIRED,
                                created_at: { gte: bucketStart, lt: bucketEnd },
                            },
                        }),
                    ]);

                    return {
                        time: bucketStart.toISOString(),
                        created,
                        expired,
                    };
                })
            );

            // Get violation trends
            const violationTrend = await Promise.all(
                buckets.map(async (time) => {
                    const bucketStart = new Date(time);
                    const bucketEnd = new Date(bucketStart.getTime() + intervalMinutes * 60 * 1000);

                    const count = await prisma.securityAuditLog.count({
                        where: {
                            event_type: {
                                in: [
                                    SecurityEventType.RATE_LIMIT_EXCEEDED,
                                    SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                                    SecurityEventType.CSRF_TOKEN_INVALID,
                                ],
                            },
                            created_at: { gte: bucketStart, lt: bucketEnd },
                        },
                    });

                    return {
                        time: bucketStart.toISOString(),
                        count,
                        type: 'security_violation',
                    };
                })
            );

            return {
                authenticationTrend: authTrend,
                sessionTrend: sessionTrend,
                violationTrend: violationTrend,
            };
        } catch (error) {
            console.error('Failed to get security trends:', error);
            return {
                authenticationTrend: [],
                sessionTrend: [],
                violationTrend: [],
            };
        }
    }

    /**
     * Get top security risks
     */
    private static async getTopSecurityRisks(startDate: Date): Promise<SecurityMetrics['topRisks']> {
        try {
            const risks: SecurityMetrics['topRisks'] = [];

            // Get event type counts for risk assessment
            const eventCounts = await prisma.securityAuditLog.groupBy({
                by: ['event_type'],
                where: {
                    created_at: { gte: startDate },
                    success: false,
                },
                _count: { event_type: true },
                _max: { created_at: true },
                orderBy: { _count: { event_type: 'desc' } },
                take: 10,
            });

            for (const eventCount of eventCounts) {
                const riskInfo = this.getRiskInfo(eventCount.event_type);
                if (riskInfo) {
                    risks.push({
                        type: eventCount.event_type,
                        description: riskInfo.description,
                        count: (eventCount as any)._count.event_type,
                        severity: riskInfo.severity,
                        lastOccurrence: (eventCount as any)._max.created_at,
                    });
                }
            }

            return risks;
        } catch (error) {
            console.error('Failed to get top security risks:', error);
            return [];
        }
    }

    // Helper methods
    private static getEventSeverity(eventType: string, success: boolean): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
        if (!success) {
            switch (eventType) {
                case SecurityEventType.LOGIN_FAILURE:
                case SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT:
                    return 'MEDIUM';
                case SecurityEventType.SESSION_IP_MISMATCH:
                case SecurityEventType.ACCOUNT_LOCKED:
                    return 'HIGH';
                case SecurityEventType.RATE_LIMIT_EXCEEDED:
                case SecurityEventType.CSRF_TOKEN_INVALID:
                    return 'CRITICAL';
                default:
                    return 'LOW';
            }
        }
        return 'LOW';
    }

    private static generateEventDescription(event: any): string {
        const eventType = event.event_type;
        const email = event.email || 'Unknown user';
        const ip = event.ip_address;

        switch (eventType) {
            case SecurityEventType.LOGIN_SUCCESS:
                return `Successful login by ${email} from ${ip}`;
            case SecurityEventType.LOGIN_FAILURE:
                return `Failed login attempt for ${email} from ${ip}`;
            case SecurityEventType.ACCOUNT_LOCKED:
                return `Account ${email} locked due to multiple failed attempts from ${ip}`;
            case SecurityEventType.SESSION_IP_MISMATCH:
                return `Session IP mismatch detected for ${email} (${ip})`;
            case SecurityEventType.RATE_LIMIT_EXCEEDED:
                return `Rate limit exceeded from ${ip}`;
            case SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT:
                return `Unauthorized access attempt from ${ip}`;
            default:
                return `Security event: ${eventType.replace(/_/g, ' ').toLowerCase()}`;
        }
    }

    private static generateAnomalyDescription(type: SessionAnomaly['anomalyType'], details: any): string {
        switch (type) {
            case 'IP_CHANGE':
                return `Session IP changed from ${details?.previousIp || 'unknown'} to current IP`;
            case 'LOCATION_CHANGE':
                return `Unusual location change detected for session`;
            case 'DEVICE_CHANGE':
                return `Device fingerprint change detected for session`;
            case 'UNUSUAL_ACTIVITY':
                return `Suspicious session activity pattern detected`;
            case 'CONCURRENT_LIMIT':
                return `Concurrent session limit exceeded`;
            default:
                return 'Unknown session anomaly detected';
        }
    }

    private static getRiskInfo(eventType: string): { description: string; severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' } | null {
        switch (eventType) {
            case SecurityEventType.LOGIN_FAILURE:
                return { description: 'Multiple failed login attempts', severity: 'MEDIUM' };
            case SecurityEventType.ACCOUNT_LOCKED:
                return { description: 'Account lockouts due to security violations', severity: 'HIGH' };
            case SecurityEventType.SESSION_IP_MISMATCH:
                return { description: 'Session hijacking attempts detected', severity: 'CRITICAL' };
            case SecurityEventType.RATE_LIMIT_EXCEEDED:
                return { description: 'Potential DDoS or brute force attacks', severity: 'HIGH' };
            case SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT:
                return { description: 'Privilege escalation attempts', severity: 'CRITICAL' };
            case SecurityEventType.CSRF_TOKEN_INVALID:
                return { description: 'CSRF attack attempts', severity: 'HIGH' };
            case SecurityEventType.INPUT_VALIDATION_FAILED:
                return { description: 'Potential injection attacks', severity: 'MEDIUM' };
            default:
                return null;
        }
    }
}

// Export convenience functions
export const getSecurityDashboard = (timeRange?: 'hour' | 'day' | 'week' | 'month') =>
    SecurityMonitoring.getSecurityDashboard(timeRange);

export const getFailedLoginAttempts = (timeRange?: 'hour' | 'day' | 'week', limit?: number) =>
    SecurityMonitoring.getFailedLoginAttempts(timeRange, limit);

export const detectSessionAnomalies = (timeRange?: 'hour' | 'day' | 'week', limit?: number) =>
    SecurityMonitoring.detectSessionAnomalies(timeRange, limit);