import { prisma } from './prisma';

export interface RetentionPolicy {
    name: string;
    description: string;
    table: string;
    retentionPeriodDays: number;
    dateField: string;
    conditions?: any;
    enabled: boolean;
}

export interface RetentionResult {
    policyName: string;
    success: boolean;
    recordsDeleted: number;
    recordsArchived: number;
    errors: string[];
    executionTime: number;
}

export class DataRetentionManager {
    /**
     * Get all data retention policies
     */
    getRetentionPolicies(): RetentionPolicy[] {
        return [
            {
                name: 'security_audit_logs',
                description: 'Security audit logs older than 1 year',
                table: 'security_audit_log',
                retentionPeriodDays: 365,
                dateField: 'created_at',
                enabled: true
            },
            {
                name: 'admin_activities',
                description: 'Admin activities older than 6 months',
                table: 'admin_activities',
                retentionPeriodDays: 180,
                dateField: 'created_at',
                enabled: true
            },
            {
                name: 'user_sessions',
                description: 'Expired user sessions older than 30 days',
                table: 'user_sessions',
                retentionPeriodDays: 30,
                dateField: 'expires_at',
                conditions: { is_active: false },
                enabled: true
            },
            {
                name: 'password_history',
                description: 'Password history older than 2 years',
                table: 'password_history',
                retentionPeriodDays: 730,
                dateField: 'created_at',
                enabled: true
            },
            {
                name: 'ip_analysis_requests',
                description: 'IP analysis requests older than 90 days',
                table: 'ip_analysis_requests',
                retentionPeriodDays: 90,
                dateField: 'requested_at',
                enabled: true
            },
            {
                name: 'freemius_events_processed',
                description: 'Processed Freemius events older than 60 days',
                table: 'freemius_events',
                retentionPeriodDays: 60,
                dateField: 'processed_at',
                conditions: { processing_status: 'processed' },
                enabled: true
            },
            {
                name: 'expired_ip_data',
                description: 'Expired IP registry data older than 30 days past expiration',
                table: 'ip_registry_data',
                retentionPeriodDays: 30,
                dateField: 'data_expires_at',
                enabled: true
            },
            {
                name: 'rate_limit_tracking',
                description: 'Expired rate limit tracking records',
                table: 'rate_limit_tracking',
                retentionPeriodDays: 7,
                dateField: 'expires_at',
                enabled: true
            },
            {
                name: 'csrf_tokens',
                description: 'Expired CSRF tokens',
                table: 'csrf_tokens',
                retentionPeriodDays: 1,
                dateField: 'expires_at',
                enabled: true
            },
            {
                name: 'password_reset_tokens',
                description: 'Used or expired password reset tokens older than 7 days',
                table: 'password_reset_tokens',
                retentionPeriodDays: 7,
                dateField: 'expires_at',
                enabled: true
            }
        ];
    }

    /**
     * Enforce all retention policies
     */
    async enforceAllPolicies(): Promise<RetentionResult[]> {
        const results: RetentionResult[] = [];
        const policies = this.getRetentionPolicies().filter(policy => policy.enabled);

        for (const policy of policies) {
            try {
                const result = await this.enforcePolicy(policy);
                results.push(result);
            } catch (error) {
                results.push({
                    policyName: policy.name,
                    success: false,
                    recordsDeleted: 0,
                    recordsArchived: 0,
                    errors: [error instanceof Error ? error.message : 'Unknown error'],
                    executionTime: 0
                });
            }
        }

        return results;
    }

    /**
     * Enforce a specific retention policy
     */
    async enforcePolicy(policy: RetentionPolicy): Promise<RetentionResult> {
        const startTime = Date.now();
        const errors: string[] = [];
        let recordsDeleted = 0;
        let recordsArchived = 0;

        try {
            const cutoffDate = new Date(Date.now() - policy.retentionPeriodDays * 24 * 60 * 60 * 1000);

            // Build the where clause
            const whereClause: any = {
                [policy.dateField]: { lt: cutoffDate }
            };

            // Add additional conditions if specified
            if (policy.conditions) {
                Object.assign(whereClause, policy.conditions);
            }

            // Execute deletion based on table
            switch (policy.table) {
                case 'security_audit_log':
                    const auditResult = await prisma.securityAuditLog.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = auditResult.count;
                    break;

                case 'admin_activities':
                    const activitiesResult = await prisma.adminActivity.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = activitiesResult.count;
                    break;

                case 'user_sessions':
                    const sessionsResult = await prisma.userSession.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = sessionsResult.count;
                    break;

                case 'password_history':
                    const passwordResult = await prisma.passwordHistory.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = passwordResult.count;
                    break;

                case 'ip_analysis_requests':
                    const ipRequestsResult = await prisma.ipAnalysisRequest.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = ipRequestsResult.count;
                    break;

                case 'freemius_events':
                    const eventsResult = await prisma.freemiusEvent.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = eventsResult.count;
                    break;

                case 'ip_registry_data':
                    const ipDataResult = await prisma.ipRegistryData.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = ipDataResult.count;
                    break;

                case 'rate_limit_tracking':
                    const rateLimitResult = await prisma.rateLimitTracking.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = rateLimitResult.count;
                    break;

                case 'csrf_tokens':
                    const csrfResult = await prisma.csrfToken.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = csrfResult.count;
                    break;

                case 'password_reset_tokens':
                    const resetTokensResult = await prisma.passwordResetToken.deleteMany({
                        where: whereClause
                    });
                    recordsDeleted = resetTokensResult.count;
                    break;

                default:
                    throw new Error(`Unknown table: ${policy.table}`);
            }

            return {
                policyName: policy.name,
                success: true,
                recordsDeleted,
                recordsArchived,
                errors,
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                policyName: policy.name,
                success: false,
                recordsDeleted: 0,
                recordsArchived: 0,
                errors,
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Get retention policy statistics
     */
    async getRetentionStatistics(): Promise<{
        policy: RetentionPolicy;
        recordsToDelete: number;
        oldestRecord?: Date;
        newestRecord?: Date;
    }[]> {
        const policies = this.getRetentionPolicies().filter(policy => policy.enabled);
        const statistics = [];

        for (const policy of policies) {
            try {
                const cutoffDate = new Date(Date.now() - policy.retentionPeriodDays * 24 * 60 * 60 * 1000);

                // Build the where clause
                const whereClause: any = {
                    [policy.dateField]: { lt: cutoffDate }
                };

                // Add additional conditions if specified
                if (policy.conditions) {
                    Object.assign(whereClause, policy.conditions);
                }

                let recordsToDelete = 0;
                let oldestRecord: Date | undefined;
                let newestRecord: Date | undefined;

                // Count records based on table
                switch (policy.table) {
                    case 'security_audit_log':
                        recordsToDelete = await prisma.securityAuditLog.count({ where: whereClause });
                        const auditStats = await prisma.securityAuditLog.aggregate({
                            where: whereClause,
                            _min: { created_at: true },
                            _max: { created_at: true }
                        });
                        oldestRecord = auditStats._min.created_at || undefined;
                        newestRecord = auditStats._max.created_at || undefined;
                        break;

                    case 'admin_activities':
                        recordsToDelete = await prisma.adminActivity.count({ where: whereClause });
                        const activityStats = await prisma.adminActivity.aggregate({
                            where: whereClause,
                            _min: { created_at: true },
                            _max: { created_at: true }
                        });
                        oldestRecord = activityStats._min.created_at || undefined;
                        newestRecord = activityStats._max.created_at || undefined;
                        break;

                    case 'user_sessions':
                        recordsToDelete = await prisma.userSession.count({ where: whereClause });
                        break;

                    case 'password_history':
                        recordsToDelete = await prisma.passwordHistory.count({ where: whereClause });
                        break;

                    case 'ip_analysis_requests':
                        recordsToDelete = await prisma.ipAnalysisRequest.count({ where: whereClause });
                        break;

                    case 'freemius_events':
                        recordsToDelete = await prisma.freemiusEvent.count({ where: whereClause });
                        break;

                    case 'ip_registry_data':
                        recordsToDelete = await prisma.ipRegistryData.count({ where: whereClause });
                        break;

                    case 'rate_limit_tracking':
                        recordsToDelete = await prisma.rateLimitTracking.count({ where: whereClause });
                        break;

                    case 'csrf_tokens':
                        recordsToDelete = await prisma.csrfToken.count({ where: whereClause });
                        break;

                    case 'password_reset_tokens':
                        recordsToDelete = await prisma.passwordResetToken.count({ where: whereClause });
                        break;
                }

                statistics.push({
                    policy,
                    recordsToDelete,
                    oldestRecord,
                    newestRecord
                });

            } catch (error) {
                statistics.push({
                    policy,
                    recordsToDelete: 0,
                    oldestRecord: undefined,
                    newestRecord: undefined
                });
            }
        }

        return statistics;
    }

    /**
     * Preview what would be deleted by retention policies without actually deleting
     */
    async previewRetentionEnforcement(): Promise<{
        policy: RetentionPolicy;
        recordsToDelete: number;
        sampleRecords: any[];
    }[]> {
        const policies = this.getRetentionPolicies().filter(policy => policy.enabled);
        const previews = [];

        for (const policy of policies) {
            try {
                const cutoffDate = new Date(Date.now() - policy.retentionPeriodDays * 24 * 60 * 60 * 1000);

                // Build the where clause
                const whereClause: any = {
                    [policy.dateField]: { lt: cutoffDate }
                };

                // Add additional conditions if specified
                if (policy.conditions) {
                    Object.assign(whereClause, policy.conditions);
                }

                let recordsToDelete = 0;
                let sampleRecords: any[] = [];

                // Get sample records based on table
                switch (policy.table) {
                    case 'security_audit_log':
                        recordsToDelete = await prisma.securityAuditLog.count({ where: whereClause });
                        sampleRecords = await prisma.securityAuditLog.findMany({
                            where: whereClause,
                            take: 5,
                            select: { id: true, event_type: true, created_at: true }
                        });
                        break;

                    case 'admin_activities':
                        recordsToDelete = await prisma.adminActivity.count({ where: whereClause });
                        sampleRecords = await prisma.adminActivity.findMany({
                            where: whereClause,
                            take: 5,
                            select: { id: true, action: true, created_at: true }
                        });
                        break;

                    // Add other cases as needed...
                }

                previews.push({
                    policy,
                    recordsToDelete,
                    sampleRecords
                });

            } catch (error) {
                previews.push({
                    policy,
                    recordsToDelete: 0,
                    sampleRecords: []
                });
            }
        }

        return previews;
    }
}

export const dataRetentionManager = new DataRetentionManager();