import prisma from './prisma';
import { Logger } from './logger';

// Define our own Store interface to avoid express-session dependency
interface SessionStore {
    get(sessionId: string, callback: (err?: any, session?: any) => void): void;
    set(sessionId: string, sessionData: any, callback?: (err?: any) => void): void;
    destroy(sessionId: string, callback?: (err?: any) => void): void;
    touch?(sessionId: string, sessionData: any, callback?: (err?: any) => void): void;
    all?(callback: (err?: any, sessions?: { [sessionId: string]: any } | null) => void): void;
    length?(callback: (err?: any, length?: number) => void): void;
    clear?(callback?: (err?: any) => void): void;
}

export interface SessionStoreData {
    userId: string;
    email: string;
    role: string;
    firstName: string;
    lastName: string;
    isActive: boolean;
    requiresPasswordChange: boolean;
    ipAddress: string;
    userAgent?: string;
    deviceFingerprint?: string;
    location?: string;
    lastActivity: Date;
    createdAt: Date;
    securityFlags?: Record<string, any>;
}

export interface SessionStoreOptions {
    tableName?: string;
    createDatabaseTable?: boolean;
    ttl?: number; // Time to live in seconds
    disableTouch?: boolean;
    serializer?: {
        stringify: (obj: any) => string;
        parse: (str: string) => any;
    };
}

/**
 * Prisma-based session store for Express sessions using the existing UserSession model
 * Implements a session store interface for database-backed session storage
 */
export class PrismaSessionStore implements SessionStore {
    private options: Required<SessionStoreOptions>;

    constructor(options: SessionStoreOptions = {}) {

        this.options = {
            tableName: 'user_sessions',
            createDatabaseTable: false, // We use existing Prisma schema
            ttl: 24 * 60 * 60, // 24 hours default
            disableTouch: false,
            serializer: {
                stringify: JSON.stringify,
                parse: JSON.parse
            },
            ...options
        };

        Logger.info('PrismaSessionStore initialized', {
            ttl: this.options.ttl,
            disableTouch: this.options.disableTouch
        });
    }

    /**
     * Get session data by session ID
     */
    get(sessionId: string, callback: (err?: any, session?: SessionStoreData | null) => void): void {
        this.getAsync(sessionId)
            .then(session => callback(null, session))
            .catch(err => {
                Logger.error('Session get error', err, { sessionId: sessionId.substring(0, 8) + '...' });
                callback(err);
            });
    }

    /**
     * Async version of get method
     */
    async getAsync(sessionId: string): Promise<SessionStoreData | null> {
        try {
            const session = await prisma.userSession.findUnique({
                where: {
                    session_token: sessionId,
                    is_active: true
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            first_name: true,
                            last_name: true,
                            role: true,
                            is_active: true,
                            requires_password_change: true
                        }
                    }
                }
            });

            if (!session) {
                return null;
            }

            // Check if session has expired
            const now = new Date();
            if (session.expires_at < now) {
                // Session expired, clean it up
                await this.destroyAsync(sessionId);
                return null;
            }

            // Check idle timeout
            const idleTimeMs = now.getTime() - session.last_activity.getTime();
            const maxIdleTimeMs = session.max_idle_time * 1000;

            if (idleTimeMs > maxIdleTimeMs) {
                // Session idle timeout exceeded
                await this.destroyAsync(sessionId);
                return null;
            }

            // Check if user is still active
            if (!session.user.is_active) {
                // User deactivated, invalidate session
                await this.destroyAsync(sessionId);
                return null;
            }

            return {
                userId: session.user.id,
                email: session.user.email,
                role: session.user.role,
                firstName: session.user.first_name,
                lastName: session.user.last_name,
                isActive: session.user.is_active,
                requiresPasswordChange: session.user.requires_password_change,
                ipAddress: session.ip_address,
                userAgent: session.user_agent || undefined,
                deviceFingerprint: session.device_fingerprint || undefined,
                location: session.location || undefined,
                lastActivity: session.last_activity,
                createdAt: session.created_at,
                securityFlags: session.security_flags as Record<string, any> || undefined
            };

        } catch (error) {
            Logger.error('Failed to get session from database', error, {
                sessionId: sessionId.substring(0, 8) + '...'
            });
            throw error;
        }
    }

    /**
     * Set/update session data
     */
    set(sessionId: string, sessionData: any, callback?: (err?: any) => void): void {
        this.setAsync(sessionId, sessionData)
            .then(() => callback?.())
            .catch(err => {
                Logger.error('Session set error', err, { sessionId: sessionId.substring(0, 8) + '...' });
                callback?.(err);
            });
    }

    /**
     * Async version of set method
     */
    async setAsync(sessionId: string, sessionData: any): Promise<void> {
        try {
            const now = new Date();
            const expiresAt = new Date(now.getTime() + (this.options.ttl * 1000));

            // Extract session data
            const {
                userId,
                ipAddress,
                userAgent,
                deviceFingerprint,
                location,
                securityFlags,
                maxIdleTime = this.options.ttl,
                absoluteTimeout = this.options.ttl
            } = sessionData;

            if (!userId || !ipAddress) {
                throw new Error('Session data must include userId and ipAddress');
            }

            // Upsert session record
            await prisma.userSession.upsert({
                where: { session_token: sessionId },
                update: {
                    last_activity: now,
                    expires_at: expiresAt,
                    user_agent: userAgent,
                    device_fingerprint: deviceFingerprint,
                    location: location,
                    security_flags: securityFlags || {},
                    max_idle_time: maxIdleTime,
                    absolute_timeout: absoluteTimeout
                },
                create: {
                    session_token: sessionId,
                    user_id: userId,
                    ip_address: ipAddress,
                    user_agent: userAgent,
                    device_fingerprint: deviceFingerprint,
                    location: location,
                    is_active: true,
                    last_activity: now,
                    expires_at: expiresAt,
                    max_idle_time: maxIdleTime,
                    absolute_timeout: absoluteTimeout,
                    security_flags: securityFlags || {
                        created_at: now.toISOString(),
                        creation_ip: ipAddress,
                        creation_user_agent: userAgent
                    }
                }
            });

            Logger.info('Session stored successfully', {
                sessionId: sessionId.substring(0, 8) + '...',
                userId,
                expiresAt
            });

        } catch (error) {
            Logger.error('Failed to set session in database', error, {
                sessionId: sessionId.substring(0, 8) + '...',
                userId: sessionData?.userId
            });
            throw error;
        }
    }

    /**
     * Destroy/remove session
     */
    destroy(sessionId: string, callback?: (err?: any) => void): void {
        this.destroyAsync(sessionId)
            .then(() => callback?.())
            .catch(err => {
                Logger.error('Session destroy error', err, { sessionId: sessionId.substring(0, 8) + '...' });
                callback?.(err);
            });
    }

    /**
     * Async version of destroy method
     */
    async destroyAsync(sessionId: string): Promise<void> {
        try {
            const result = await prisma.userSession.updateMany({
                where: { session_token: sessionId },
                data: {
                    is_active: false,
                    security_flags: {
                        invalidated_at: new Date().toISOString(),
                        invalidation_reason: 'session_destroyed'
                    }
                }
            });

            Logger.info('Session destroyed', {
                sessionId: sessionId.substring(0, 8) + '...',
                affectedRows: result.count
            });

        } catch (error) {
            Logger.error('Failed to destroy session in database', error, {
                sessionId: sessionId.substring(0, 8) + '...'
            });
            throw error;
        }
    }

    /**
     * Touch session to update last activity (optional, controlled by disableTouch)
     */
    touch(sessionId: string, sessionData: any, callback?: (err?: any) => void): void {
        if (this.options.disableTouch) {
            callback?.();
            return;
        }

        this.touchAsync(sessionId, sessionData)
            .then(() => callback?.())
            .catch(err => {
                Logger.error('Session touch error', err, { sessionId: sessionId.substring(0, 8) + '...' });
                callback?.(err);
            });
    }

    /**
     * Async version of touch method
     */
    async touchAsync(sessionId: string, _sessionData: any): Promise<void> {
        if (this.options.disableTouch) {
            return;
        }

        try {
            const now = new Date();
            const expiresAt = new Date(now.getTime() + (this.options.ttl * 1000));

            await prisma.userSession.updateMany({
                where: {
                    session_token: sessionId,
                    is_active: true
                },
                data: {
                    last_activity: now,
                    expires_at: expiresAt
                }
            });

        } catch (error) {
            Logger.error('Failed to touch session in database', error, {
                sessionId: sessionId.substring(0, 8) + '...'
            });
            throw error;
        }
    }

    /**
     * Get all session IDs (optional method for session management)
     */
    all(callback: (err?: any, sessions?: { [sessionId: string]: SessionStoreData } | null) => void): void {
        this.allAsync()
            .then(sessions => callback(null, sessions))
            .catch(err => {
                Logger.error('Session all error', err);
                callback(err);
            });
    }

    /**
     * Async version of all method
     */
    async allAsync(): Promise<{ [sessionId: string]: SessionStoreData } | null> {
        try {
            const sessions = await prisma.userSession.findMany({
                where: { is_active: true },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            first_name: true,
                            last_name: true,
                            role: true,
                            is_active: true,
                            requires_password_change: true
                        }
                    }
                }
            });

            const result: { [sessionId: string]: SessionStoreData } = {};

            for (const session of sessions) {
                // Skip expired sessions
                const now = new Date();
                if (session.expires_at < now) {
                    continue;
                }

                // Skip idle timeout sessions
                const idleTimeMs = now.getTime() - session.last_activity.getTime();
                const maxIdleTimeMs = session.max_idle_time * 1000;
                if (idleTimeMs > maxIdleTimeMs) {
                    continue;
                }

                // Skip inactive users
                if (!session.user.is_active) {
                    continue;
                }

                result[session.session_token] = {
                    userId: session.user.id,
                    email: session.user.email,
                    role: session.user.role,
                    firstName: session.user.first_name,
                    lastName: session.user.last_name,
                    isActive: session.user.is_active,
                    requiresPasswordChange: session.user.requires_password_change,
                    ipAddress: session.ip_address,
                    userAgent: session.user_agent || undefined,
                    deviceFingerprint: session.device_fingerprint || undefined,
                    location: session.location || undefined,
                    lastActivity: session.last_activity,
                    createdAt: session.created_at,
                    securityFlags: session.security_flags as Record<string, any> || undefined
                };
            }

            return result;

        } catch (error) {
            Logger.error('Failed to get all sessions from database', error);
            throw error;
        }
    }

    /**
     * Get session count (optional method)
     */
    length(callback: (err?: any, length?: number) => void): void {
        this.lengthAsync()
            .then(length => callback(null, length))
            .catch(err => {
                Logger.error('Session length error', err);
                callback(err);
            });
    }

    /**
     * Async version of length method
     */
    async lengthAsync(): Promise<number> {
        try {
            const count = await prisma.userSession.count({
                where: { is_active: true }
            });

            return count;

        } catch (error) {
            Logger.error('Failed to get session count from database', error);
            throw error;
        }
    }

    /**
     * Clear all sessions (optional method)
     */
    clear(callback?: (err?: any) => void): void {
        this.clearAsync()
            .then(() => callback?.())
            .catch(err => {
                Logger.error('Session clear error', err);
                callback?.(err);
            });
    }

    /**
     * Async version of clear method
     */
    async clearAsync(): Promise<void> {
        try {
            const result = await prisma.userSession.updateMany({
                where: { is_active: true },
                data: {
                    is_active: false,
                    security_flags: {
                        invalidated_at: new Date().toISOString(),
                        invalidation_reason: 'store_cleared'
                    }
                }
            });

            Logger.info('All sessions cleared', { affectedRows: result.count });

        } catch (error) {
            Logger.error('Failed to clear all sessions from database', error);
            throw error;
        }
    }

    /**
     * Cleanup expired sessions (maintenance method)
     */
    async cleanupExpiredSessions(): Promise<{
        expiredSessions: number;
        idleTimeoutSessions: number;
        totalCleaned: number;
    }> {
        try {
            const now = new Date();
            let expiredCount = 0;
            let idleTimeoutCount = 0;

            // Clean up sessions that exceeded absolute timeout
            const absoluteTimeoutResult = await prisma.userSession.updateMany({
                where: {
                    expires_at: { lte: now },
                    is_active: true
                },
                data: {
                    is_active: false,
                    security_flags: {
                        invalidated_at: now.toISOString(),
                        invalidation_reason: 'absolute_timeout_expired'
                    }
                }
            });
            expiredCount = absoluteTimeoutResult.count;

            // Clean up sessions that exceeded idle timeout
            // We need to find sessions where last_activity + max_idle_time < now
            const idleTimeoutSessions = await prisma.userSession.findMany({
                where: {
                    is_active: true,
                    expires_at: { gt: now } // Not already expired by absolute timeout
                },
                select: {
                    id: true,
                    last_activity: true,
                    max_idle_time: true
                }
            });

            const idleExpiredIds: string[] = [];
            for (const session of idleTimeoutSessions) {
                const idleTimeMs = now.getTime() - session.last_activity.getTime();
                const maxIdleTimeMs = session.max_idle_time * 1000;

                if (idleTimeMs > maxIdleTimeMs) {
                    idleExpiredIds.push(session.id);
                }
            }

            if (idleExpiredIds.length > 0) {
                const idleTimeoutResult = await prisma.userSession.updateMany({
                    where: {
                        id: { in: idleExpiredIds }
                    },
                    data: {
                        is_active: false,
                        security_flags: {
                            invalidated_at: now.toISOString(),
                            invalidation_reason: 'idle_timeout_expired'
                        }
                    }
                });
                idleTimeoutCount = idleTimeoutResult.count;
            }

            const totalCleaned = expiredCount + idleTimeoutCount;

            Logger.info('Session cleanup completed', {
                expiredSessions: expiredCount,
                idleTimeoutSessions: idleTimeoutCount,
                totalCleaned
            });

            return {
                expiredSessions: expiredCount,
                idleTimeoutSessions: idleTimeoutCount,
                totalCleaned
            };

        } catch (error) {
            Logger.error('Failed to cleanup expired sessions', error);
            throw error;
        }
    }

    /**
     * Get sessions for a specific user
     */
    async getUserSessions(userId: string): Promise<SessionStoreData[]> {
        try {
            const sessions = await prisma.userSession.findMany({
                where: {
                    user_id: userId,
                    is_active: true
                },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            first_name: true,
                            last_name: true,
                            role: true,
                            is_active: true,
                            requires_password_change: true
                        }
                    }
                },
                orderBy: { last_activity: 'desc' }
            });

            return sessions
                .filter(session => {
                    // Filter out expired sessions
                    const now = new Date();
                    if (session.expires_at < now) return false;

                    // Filter out idle timeout sessions
                    const idleTimeMs = now.getTime() - session.last_activity.getTime();
                    const maxIdleTimeMs = session.max_idle_time * 1000;
                    if (idleTimeMs > maxIdleTimeMs) return false;

                    return session.user.is_active;
                })
                .map(session => ({
                    userId: session.user.id,
                    email: session.user.email,
                    role: session.user.role,
                    firstName: session.user.first_name,
                    lastName: session.user.last_name,
                    isActive: session.user.is_active,
                    requiresPasswordChange: session.user.requires_password_change,
                    ipAddress: session.ip_address,
                    userAgent: session.user_agent || undefined,
                    deviceFingerprint: session.device_fingerprint || undefined,
                    location: session.location || undefined,
                    lastActivity: session.last_activity,
                    createdAt: session.created_at,
                    securityFlags: session.security_flags as Record<string, any> || undefined
                }));

        } catch (error) {
            Logger.error('Failed to get user sessions', error, { userId });
            throw error;
        }
    }

    /**
     * Invalidate all sessions for a user except optionally one
     */
    async invalidateUserSessions(userId: string, excludeSessionId?: string): Promise<number> {
        try {
            const whereClause: any = {
                user_id: userId,
                is_active: true
            };

            if (excludeSessionId) {
                whereClause.session_token = {
                    not: excludeSessionId
                };
            }

            const result = await prisma.userSession.updateMany({
                where: whereClause,
                data: {
                    is_active: false,
                    security_flags: {
                        invalidated_at: new Date().toISOString(),
                        invalidation_reason: 'user_session_invalidation'
                    }
                }
            });

            Logger.info('User sessions invalidated', {
                userId,
                excludeSessionId: excludeSessionId?.substring(0, 8) + '...',
                invalidatedCount: result.count
            });

            return result.count;

        } catch (error) {
            Logger.error('Failed to invalidate user sessions', error, { userId });
            throw error;
        }
    }
}