import { passwordManager, PasswordResetRequest } from './password-manager';
import { rateLimitHelpers } from './rate-limiter';
import { SecurityLogger, SecurityEventType } from './security-logger';
import prisma from './prisma';

// Password reset workflow interfaces
export interface PasswordResetInitiateRequest {
    email: string;
    ipAddress: string;
    userAgent?: string;
}

export interface PasswordResetInitiateResponse {
    success: boolean;
    message: string;
    rateLimited?: boolean;
    retryAfter?: number;
}

export interface PasswordResetCompleteRequest {
    token: string;
    newPassword: string;
    ipAddress: string;
    userAgent?: string;
}

export interface PasswordResetCompleteResponse {
    success: boolean;
    message: string;
    error?: string;
    userId?: string;
}

export interface PasswordResetValidateRequest {
    token: string;
    ipAddress: string;
    userAgent?: string;
}

export interface PasswordResetValidateResponse {
    valid: boolean;
    message: string;
    error?: string;
    expiresAt?: Date;
}

export class PasswordResetWorkflow {
    /**
     * Initiate password reset process with rate limiting and security controls
     */
    async initiatePasswordReset(request: PasswordResetInitiateRequest): Promise<PasswordResetInitiateResponse> {
        const { email, ipAddress, userAgent } = request;

        try {
            // Check rate limiting first
            const rateLimitResult = await rateLimitHelpers.checkPasswordResetLimit(ipAddress);

            if (!rateLimitResult.allowed) {
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
                    email,
                    ipAddress,
                    userAgent,
                    success: false,
                    details: {
                        violationType: 'password_reset_rate_limit',
                        endpoint: 'password_reset_initiate',
                        requestCount: rateLimitResult.remaining,
                        timeWindow: '1 hour'
                    }
                });

                return {
                    success: false,
                    message: 'Too many password reset requests. Please try again later.',
                    rateLimited: true,
                    retryAfter: rateLimitResult.retryAfter
                };
            }

            // Record the request for rate limiting
            await rateLimitHelpers.recordAuthRequest(ipAddress);

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_REQUEST,
                    email,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Invalid email format provided',
                    details: {
                        reason: 'invalid_email_format'
                    }
                });

                return {
                    success: false,
                    message: 'Please provide a valid email address.'
                };
            }

            // Generate password reset token
            const resetRequest: PasswordResetRequest = {
                email,
                ipAddress,
                userAgent
            };

            const tokenResult = await passwordManager.generatePasswordResetToken(resetRequest);

            if (!tokenResult.success) {
                return {
                    success: false,
                    message: tokenResult.error || 'Unable to process password reset request.'
                };
            }

            // Always return success to prevent user enumeration
            // The actual token is only generated for valid users
            return {
                success: true,
                message: 'If an account with that email exists, you will receive password reset instructions.'
            };

        } catch (error) {
            console.error('Password reset initiation error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_REQUEST,
                email,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during password reset initiation',
                details: {
                    errorType: 'system_error',
                    reason: 'password_reset_initiation_failed'
                }
            });

            return {
                success: false,
                message: 'Unable to process password reset request. Please try again later.'
            };
        }
    }

    /**
     * Validate password reset token
     */
    async validatePasswordResetToken(request: PasswordResetValidateRequest): Promise<PasswordResetValidateResponse> {
        const { token, ipAddress, userAgent } = request;

        try {
            // Basic token format validation
            if (!token || token.length < 32) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Invalid token format provided',
                    details: {
                        reason: 'invalid_token_format',
                        tokenLength: token?.length || 0
                    }
                });

                return {
                    valid: false,
                    message: 'Invalid reset token format.'
                };
            }

            // Validate token with password manager
            const validation = await passwordManager.validatePasswordResetToken(token);

            if (!validation.isValid) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
                    userId: validation.userId,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: validation.error || 'Token validation failed',
                    details: {
                        reason: 'token_validation_failed',
                        token: token.substring(0, 8) + '...' // Log partial token for debugging
                    }
                });

                return {
                    valid: false,
                    message: validation.error || 'Invalid or expired reset token.',
                    error: validation.error
                };
            }

            // Log successful token validation
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_REQUEST,
                userId: validation.userId,
                ipAddress,
                userAgent,
                success: true,
                details: {
                    reason: 'token_validation_successful',
                    expiresAt: validation.token?.expiresAt?.toISOString(),
                    token: token.substring(0, 8) + '...'
                }
            });

            return {
                valid: true,
                message: 'Reset token is valid.',
                expiresAt: validation.token?.expiresAt
            };

        } catch (error) {
            console.error('Password reset token validation error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during token validation',
                details: {
                    errorType: 'system_error',
                    reason: 'token_validation_system_error'
                }
            });

            return {
                valid: false,
                message: 'Unable to validate reset token. Please try again later.'
            };
        }
    }

    /**
     * Complete password reset process
     */
    async completePasswordReset(request: PasswordResetCompleteRequest): Promise<PasswordResetCompleteResponse> {
        const { token, newPassword, ipAddress, userAgent } = request;

        try {
            // Check rate limiting for password reset completion
            const rateLimitResult = await rateLimitHelpers.checkPasswordResetLimit(ipAddress);

            if (!rateLimitResult.allowed) {
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
                    ipAddress,
                    userAgent,
                    success: false,
                    details: {
                        violationType: 'password_reset_completion_rate_limit',
                        endpoint: 'password_reset_complete',
                        requestCount: rateLimitResult.remaining,
                        timeWindow: '1 hour'
                    }
                });

                return {
                    success: false,
                    message: 'Too many password reset attempts. Please try again later.',
                    error: 'rate_limit_exceeded'
                };
            }

            // Record the request for rate limiting
            await rateLimitHelpers.recordAuthRequest(ipAddress);

            // Basic input validation
            if (!token || token.length < 32) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_SUCCESS,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Invalid token format provided for password reset',
                    details: {
                        reason: 'invalid_token_format',
                        tokenLength: token?.length || 0
                    }
                });

                return {
                    success: false,
                    message: 'Invalid reset token.',
                    error: 'invalid_token'
                };
            }

            if (!newPassword || newPassword.length < 12) {
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.PASSWORD_RESET_SUCCESS,
                    ipAddress,
                    userAgent,
                    success: false,
                    errorMessage: 'Password too short for reset',
                    details: {
                        reason: 'password_too_short',
                        passwordLength: newPassword?.length || 0
                    }
                });

                return {
                    success: false,
                    message: 'Password must be at least 12 characters long.',
                    error: 'password_too_short'
                };
            }

            // Complete password reset using password manager
            const resetResult = await passwordManager.resetPasswordWithToken(
                token,
                newPassword,
                ipAddress,
                userAgent
            );

            if (!resetResult.success) {
                return {
                    success: false,
                    message: resetResult.error || 'Unable to reset password.',
                    error: resetResult.error
                };
            }

            return {
                success: true,
                message: 'Password has been successfully reset. You can now log in with your new password.',
                userId: resetResult.userId
            };

        } catch (error) {
            console.error('Password reset completion error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.PASSWORD_RESET_SUCCESS,
                ipAddress,
                userAgent,
                success: false,
                errorMessage: 'System error during password reset completion',
                details: {
                    errorType: 'system_error',
                    reason: 'password_reset_completion_failed'
                }
            });

            return {
                success: false,
                message: 'Unable to complete password reset. Please try again later.',
                error: 'system_error'
            };
        }
    }

    /**
     * Get password reset statistics for monitoring
     */
    async getPasswordResetStats(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<{
        totalRequests: number;
        successfulResets: number;
        failedAttempts: number;
        rateLimitedRequests: number;
        expiredTokens: number;
        averageTimeToReset: number; // in minutes
    }> {
        try {
            const now = new Date();
            let startTime: Date;

            switch (timeframe) {
                case 'hour':
                    startTime = new Date(now.getTime() - (60 * 60 * 1000));
                    break;
                case 'week':
                    startTime = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
                    break;
                case 'day':
                default:
                    startTime = new Date(now.getTime() - (24 * 60 * 60 * 1000));
                    break;
            }

            // Get statistics from security audit log
            const [
                totalRequests,
                successfulResets,
                failedAttempts,
                rateLimitedRequests
            ] = await Promise.all([
                // Total password reset requests
                prisma.securityAuditLog.count({
                    where: {
                        event_type: 'PASSWORD_RESET_REQUEST',
                        created_at: { gte: startTime }
                    }
                }),

                // Successful password resets
                prisma.securityAuditLog.count({
                    where: {
                        event_type: 'PASSWORD_RESET_SUCCESS',
                        success: true,
                        created_at: { gte: startTime }
                    }
                }),

                // Failed reset attempts
                prisma.securityAuditLog.count({
                    where: {
                        event_type: 'PASSWORD_RESET_SUCCESS',
                        success: false,
                        created_at: { gte: startTime }
                    }
                }),

                // Rate limited requests
                prisma.securityAuditLog.count({
                    where: {
                        event_type: 'RATE_LIMIT_EXCEEDED',
                        details: {
                            path: ['violationType'],
                            equals: 'password_reset_rate_limit'
                        },
                        created_at: { gte: startTime }
                    }
                })
            ]);

            // Get expired tokens count
            const expiredTokens = await passwordManager.cleanupExpiredResetTokens();

            // Calculate average time to reset (simplified - would need more complex query for accurate calculation)
            const averageTimeToReset = 30; // Placeholder - would need to track token creation to completion time

            return {
                totalRequests,
                successfulResets,
                failedAttempts,
                rateLimitedRequests,
                expiredTokens,
                averageTimeToReset
            };

        } catch (error) {
            console.error('Error getting password reset statistics:', error);
            return {
                totalRequests: 0,
                successfulResets: 0,
                failedAttempts: 0,
                rateLimitedRequests: 0,
                expiredTokens: 0,
                averageTimeToReset: 0
            };
        }
    }

    /**
     * Clean up expired tokens and old audit logs (maintenance function)
     */
    async performMaintenance(): Promise<{
        expiredTokensCleanedUp: number;
        oldAuditLogsCleanedUp: number;
    }> {
        try {
            // Clean up expired password reset tokens
            const expiredTokensCleanedUp = await passwordManager.cleanupExpiredResetTokens();

            // Clean up old audit logs (older than 90 days)
            const ninetyDaysAgo = new Date(Date.now() - (90 * 24 * 60 * 60 * 1000));
            const oldAuditLogsResult = await prisma.securityAuditLog.deleteMany({
                where: {
                    event_type: {
                        in: ['PASSWORD_RESET_REQUEST', 'PASSWORD_RESET_SUCCESS']
                    },
                    created_at: { lt: ninetyDaysAgo }
                }
            });

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SYSTEM_SETTINGS_CHANGED,
                ipAddress: 'system',
                userAgent: 'maintenance_job',
                success: true,
                details: {
                    component: 'password_reset_workflow',
                    expiredTokensCleanedUp,
                    oldAuditLogsCleanedUp: oldAuditLogsResult.count,
                    maintenanceType: 'scheduled_cleanup'
                }
            });

            return {
                expiredTokensCleanedUp,
                oldAuditLogsCleanedUp: oldAuditLogsResult.count
            };

        } catch (error) {
            console.error('Password reset maintenance error:', error);

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress: 'system',
                userAgent: 'maintenance_job',
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown maintenance error',
                details: {
                    component: 'password_reset_workflow',
                    maintenanceType: 'scheduled_cleanup',
                    errorType: 'system_error'
                }
            });

            return {
                expiredTokensCleanedUp: 0,
                oldAuditLogsCleanedUp: 0
            };
        }
    }

    /**
     * Security monitoring for password reset workflow
     */
    async detectSuspiciousActivity(timeframe: 'hour' | 'day' = 'hour'): Promise<{
        suspiciousIPs: Array<{
            ipAddress: string;
            requestCount: number;
            successRate: number;
        }>;
        unusualPatterns: Array<{
            pattern: string;
            description: string;
            severity: 'low' | 'medium' | 'high';
            count: number;
        }>;
        recommendations: string[];
    }> {
        try {
            const now = new Date();
            const startTime = timeframe === 'hour'
                ? new Date(now.getTime() - (60 * 60 * 1000))
                : new Date(now.getTime() - (24 * 60 * 60 * 1000));

            // Get password reset activity by IP
            const ipActivity = await prisma.securityAuditLog.groupBy({
                by: ['ip_address'],
                where: {
                    event_type: {
                        in: ['PASSWORD_RESET_REQUEST', 'PASSWORD_RESET_SUCCESS']
                    },
                    created_at: { gte: startTime }
                },
                _count: {
                    id: true
                }
            });

            // Analyze suspicious IPs (more than 5 requests in timeframe)
            const suspiciousIPs = [];
            for (const ip of ipActivity) {
                if (ip._count.id > 5) {
                    // Calculate success rate
                    const [totalRequests, successfulRequests] = await Promise.all([
                        prisma.securityAuditLog.count({
                            where: {
                                ip_address: ip.ip_address,
                                event_type: {
                                    in: ['PASSWORD_RESET_REQUEST', 'PASSWORD_RESET_SUCCESS']
                                },
                                created_at: { gte: startTime }
                            }
                        }),
                        prisma.securityAuditLog.count({
                            where: {
                                ip_address: ip.ip_address,
                                event_type: 'PASSWORD_RESET_SUCCESS',
                                success: true,
                                created_at: { gte: startTime }
                            }
                        })
                    ]);

                    const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;

                    suspiciousIPs.push({
                        ipAddress: ip.ip_address,
                        requestCount: ip._count.id,
                        successRate
                    });
                }
            }

            // Detect unusual patterns
            const unusualPatterns = [];
            const recommendations = [];

            // Pattern 1: High volume of requests
            const totalRequests = await prisma.securityAuditLog.count({
                where: {
                    event_type: 'PASSWORD_RESET_REQUEST',
                    created_at: { gte: startTime }
                }
            });

            if (totalRequests > (timeframe === 'hour' ? 50 : 200)) {
                unusualPatterns.push({
                    pattern: 'high_volume_requests',
                    description: `Unusually high number of password reset requests (${totalRequests})`,
                    severity: 'high' as const,
                    count: totalRequests
                });
                recommendations.push('Consider implementing additional rate limiting or CAPTCHA verification');
            }

            // Pattern 2: High failure rate
            const failedRequests = await prisma.securityAuditLog.count({
                where: {
                    event_type: 'PASSWORD_RESET_SUCCESS',
                    success: false,
                    created_at: { gte: startTime }
                }
            });

            const failureRate = totalRequests > 0 ? (failedRequests / totalRequests) * 100 : 0;
            if (failureRate > 70) {
                unusualPatterns.push({
                    pattern: 'high_failure_rate',
                    description: `High password reset failure rate (${failureRate.toFixed(1)}%)`,
                    severity: 'medium' as const,
                    count: failedRequests
                });
                recommendations.push('Investigate common failure reasons and improve user guidance');
            }

            // Pattern 3: Rate limit violations
            const rateLimitViolations = await prisma.securityAuditLog.count({
                where: {
                    event_type: 'RATE_LIMIT_EXCEEDED',
                    details: {
                        path: ['violationType'],
                        string_contains: 'password_reset'
                    },
                    created_at: { gte: startTime }
                }
            });

            if (rateLimitViolations > (timeframe === 'hour' ? 10 : 50)) {
                unusualPatterns.push({
                    pattern: 'excessive_rate_limiting',
                    description: `High number of rate limit violations (${rateLimitViolations})`,
                    severity: 'medium' as const,
                    count: rateLimitViolations
                });
                recommendations.push('Review rate limiting thresholds and consider IP-based blocking');
            }

            // Add general recommendations
            if (suspiciousIPs.length > 0) {
                recommendations.push('Monitor and potentially block suspicious IP addresses');
            }

            if (unusualPatterns.length === 0) {
                recommendations.push('Password reset activity appears normal');
            }

            return {
                suspiciousIPs,
                unusualPatterns,
                recommendations
            };

        } catch (error) {
            console.error('Error detecting suspicious password reset activity:', error);
            return {
                suspiciousIPs: [],
                unusualPatterns: [{
                    pattern: 'monitoring_error',
                    description: 'Unable to analyze password reset activity due to system error',
                    severity: 'high' as const,
                    count: 0
                }],
                recommendations: ['Check system logs and resolve monitoring issues']
            };
        }
    }
}

// Export singleton instance
export const passwordResetWorkflow = new PasswordResetWorkflow();

// Export utility functions for backward compatibility
export async function initiatePasswordReset(request: PasswordResetInitiateRequest) {
    return passwordResetWorkflow.initiatePasswordReset(request);
}

export async function validatePasswordResetToken(request: PasswordResetValidateRequest) {
    return passwordResetWorkflow.validatePasswordResetToken(request);
}

export async function completePasswordReset(request: PasswordResetCompleteRequest) {
    return passwordResetWorkflow.completePasswordReset(request);
}

export async function getPasswordResetStats(timeframe: 'hour' | 'day' | 'week' = 'day') {
    return passwordResetWorkflow.getPasswordResetStats(timeframe);
}

export async function performPasswordResetMaintenance() {
    return passwordResetWorkflow.performMaintenance();
}

export async function detectSuspiciousPasswordResetActivity(timeframe: 'hour' | 'day' = 'hour') {
    return passwordResetWorkflow.detectSuspiciousActivity(timeframe);
}