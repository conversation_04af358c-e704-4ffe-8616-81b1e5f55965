import { Request, Response, NextFunction } from 'express';
import { prisma } from './prisma';

// Performance metrics interface
interface PerformanceMetrics {
    endpoint: string;
    method: string;
    responseTime: number;
    statusCode: number;
    timestamp: Date;
    userId?: string;
    userAgent?: string;
    ipAddress?: string;
    memoryUsage?: NodeJS.MemoryUsage;
    cpuUsage?: NodeJS.CpuUsage;
}

// Performance statistics interface
interface PerformanceStats {
    endpoint: string;
    totalRequests: number;
    averageResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
    errorRate: number;
    throughput: number; // requests per second
    lastUpdated: Date;
}

// Health check status interface
interface HealthCheckStatus {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: Date;
    checks: {
        database: {
            status: 'healthy' | 'unhealthy';
            responseTime: number;
            error?: string;
        };
        memory: {
            status: 'healthy' | 'warning' | 'critical';
            usage: NodeJS.MemoryUsage;
            percentage: number;
        };
        externalServices: {
            freemius: {
                status: 'healthy' | 'unhealthy';
                responseTime?: number;
                error?: string;
            };
            ipRegistry: {
                status: 'healthy' | 'unhealthy';
                responseTime?: number;
                error?: string;
            };
        };
    };
    uptime: number;
    version: string;
}

class PerformanceMonitor {
    private metrics: Map<string, PerformanceMetrics[]> = new Map();
    private readonly maxMetricsPerEndpoint = 1000;
    private readonly metricsRetentionMs = 24 * 60 * 60 * 1000; // 24 hours
    private startTime = Date.now();

    /**
     * Express middleware for performance monitoring
     */
    middleware() {
        return (req: Request, res: Response, next: NextFunction) => {
            const startTime = process.hrtime.bigint();
            const startCpuUsage = process.cpuUsage();

            // Override res.end to capture response time
            const originalEnd = res.end;
            res.end = function (chunk?: any, encoding?: any) {
                const endTime = process.hrtime.bigint();
                const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds

                // Capture performance metrics
                const metrics: PerformanceMetrics = {
                    endpoint: req.route?.path || req.path,
                    method: req.method,
                    responseTime,
                    statusCode: res.statusCode,
                    timestamp: new Date(),
                    userId: (req as any).user?.id,
                    userAgent: req.headers['user-agent'],
                    ipAddress: req.ip || req.connection.remoteAddress,
                    memoryUsage: process.memoryUsage(),
                    cpuUsage: process.cpuUsage(startCpuUsage),
                };

                // Store metrics
                performanceMonitor.recordMetrics(metrics);

                // Call original end method
                return originalEnd.call(this, chunk, encoding);
            };

            next();
        };
    }

    /**
     * Record performance metrics
     */
    recordMetrics(metrics: PerformanceMetrics): void {
        const key = `${metrics.method}:${metrics.endpoint}`;

        if (!this.metrics.has(key)) {
            this.metrics.set(key, []);
        }

        const endpointMetrics = this.metrics.get(key)!;
        endpointMetrics.push(metrics);

        // Limit metrics per endpoint and clean old ones
        if (endpointMetrics.length > this.maxMetricsPerEndpoint) {
            endpointMetrics.splice(0, endpointMetrics.length - this.maxMetricsPerEndpoint);
        }

        // Clean old metrics
        this.cleanOldMetrics();
    }

    /**
     * Get performance statistics for an endpoint
     */
    getEndpointStats(method: string, endpoint: string): PerformanceStats | null {
        const key = `${method}:${endpoint}`;
        const metrics = this.metrics.get(key);

        if (!metrics || metrics.length === 0) {
            return null;
        }

        const responseTimes = metrics.map(m => m.responseTime).sort((a, b) => a - b);
        const errorCount = metrics.filter(m => m.statusCode >= 400).length;
        const timeSpan = (Date.now() - metrics[0].timestamp.getTime()) / 1000; // seconds

        return {
            endpoint,
            totalRequests: metrics.length,
            averageResponseTime: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
            minResponseTime: responseTimes[0],
            maxResponseTime: responseTimes[responseTimes.length - 1],
            p95ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.95)],
            p99ResponseTime: responseTimes[Math.floor(responseTimes.length * 0.99)],
            errorRate: (errorCount / metrics.length) * 100,
            throughput: metrics.length / Math.max(timeSpan, 1),
            lastUpdated: new Date(),
        };
    }

    /**
     * Get all performance statistics
     */
    getAllStats(): PerformanceStats[] {
        const stats: PerformanceStats[] = [];

        for (const [key] of this.metrics) {
            const [method, endpoint] = key.split(':', 2);
            const endpointStats = this.getEndpointStats(method, endpoint);
            if (endpointStats) {
                stats.push(endpointStats);
            }
        }

        return stats.sort((a, b) => b.totalRequests - a.totalRequests);
    }

    /**
     * Get system health check
     */
    async getHealthCheck(): Promise<HealthCheckStatus> {
        const checks: HealthCheckStatus['checks'] = {
            database: await this.checkDatabase(),
            memory: this.checkMemory(),
            externalServices: {
                freemius: await this.checkFreemiusService(),
                ipRegistry: await this.checkIpRegistryService(),
            },
        };

        // Determine overall status
        let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

        if (checks.database.status === 'unhealthy' ||
            checks.externalServices.freemius.status === 'unhealthy' ||
            checks.externalServices.ipRegistry.status === 'unhealthy') {
            status = 'unhealthy';
        } else if (checks.memory.status === 'critical' || checks.memory.status === 'warning') {
            status = 'degraded';
        }

        return {
            status,
            timestamp: new Date(),
            checks,
            uptime: (Date.now() - this.startTime) / 1000,
            version: process.env.npm_package_version || '1.0.0',
        };
    }

    /**
     * Check database connectivity and performance
     */
    private async checkDatabase(): Promise<HealthCheckStatus['checks']['database']> {
        try {
            const startTime = Date.now();
            await prisma.$queryRaw`SELECT 1`;
            const responseTime = Date.now() - startTime;

            return {
                status: responseTime < 1000 ? 'healthy' : 'unhealthy',
                responseTime,
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                responseTime: 0,
                error: error instanceof Error ? error.message : 'Unknown database error',
            };
        }
    }

    /**
     * Check memory usage
     */
    private checkMemory(): HealthCheckStatus['checks']['memory'] {
        const usage = process.memoryUsage();
        const totalMemory = usage.heapTotal + usage.external;
        const usedMemory = usage.heapUsed;
        const percentage = (usedMemory / totalMemory) * 100;

        let status: 'healthy' | 'warning' | 'critical' = 'healthy';
        if (percentage > 90) {
            status = 'critical';
        } else if (percentage > 75) {
            status = 'warning';
        }

        return {
            status,
            usage,
            percentage,
        };
    }

    /**
     * Check Freemius service health
     */
    private async checkFreemiusService(): Promise<HealthCheckStatus['checks']['externalServices']['freemius']> {
        try {
            // Simple connectivity check - try to get product info
            const startTime = Date.now();

            // Import dynamically to avoid circular dependencies
            const { FreemiusService } = await import('../services/freemius-service');
            const service = new FreemiusService();

            // This is a lightweight check - just verify we can connect
            await service.getProduct(['id', 'title']);

            const responseTime = Date.now() - startTime;

            return {
                status: responseTime < 5000 ? 'healthy' : 'unhealthy',
                responseTime,
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                responseTime: 0,
                error: error instanceof Error ? error.message : 'Freemius service error',
            };
        }
    }

    /**
     * Check IP Registry service health
     */
    private async checkIpRegistryService(): Promise<HealthCheckStatus['checks']['externalServices']['ipRegistry']> {
        try {
            // Simple connectivity check - try to analyze a known IP
            const startTime = Date.now();

            // Import dynamically to avoid circular dependencies
            const { ipIntelligenceService } = await import('../services/ip-intelligence-service');

            // Use a known safe IP for health check (Google DNS)
            await ipIntelligenceService.analyzeIp('*******', {
                requestSource: 'admin',
            });

            const responseTime = Date.now() - startTime;

            return {
                status: responseTime < 3000 ? 'healthy' : 'unhealthy',
                responseTime,
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                responseTime: 0,
                error: error instanceof Error ? error.message : 'IP Registry service error',
            };
        }
    }

    /**
     * Clean old metrics to prevent memory leaks
     */
    private cleanOldMetrics(): void {
        const cutoffTime = Date.now() - this.metricsRetentionMs;

        for (const [key, metrics] of this.metrics) {
            const filteredMetrics = metrics.filter(m => m.timestamp.getTime() > cutoffTime);

            if (filteredMetrics.length === 0) {
                this.metrics.delete(key);
            } else {
                this.metrics.set(key, filteredMetrics);
            }
        }
    }

    /**
     * Get slow queries from recent metrics
     */
    getSlowQueries(thresholdMs: number = 1000): PerformanceMetrics[] {
        const slowQueries: PerformanceMetrics[] = [];

        for (const metrics of this.metrics.values()) {
            const slow = metrics.filter(m => m.responseTime > thresholdMs);
            slowQueries.push(...slow);
        }

        return slowQueries
            .sort((a, b) => b.responseTime - a.responseTime)
            .slice(0, 50); // Top 50 slowest queries
    }

    /**
     * Get error rate statistics
     */
    getErrorStats(timeRangeMs: number = 60 * 60 * 1000): {
        totalRequests: number;
        errorCount: number;
        errorRate: number;
        errorsByEndpoint: Array<{ endpoint: string; errors: number; total: number; rate: number }>;
    } {
        const cutoffTime = Date.now() - timeRangeMs;
        let totalRequests = 0;
        let errorCount = 0;
        const endpointErrors: Map<string, { errors: number; total: number }> = new Map();

        for (const [key, metrics] of this.metrics) {
            const recentMetrics = metrics.filter(m => m.timestamp.getTime() > cutoffTime);
            const errors = recentMetrics.filter(m => m.statusCode >= 400).length;

            totalRequests += recentMetrics.length;
            errorCount += errors;

            if (recentMetrics.length > 0) {
                endpointErrors.set(key, {
                    errors,
                    total: recentMetrics.length,
                });
            }
        }

        const errorsByEndpoint = Array.from(endpointErrors.entries())
            .map(([endpoint, stats]) => ({
                endpoint,
                errors: stats.errors,
                total: stats.total,
                rate: (stats.errors / stats.total) * 100,
            }))
            .sort((a, b) => b.rate - a.rate);

        return {
            totalRequests,
            errorCount,
            errorRate: totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0,
            errorsByEndpoint,
        };
    }

    /**
     * Reset all metrics (useful for testing)
     */
    reset(): void {
        this.metrics.clear();
        this.startTime = Date.now();
    }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();

// Export types
export type { PerformanceMetrics, PerformanceStats, HealthCheckStatus };