import { prisma } from './prisma';

// Security event types for categorization
export enum SecurityEventType {
    // Authentication events
    LOGIN_SUCCESS = 'LOGIN_SUCCESS',
    LOGIN_FAILURE = 'LOGIN_FAILURE',
    LOGOUT = 'LOGOUT',
    PASSWORD_CHANGE = 'PASSWORD_CHANGE',
    PASSWORD_RESET_REQUEST = 'PASSWORD_RESET_REQUEST',
    PASSWORD_RESET_SUCCESS = 'PASSWORD_RESET_SUCCESS',
    ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
    ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',

    // Session events
    SESSION_CREATED = 'SESSION_CREATED',
    SESSION_EXPIRED = 'SESSION_EXPIRED',
    SESSION_INVALIDATED = 'SESSION_INVALIDATED',
    SESSION_IP_MISMATCH = 'SESSION_IP_MISMATCH',
    CONCURRENT_SESSION_LIMIT = 'CONCURRENT_SESSION_LIMIT',
    SUSPICIOUS_SESSION_ACTIVITY = 'SUSPICIOUS_SESSION_ACTIVITY',

    // Admin actions
    ADMIN_USER_CREATED = 'ADMIN_USER_CREATED',
    ADMIN_USER_UPDATED = 'ADMIN_USER_UPDATED',
    ADMIN_USER_DEACTIVATED = 'ADMIN_USER_DEACTIVATED',
    ADMIN_ROLE_CHANGED = 'ADMIN_ROLE_CHANGED',
    ADMIN_PERMISSIONS_MODIFIED = 'ADMIN_PERMISSIONS_MODIFIED',

    // Authentication and authorization events
    AUTHENTICATION_SUCCESS = 'AUTHENTICATION_SUCCESS',
    AUTHENTICATION_FAILED = 'AUTHENTICATION_FAILED',
    AUTHORIZATION_SUCCESS = 'AUTHORIZATION_SUCCESS',
    AUTHORIZATION_FAILED = 'AUTHORIZATION_FAILED',
    SESSION_INVALID = 'SESSION_INVALID',

    // Security violations
    RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
    CSRF_TOKEN_INVALID = 'CSRF_TOKEN_INVALID',
    UNAUTHORIZED_ACCESS_ATTEMPT = 'UNAUTHORIZED_ACCESS_ATTEMPT',
    INPUT_VALIDATION_FAILED = 'INPUT_VALIDATION_FAILED',
    SECURITY_HEADER_VIOLATION = 'SECURITY_HEADER_VIOLATION',

    // System events
    SYSTEM_SETTINGS_CHANGED = 'SYSTEM_SETTINGS_CHANGED',
    SECURITY_POLICY_UPDATED = 'SECURITY_POLICY_UPDATED',
    AUDIT_LOG_ACCESSED = 'AUDIT_LOG_ACCESSED',
    SECURITY_ALERT_TRIGGERED = 'SECURITY_ALERT_TRIGGERED',

    // IP Intelligence events
    IP_LOOKUP_PERFORMED = 'IP_LOOKUP_PERFORMED',
    IP_DATA_REFRESHED = 'IP_DATA_REFRESHED',
    IP_ANALYSIS_HISTORY_ACCESSED = 'IP_ANALYSIS_HISTORY_ACCESSED',
    IP_STATS_ACCESSED = 'IP_STATS_ACCESSED',
    IP_ANALYSIS_REQUESTED = 'IP_ANALYSIS_REQUESTED',
    IP_ANALYSIS_COMPLETED = 'IP_ANALYSIS_COMPLETED',
    IP_ANALYSIS_FAILED = 'IP_ANALYSIS_FAILED',
    IP_DATA_CLEANUP_EXECUTED = 'IP_DATA_CLEANUP_EXECUTED',

    // Freemius management events
    FREEMIUS_PRODUCTS_ACCESSED = 'FREEMIUS_PRODUCTS_ACCESSED',
    FREEMIUS_PRODUCT_DETAILS_ACCESSED = 'FREEMIUS_PRODUCT_DETAILS_ACCESSED',
    FREEMIUS_INSTALLATIONS_ACCESSED = 'FREEMIUS_INSTALLATIONS_ACCESSED',
    FREEMIUS_INSTALLATION_DETAILS_ACCESSED = 'FREEMIUS_INSTALLATION_DETAILS_ACCESSED',
    FREEMIUS_DATA_SYNCED = 'FREEMIUS_DATA_SYNCED',
    FREEMIUS_EVENTS_ACCESSED = 'FREEMIUS_EVENTS_ACCESSED',
    FREEMIUS_STATS_ACCESSED = 'FREEMIUS_STATS_ACCESSED',
    FREEMIUS_API_REQUEST = 'FREEMIUS_API_REQUEST',
    FREEMIUS_API_SUCCESS = 'FREEMIUS_API_SUCCESS',
    FREEMIUS_API_FAILED = 'FREEMIUS_API_FAILED',
    FREEMIUS_WEBHOOK_RECEIVED = 'FREEMIUS_WEBHOOK_RECEIVED',
    FREEMIUS_WEBHOOK_PROCESSED = 'FREEMIUS_WEBHOOK_PROCESSED',
    FREEMIUS_WEBHOOK_FAILED = 'FREEMIUS_WEBHOOK_FAILED',
    FREEMIUS_SYNC_STARTED = 'FREEMIUS_SYNC_STARTED',
    FREEMIUS_SYNC_COMPLETED = 'FREEMIUS_SYNC_COMPLETED',
    FREEMIUS_SYNC_FAILED = 'FREEMIUS_SYNC_FAILED',
    FREEMIUS_INSTALLATION_VALIDATED = 'FREEMIUS_INSTALLATION_VALIDATED',
    FREEMIUS_INSTALLATION_VALIDATION_FAILED = 'FREEMIUS_INSTALLATION_VALIDATION_FAILED'
}

// Base interface for all security events
export interface BaseSecurityEvent {
    eventType: SecurityEventType;
    userId?: string;
    email?: string;
    ipAddress: string;
    userAgent?: string;
    resource?: string;
    action?: string;
    details?: Record<string, any>;
    success: boolean;
    errorMessage?: string;
}

// Specific event interfaces
export interface AuthEvent extends BaseSecurityEvent {
    eventType: SecurityEventType.LOGIN_SUCCESS | SecurityEventType.LOGIN_FAILURE |
    SecurityEventType.LOGOUT | SecurityEventType.PASSWORD_CHANGE |
    SecurityEventType.PASSWORD_RESET_REQUEST | SecurityEventType.PASSWORD_RESET_SUCCESS |
    SecurityEventType.ACCOUNT_LOCKED | SecurityEventType.ACCOUNT_UNLOCKED;
    email: string;
    details?: {
        loginMethod?: string;
        failureReason?: string;
        lockoutDuration?: number;
        passwordStrength?: string;
        resetTokenId?: string;
    };
}

export interface SessionEvent extends BaseSecurityEvent {
    eventType: SecurityEventType.SESSION_CREATED | SecurityEventType.SESSION_EXPIRED |
    SecurityEventType.SESSION_INVALIDATED | SecurityEventType.SESSION_IP_MISMATCH |
    SecurityEventType.CONCURRENT_SESSION_LIMIT | SecurityEventType.SUSPICIOUS_SESSION_ACTIVITY;
    userId: string;
    details?: {
        sessionId?: string;
        deviceFingerprint?: string;
        location?: string;
        previousIp?: string;
        sessionCount?: number;
        suspiciousActivity?: string;
        expirationReason?: string;
    };
}

export interface AdminActionEvent extends BaseSecurityEvent {
    eventType: SecurityEventType.ADMIN_USER_CREATED | SecurityEventType.ADMIN_USER_UPDATED |
    SecurityEventType.ADMIN_USER_DEACTIVATED | SecurityEventType.ADMIN_ROLE_CHANGED |
    SecurityEventType.ADMIN_PERMISSIONS_MODIFIED;
    userId: string; // Actor performing the action
    resource: string; // Target resource (e.g., 'user')
    action: string; // Specific action performed
    details: {
        targetUserId?: string;
        targetEmail?: string;
        oldRole?: string;
        newRole?: string;
        changedFields?: string[];
        permissions?: string[];
        reason?: string;
    };
}

export interface SecurityViolationEvent extends BaseSecurityEvent {
    eventType: SecurityEventType.RATE_LIMIT_EXCEEDED | SecurityEventType.CSRF_TOKEN_INVALID |
    SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT | SecurityEventType.INPUT_VALIDATION_FAILED |
    SecurityEventType.SECURITY_HEADER_VIOLATION;
    success: false;
    details: {
        violationType: string;
        endpoint?: string;
        requestCount?: number;
        timeWindow?: string;
        expectedToken?: string;
        providedToken?: string;
        validationErrors?: string[];
        missingHeaders?: string[];
        attemptedResource?: string;
        requiredPermission?: string;
    };
}

export interface IpIntelligenceEvent extends BaseSecurityEvent {
    eventType: SecurityEventType.IP_LOOKUP_PERFORMED | SecurityEventType.IP_DATA_REFRESHED |
    SecurityEventType.IP_ANALYSIS_REQUESTED | SecurityEventType.IP_ANALYSIS_COMPLETED |
    SecurityEventType.IP_ANALYSIS_FAILED | SecurityEventType.IP_DATA_CLEANUP_EXECUTED |
    SecurityEventType.IP_ANALYSIS_HISTORY_ACCESSED | SecurityEventType.IP_STATS_ACCESSED;
    details: {
        targetIp?: string;
        requestSource?: 'plugin' | 'admin' | 'webhook';
        installationId?: string;
        riskScore?: number;
        recommendation?: 'allow' | 'block' | 'review';
        processingTimeMs?: number;
        usedCachedData?: boolean;
        dataAge?: number;
        cleanupCount?: number;
        threats?: string[];
        location?: {
            country?: string;
            region?: string;
            city?: string;
        };
        network?: {
            isp?: string;
            organization?: string;
            asn?: string;
        };
        securityFlags?: {
            isProxy?: boolean;
            isVpn?: boolean;
            isTor?: boolean;
            isMalware?: boolean;
        };
    };
}

export interface FreemiusEvent extends BaseSecurityEvent {
    eventType: SecurityEventType.FREEMIUS_API_REQUEST | SecurityEventType.FREEMIUS_API_SUCCESS |
    SecurityEventType.FREEMIUS_API_FAILED | SecurityEventType.FREEMIUS_WEBHOOK_RECEIVED |
    SecurityEventType.FREEMIUS_WEBHOOK_PROCESSED | SecurityEventType.FREEMIUS_WEBHOOK_FAILED |
    SecurityEventType.FREEMIUS_SYNC_STARTED | SecurityEventType.FREEMIUS_SYNC_COMPLETED |
    SecurityEventType.FREEMIUS_SYNC_FAILED | SecurityEventType.FREEMIUS_DATA_SYNCED |
    SecurityEventType.FREEMIUS_INSTALLATION_VALIDATED | SecurityEventType.FREEMIUS_INSTALLATION_VALIDATION_FAILED |
    SecurityEventType.FREEMIUS_PRODUCTS_ACCESSED | SecurityEventType.FREEMIUS_INSTALLATIONS_ACCESSED |
    SecurityEventType.FREEMIUS_EVENTS_ACCESSED | SecurityEventType.FREEMIUS_STATS_ACCESSED;
    details: {
        apiEndpoint?: string;
        httpMethod?: string;
        responseStatus?: number;
        responseTime?: number;
        productId?: string;
        installationId?: string;
        webhookEventId?: string;
        webhookEventType?: string;
        syncType?: 'products' | 'installations' | 'all';
        syncResults?: {
            productsUpdated?: number;
            installationsUpdated?: number;
            eventsProcessed?: number;
        };
        validationResults?: {
            isValid?: boolean;
            errors?: string[];
        };
        filters?: any;
        pagination?: {
            limit?: number;
            offset?: number;
        };
        errorDetails?: {
            code?: string;
            message?: string;
            stack?: string;
        };
    };
}

// Audit log query filters
export interface AuditLogFilters {
    eventTypes?: SecurityEventType[];
    userId?: string;
    email?: string;
    ipAddress?: string;
    resource?: string;
    action?: string;
    success?: boolean;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
}

// Audit log entry interface
export interface AuditLogEntry {
    id: string;
    eventType: string;
    userId?: string;
    email?: string;
    ipAddress: string;
    userAgent?: string;
    resource?: string;
    action?: string;
    details?: Record<string, any>;
    success: boolean;
    errorMessage?: string;
    createdAt: Date;
}

/**
 * Comprehensive Security Logger Service
 * 
 * Provides centralized security event logging with categorization,
 * integrity protection, and audit trail capabilities.
 */
export class SecurityLogger {
    /**
     * Log authentication events (login, logout, password changes, etc.)
     */
    static async logAuthEvent(event: AuthEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource || 'authentication',
                    action: event.action || event.eventType.toLowerCase(),
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: event.success,
                    error_message: event.errorMessage,
                },
            });

            // Log to console for development/debugging
            console.log(`[SECURITY] ${event.eventType}: ${event.email} from ${event.ipAddress} - ${event.success ? 'SUCCESS' : 'FAILED'}`);
        } catch (error) {
            console.error('Failed to log authentication event:', error);
            // Don't throw - logging failures shouldn't break the application
        }
    }

    /**
     * Log session-related security events
     */
    static async logSessionEvent(event: SessionEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource || 'session',
                    action: event.action || event.eventType.toLowerCase(),
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: event.success,
                    error_message: event.errorMessage,
                },
            });

            console.log(`[SECURITY] ${event.eventType}: User ${event.userId} from ${event.ipAddress} - ${event.success ? 'SUCCESS' : 'FAILED'}`);
        } catch (error) {
            console.error('Failed to log session event:', error);
        }
    }

    /**
     * Log administrative actions with actor and target details
     */
    static async logAdminAction(event: AdminActionEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource,
                    action: event.action,
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: event.success,
                    error_message: event.errorMessage,
                },
            });

            console.log(`[SECURITY] ${event.eventType}: Admin ${event.userId} performed ${event.action} on ${event.resource} - ${event.success ? 'SUCCESS' : 'FAILED'}`);
        } catch (error) {
            console.error('Failed to log admin action:', error);
        }
    }

    /**
     * Log security violations and suspicious activities
     */
    static async logSecurityViolation(event: SecurityViolationEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource || 'security',
                    action: event.action || 'violation',
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: false, // Security violations are always failures
                    error_message: event.errorMessage,
                },
            });

            // Security violations should be more prominently logged
            console.warn(`[SECURITY VIOLATION] ${event.eventType}: ${event.details.violationType} from ${event.ipAddress}`);
        } catch (error) {
            console.error('Failed to log security violation:', error);
        }
    }

    /**
     * Log IP intelligence operations with comprehensive context
     */
    static async logIpIntelligenceEvent(event: IpIntelligenceEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource || 'ip_intelligence',
                    action: event.action || event.eventType.toLowerCase(),
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: event.success,
                    error_message: event.errorMessage,
                },
            });

            const logLevel = event.success ? 'log' : 'error';
            const status = event.success ? 'SUCCESS' : 'FAILED';
            const targetIp = event.details.targetIp || 'unknown';

            console[logLevel](`[IP INTELLIGENCE] ${event.eventType}: ${targetIp} - ${status}`, {
                riskScore: event.details.riskScore,
                recommendation: event.details.recommendation,
                processingTime: event.details.processingTimeMs,
                usedCache: event.details.usedCachedData,
            });
        } catch (error) {
            console.error('Failed to log IP intelligence event:', error);
        }
    }

    /**
     * Log Freemius API operations and management actions
     */
    static async logFreemiusEvent(event: FreemiusEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource || 'freemius',
                    action: event.action || event.eventType.toLowerCase(),
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: event.success,
                    error_message: event.errorMessage,
                },
            });

            const logLevel = event.success ? 'log' : 'error';
            const status = event.success ? 'SUCCESS' : 'FAILED';

            console[logLevel](`[FREEMIUS] ${event.eventType}: ${status}`, {
                endpoint: event.details.apiEndpoint,
                responseTime: event.details.responseTime,
                productId: event.details.productId,
                installationId: event.details.installationId,
                syncResults: event.details.syncResults,
            });
        } catch (error) {
            console.error('Failed to log Freemius event:', error);
        }
    }

    /**
     * Generic method to log any security event
     */
    static async logSecurityEvent(event: BaseSecurityEvent): Promise<void> {
        try {
            await prisma.securityAuditLog.create({
                data: {
                    event_type: event.eventType,
                    user_id: event.userId,
                    email: event.email,
                    ip_address: event.ipAddress,
                    user_agent: event.userAgent,
                    resource: event.resource,
                    action: event.action,
                    details: event.details ? JSON.parse(JSON.stringify(event.details)) : null,
                    success: event.success,
                    error_message: event.errorMessage,
                },
            });

            console.log(`[SECURITY] ${event.eventType}: ${event.success ? 'SUCCESS' : 'FAILED'}`);
        } catch (error) {
            console.error('Failed to log security event:', error);
        }
    }

    /**
     * Query audit logs with filtering and pagination
     */
    static async queryAuditLog(filters: AuditLogFilters = {}): Promise<AuditLogEntry[]> {
        try {
            const where: any = {};

            // Apply filters
            if (filters.eventTypes && filters.eventTypes.length > 0) {
                where.event_type = { in: filters.eventTypes };
            }

            if (filters.userId) {
                where.user_id = filters.userId;
            }

            if (filters.email) {
                where.email = { contains: filters.email, mode: 'insensitive' };
            }

            if (filters.ipAddress) {
                where.ip_address = filters.ipAddress;
            }

            if (filters.resource) {
                where.resource = { contains: filters.resource, mode: 'insensitive' };
            }

            if (filters.action) {
                where.action = { contains: filters.action, mode: 'insensitive' };
            }

            if (filters.success !== undefined) {
                where.success = filters.success;
            }

            if (filters.startDate || filters.endDate) {
                where.created_at = {};
                if (filters.startDate) {
                    where.created_at.gte = filters.startDate;
                }
                if (filters.endDate) {
                    where.created_at.lte = filters.endDate;
                }
            }

            const logs = await prisma.securityAuditLog.findMany({
                where,
                orderBy: { created_at: 'desc' },
                take: filters.limit || 100,
                skip: filters.offset || 0,
            });

            return logs.map((log: any) => ({
                id: log.id,
                eventType: log.event_type,
                userId: log.user_id || undefined,
                email: log.email || undefined,
                ipAddress: log.ip_address,
                userAgent: log.user_agent || undefined,
                resource: log.resource || undefined,
                action: log.action || undefined,
                details: log.details as Record<string, any> || undefined,
                success: log.success,
                errorMessage: log.error_message || undefined,
                createdAt: log.created_at,
            }));
        } catch (error) {
            console.error('Failed to query audit log:', error);
            throw new Error('Failed to retrieve audit logs');
        }
    }

    /**
     * Get audit log statistics for monitoring
     */
    static async getAuditLogStats(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<{
        totalEvents: number;
        successfulEvents: number;
        failedEvents: number;
        uniqueUsers: number;
        uniqueIPs: number;
        topEventTypes: Array<{ eventType: string; count: number }>;
    }> {
        try {
            const now = new Date();
            let startDate: Date;

            switch (timeframe) {
                case 'hour':
                    startDate = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case 'day':
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
            }

            const [totalEvents, successfulEvents, failedEvents, uniqueUsers, uniqueIPs, eventTypeCounts] = await Promise.all([
                // Total events
                prisma.securityAuditLog.count({
                    where: { created_at: { gte: startDate } }
                }),

                // Successful events
                prisma.securityAuditLog.count({
                    where: {
                        created_at: { gte: startDate },
                        success: true
                    }
                }),

                // Failed events
                prisma.securityAuditLog.count({
                    where: {
                        created_at: { gte: startDate },
                        success: false
                    }
                }),

                // Unique users
                prisma.securityAuditLog.findMany({
                    where: {
                        created_at: { gte: startDate },
                        user_id: { not: null }
                    },
                    select: { user_id: true },
                    distinct: ['user_id']
                }),

                // Unique IPs
                prisma.securityAuditLog.findMany({
                    where: { created_at: { gte: startDate } },
                    select: { ip_address: true },
                    distinct: ['ip_address']
                }),

                // Event type counts
                prisma.securityAuditLog.groupBy({
                    by: ['event_type'],
                    where: { created_at: { gte: startDate } },
                    _count: { event_type: true },
                    orderBy: { _count: { event_type: 'desc' } },
                    take: 10
                })
            ]);

            return {
                totalEvents,
                successfulEvents,
                failedEvents,
                uniqueUsers: uniqueUsers.length,
                uniqueIPs: uniqueIPs.length,
                topEventTypes: eventTypeCounts.map((item: any) => ({
                    eventType: item.event_type,
                    count: item._count.event_type
                }))
            };
        } catch (error) {
            console.error('Failed to get audit log statistics:', error);
            throw new Error('Failed to retrieve audit log statistics');
        }
    }

    /**
     * Clean up old audit logs based on retention policy
     */
    static async cleanupOldLogs(retentionDays: number = 90): Promise<number> {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

            const result = await prisma.securityAuditLog.deleteMany({
                where: {
                    created_at: { lt: cutoffDate }
                }
            });

            console.log(`[SECURITY] Cleaned up ${result.count} audit log entries older than ${retentionDays} days`);
            return result.count;
        } catch (error) {
            console.error('Failed to cleanup old audit logs:', error);
            throw new Error('Failed to cleanup audit logs');
        }
    }
}

// Export convenience functions for common logging scenarios
export const logAuthSuccess = (email: string, userId: string, ipAddress: string, userAgent?: string, details?: any) =>
    SecurityLogger.logAuthEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        userId,
        email,
        ipAddress,
        userAgent,
        success: true,
        details
    });

export const logAuthFailure = (email: string, ipAddress: string, userAgent?: string, reason?: string) =>
    SecurityLogger.logAuthEvent({
        eventType: SecurityEventType.LOGIN_FAILURE,
        email,
        ipAddress,
        userAgent,
        success: false,
        errorMessage: reason,
        details: { failureReason: reason }
    });

export const logLogout = (email: string, userId: string, ipAddress: string, userAgent?: string) =>
    SecurityLogger.logAuthEvent({
        eventType: SecurityEventType.LOGOUT,
        userId,
        email,
        ipAddress,
        userAgent,
        success: true
    });

export const logPasswordChange = (email: string, userId: string, ipAddress: string, userAgent?: string) =>
    SecurityLogger.logAuthEvent({
        eventType: SecurityEventType.PASSWORD_CHANGE,
        userId,
        email,
        ipAddress,
        userAgent,
        success: true
    });

export const logAccountLocked = (email: string, ipAddress: string, lockoutDuration: number) =>
    SecurityLogger.logAuthEvent({
        eventType: SecurityEventType.ACCOUNT_LOCKED,
        email,
        ipAddress,
        success: false,
        details: { lockoutDuration }
    });

export const logSessionCreated = (userId: string, sessionId: string, ipAddress: string, userAgent?: string, deviceFingerprint?: string) =>
    SecurityLogger.logSessionEvent({
        eventType: SecurityEventType.SESSION_CREATED,
        userId,
        ipAddress,
        userAgent,
        success: true,
        details: { sessionId, deviceFingerprint }
    });

export const logSessionExpired = (userId: string, sessionId: string, ipAddress: string, reason: string) =>
    SecurityLogger.logSessionEvent({
        eventType: SecurityEventType.SESSION_EXPIRED,
        userId,
        ipAddress,
        success: true,
        details: { sessionId, expirationReason: reason }
    });

export const logRateLimitExceeded = (ipAddress: string, endpoint: string, requestCount: number, timeWindow: string) =>
    SecurityLogger.logSecurityViolation({
        eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
        ipAddress,
        userAgent: undefined,
        success: false,
        details: {
            violationType: 'rate_limit',
            endpoint,
            requestCount,
            timeWindow
        }
    });

export const logUnauthorizedAccess = (userId: string | undefined, ipAddress: string, resource: string, requiredPermission: string, userAgent?: string) =>
    SecurityLogger.logSecurityViolation({
        eventType: SecurityEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
        userId,
        ipAddress,
        userAgent,
        resource,
        success: false,
        details: {
            violationType: 'unauthorized_access',
            attemptedResource: resource,
            requiredPermission
        }
    });

// IP Intelligence logging convenience functions
export const logIpAnalysisRequest = (
    targetIp: string,
    requestSource: 'plugin' | 'admin' | 'webhook',
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    installationId?: string
) =>
    SecurityLogger.logIpIntelligenceEvent({
        eventType: SecurityEventType.IP_ANALYSIS_REQUESTED,
        userId,
        email,
        ipAddress: ipAddress || targetIp,
        userAgent,
        success: true,
        details: {
            targetIp,
            requestSource,
            installationId,
        }
    });

export const logIpAnalysisCompleted = (
    targetIp: string,
    riskScore: number,
    recommendation: 'allow' | 'block' | 'review',
    processingTimeMs: number,
    usedCachedData: boolean,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    threats?: string[],
    location?: any,
    network?: any,
    securityFlags?: any
) =>
    SecurityLogger.logIpIntelligenceEvent({
        eventType: SecurityEventType.IP_ANALYSIS_COMPLETED,
        userId,
        email,
        ipAddress: ipAddress || targetIp,
        userAgent,
        success: true,
        details: {
            targetIp,
            riskScore,
            recommendation,
            processingTimeMs,
            usedCachedData,
            threats,
            location,
            network,
            securityFlags,
        }
    });

export const logIpAnalysisFailed = (
    targetIp: string,
    errorMessage: string,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
) =>
    SecurityLogger.logIpIntelligenceEvent({
        eventType: SecurityEventType.IP_ANALYSIS_FAILED,
        userId,
        email,
        ipAddress: ipAddress || targetIp,
        userAgent,
        success: false,
        errorMessage,
        details: {
            targetIp,
        }
    });

export const logIpDataRefresh = (
    targetIp: string,
    success: boolean,
    dataAge?: number,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    errorMessage?: string
) =>
    SecurityLogger.logIpIntelligenceEvent({
        eventType: SecurityEventType.IP_DATA_REFRESHED,
        userId,
        email,
        ipAddress: ipAddress || targetIp,
        userAgent,
        success,
        errorMessage,
        details: {
            targetIp,
            dataAge,
        }
    });

export const logIpDataCleanup = (
    cleanupCount: number,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
) =>
    SecurityLogger.logIpIntelligenceEvent({
        eventType: SecurityEventType.IP_DATA_CLEANUP_EXECUTED,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: true,
        details: {
            cleanupCount,
        }
    });

// Freemius logging convenience functions
export const logFreemiusApiRequest = (
    apiEndpoint: string,
    httpMethod: string,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    productId?: string,
    installationId?: string
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_API_REQUEST,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: true,
        details: {
            apiEndpoint,
            httpMethod,
            productId,
            installationId,
        }
    });

export const logFreemiusApiSuccess = (
    apiEndpoint: string,
    responseStatus: number,
    responseTime: number,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    productId?: string,
    installationId?: string
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_API_SUCCESS,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: true,
        details: {
            apiEndpoint,
            responseStatus,
            responseTime,
            productId,
            installationId,
        }
    });

export const logFreemiusApiFailed = (
    apiEndpoint: string,
    errorMessage: string,
    responseStatus?: number,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    productId?: string,
    installationId?: string,
    errorDetails?: any
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_API_FAILED,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: false,
        errorMessage,
        details: {
            apiEndpoint,
            responseStatus,
            productId,
            installationId,
            errorDetails,
        }
    });

export const logFreemiusSyncStarted = (
    syncType: 'products' | 'installations' | 'all',
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_SYNC_STARTED,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: true,
        details: {
            syncType,
        }
    });

export const logFreemiusSyncCompleted = (
    syncType: 'products' | 'installations' | 'all',
    syncResults: { productsUpdated?: number; installationsUpdated?: number; eventsProcessed?: number },
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_SYNC_COMPLETED,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: true,
        details: {
            syncType,
            syncResults,
        }
    });

export const logFreemiusSyncFailed = (
    syncType: 'products' | 'installations' | 'all',
    errorMessage: string,
    userId?: string,
    email?: string,
    ipAddress?: string,
    userAgent?: string,
    errorDetails?: any
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_SYNC_FAILED,
        userId,
        email,
        ipAddress: ipAddress || 'system',
        userAgent,
        success: false,
        errorMessage,
        details: {
            syncType,
            errorDetails,
        }
    });

export const logFreemiusWebhookReceived = (
    webhookEventId: string,
    webhookEventType: string,
    ipAddress: string,
    userAgent?: string
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_WEBHOOK_RECEIVED,
        ipAddress,
        userAgent,
        success: true,
        details: {
            webhookEventId,
            webhookEventType,
        }
    });

export const logFreemiusWebhookProcessed = (
    webhookEventId: string,
    webhookEventType: string,
    ipAddress: string,
    userAgent?: string
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_WEBHOOK_PROCESSED,
        ipAddress,
        userAgent,
        success: true,
        details: {
            webhookEventId,
            webhookEventType,
        }
    });

export const logFreemiusWebhookFailed = (
    webhookEventId: string,
    webhookEventType: string,
    errorMessage: string,
    ipAddress: string,
    userAgent?: string,
    errorDetails?: any
) =>
    SecurityLogger.logFreemiusEvent({
        eventType: SecurityEventType.FREEMIUS_WEBHOOK_FAILED,
        ipAddress,
        userAgent,
        success: false,
        errorMessage,
        details: {
            webhookEventId,
            webhookEventType,
            errorDetails,
        }
    });