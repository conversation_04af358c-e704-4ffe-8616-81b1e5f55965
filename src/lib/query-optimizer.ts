import { prisma } from './prisma';
import { Prisma } from '../generated/prisma';

// Query optimization utilities for large datasets
export class QueryOptimizer {
    /**
     * Optimized pagination with cursor-based approach for better performance
     */
    static async paginateWithCursor<T>(
        model: any,
        options: {
            where?: any;
            orderBy?: any;
            cursor?: any;
            take?: number;
            select?: any;
            include?: any;
        }
    ): Promise<{
        data: T[];
        nextCursor?: any;
        hasMore: boolean;
        total?: number;
    }> {
        const { where, orderBy, cursor, take = 50, select, include } = options;

        // Limit take to prevent excessive memory usage
        const limitedTake = Math.min(take, 1000);

        const queryOptions: any = {
            where,
            orderBy,
            take: limitedTake + 1, // Take one extra to check if there are more
            select,
            include,
        };

        if (cursor) {
            queryOptions.cursor = cursor;
            queryOptions.skip = 1; // Skip the cursor item
        }

        const results = await model.findMany(queryOptions);

        const hasMore = results.length > limitedTake;
        const data = hasMore ? results.slice(0, limitedTake) : results;
        const nextCursor = hasMore ? results[limitedTake - 1] : null;

        return {
            data,
            nextCursor,
            hasMore,
        };
    }

    /**
     * Optimized audit log queries with proper indexing
     */
    static async getAuditLogs(filters: {
        timeRange?: string;
        eventTypes?: string[];
        userId?: string;
        email?: string;
        ipAddress?: string;
        resource?: string;
        action?: string;
        success?: boolean;
        suspiciousOnly?: boolean;
        searchTerm?: string;
        limit?: number;
        offset?: number;
        cursor?: string;
    }) {
        const {
            timeRange,
            eventTypes,
            userId,
            email,
            ipAddress,
            resource,
            action,
            success,
            suspiciousOnly,
            searchTerm,
            limit = 50,
            offset = 0,
            cursor
        } = filters;

        // Build optimized where clause
        const where: Prisma.SecurityAuditLogWhereInput = {};

        // Time range filter (most selective, should be first)
        if (timeRange) {
            const timeFilter = this.buildTimeFilter(timeRange);
            if (timeFilter) {
                where.created_at = timeFilter;
            }
        }

        // Event type filter (highly selective)
        if (eventTypes && eventTypes.length > 0) {
            where.event_type = { in: eventTypes };
        }

        // User filters
        if (userId) {
            where.user_id = userId;
        }
        if (email) {
            where.email = { contains: email };
        }

        // Network filters
        if (ipAddress) {
            where.ip_address = { contains: ipAddress };
        }

        // Resource filters
        if (resource) {
            where.resource = { contains: resource };
        }
        if (action) {
            where.action = { contains: action };
        }

        // Success filter
        if (success !== undefined) {
            where.success = success;
        }

        // Suspicious activity filter
        if (suspiciousOnly) {
            where.OR = [
                { success: false },
                { event_type: { in: ['LOGIN_FAILURE', 'PERMISSION_DENIED', 'SUSPICIOUS_ACTIVITY'] } },
                { error_message: { not: null } }
            ];
        }

        // Search term (least selective, should be last)
        if (searchTerm) {
            where.OR = [
                { email: { contains: searchTerm } },
                { resource: { contains: searchTerm } },
                { action: { contains: searchTerm } },
                { error_message: { contains: searchTerm } }
            ];
        }

        // Use cursor-based pagination for better performance
        if (cursor) {
            return this.paginateWithCursor(prisma.securityAuditLog, {
                where,
                orderBy: { created_at: 'desc' },
                cursor: { id: cursor },
                take: limit,
            });
        }

        // Fallback to offset-based pagination
        const [data, total] = await Promise.all([
            prisma.securityAuditLog.findMany({
                where,
                orderBy: { created_at: 'desc' },
                take: Math.min(limit, 1000),
                skip: offset,
            }),
            prisma.securityAuditLog.count({ where })
        ]);

        return {
            data,
            total,
            hasMore: offset + data.length < total,
        };
    }

    /**
     * Optimized IP analysis request queries
     */
    static async getIpAnalysisRequests(filters: {
        timeRange?: string;
        ipAddress?: string;
        installationId?: string;
        requestSource?: string;
        riskScoreMin?: number;
        riskScoreMax?: number;
        recommendation?: string;
        limit?: number;
        offset?: number;
        cursor?: string;
    }) {
        const {
            timeRange,
            ipAddress,
            installationId,
            requestSource,
            riskScoreMin,
            riskScoreMax,
            recommendation,
            limit = 50,
            offset = 0,
            cursor
        } = filters;

        const where: Prisma.IpAnalysisRequestWhereInput = {};

        // Time range filter
        if (timeRange) {
            const timeFilter = this.buildTimeFilter(timeRange);
            if (timeFilter) {
                where.requested_at = timeFilter;
            }
        }

        // IP address filter
        if (ipAddress) {
            where.ip_address = { contains: ipAddress };
        }

        // Installation filter
        if (installationId) {
            where.installation_id = installationId;
        }

        // Request source filter
        if (requestSource) {
            where.request_source = requestSource;
        }

        // Risk score range filter
        if (riskScoreMin !== undefined || riskScoreMax !== undefined) {
            where.risk_score = {};
            if (riskScoreMin !== undefined) {
                where.risk_score.gte = riskScoreMin;
            }
            if (riskScoreMax !== undefined) {
                where.risk_score.lte = riskScoreMax;
            }
        }

        // Recommendation filter
        if (recommendation) {
            where.recommendation = recommendation;
        }

        // Use cursor-based pagination
        if (cursor) {
            return this.paginateWithCursor(prisma.ipAnalysisRequest, {
                where,
                orderBy: { requested_at: 'desc' },
                cursor: { id: cursor },
                take: limit,
                include: {
                    ip_data: {
                        select: {
                            country_name: true,
                            city: true,
                            isp: true,
                            is_threat: true,
                            is_vpn: true,
                            is_proxy: true,
                        }
                    },
                    installation: {
                        select: {
                            id: true,
                            url: true,
                            version: true,
                            is_active: true,
                        }
                    }
                }
            });
        }

        // Fallback to offset-based pagination
        const [data, total] = await Promise.all([
            prisma.ipAnalysisRequest.findMany({
                where,
                orderBy: { requested_at: 'desc' },
                take: Math.min(limit, 1000),
                skip: offset,
                include: {
                    ip_data: {
                        select: {
                            country_name: true,
                            city: true,
                            isp: true,
                            is_threat: true,
                            is_vpn: true,
                            is_proxy: true,
                        }
                    },
                    installation: {
                        select: {
                            id: true,
                            url: true,
                            version: true,
                            is_active: true,
                        }
                    }
                }
            }),
            prisma.ipAnalysisRequest.count({ where })
        ]);

        return {
            data,
            total,
            hasMore: offset + data.length < total,
        };
    }

    /**
     * Optimized Freemius installation queries
     */
    static async getFreemiusInstallations(filters: {
        productId?: string;
        isActive?: boolean;
        isPremium?: boolean;
        isUninstalled?: boolean;
        countryCode?: string;
        version?: string;
        lastSeenAfter?: Date;
        limit?: number;
        offset?: number;
        cursor?: string;
    }) {
        const {
            productId,
            isActive,
            isPremium,
            isUninstalled,
            countryCode,
            version,
            lastSeenAfter,
            limit = 50,
            offset = 0,
            cursor
        } = filters;

        const where: Prisma.FreemiusInstallationWhereInput = {};

        // Product filter
        if (productId) {
            where.plugin_id = productId;
        }

        // Status filters
        if (isActive !== undefined) {
            where.is_active = isActive;
        }
        if (isPremium !== undefined) {
            where.is_premium = isPremium;
        }
        if (isUninstalled !== undefined) {
            where.is_uninstalled = isUninstalled;
        }

        // Location filter
        if (countryCode) {
            where.country_code = countryCode;
        }

        // Version filter
        if (version) {
            where.version = { contains: version };
        }

        // Last seen filter
        if (lastSeenAfter) {
            where.last_seen_at = { gte: lastSeenAfter };
        }

        // Use cursor-based pagination
        if (cursor) {
            return this.paginateWithCursor(prisma.freemiusInstallation, {
                where,
                orderBy: { created: 'desc' },
                cursor: { id: cursor },
                take: limit,
                select: {
                    id: true,
                    url: true,
                    title: true,
                    version: true,
                    is_active: true,
                    is_premium: true,
                    is_uninstalled: true,
                    country_code: true,
                    created: true,
                    last_seen_at: true,
                    gross: true,
                }
            });
        }

        // Fallback to offset-based pagination
        const [data, total] = await Promise.all([
            prisma.freemiusInstallation.findMany({
                where,
                orderBy: { created: 'desc' },
                take: Math.min(limit, 1000),
                skip: offset,
                select: {
                    id: true,
                    url: true,
                    title: true,
                    version: true,
                    is_active: true,
                    is_premium: true,
                    is_uninstalled: true,
                    country_code: true,
                    created: true,
                    last_seen_at: true,
                    gross: true,
                }
            }),
            prisma.freemiusInstallation.count({ where })
        ]);

        return {
            data,
            total,
            hasMore: offset + data.length < total,
        };
    }

    /**
     * Build time filter for queries
     */
    private static buildTimeFilter(timeRange: string): Prisma.DateTimeFilter | null {
        const now = new Date();
        let startDate: Date;

        switch (timeRange) {
            case 'last_hour':
                startDate = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case 'last_24h':
                startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case 'last_7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'last_30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case 'last_90d':
                startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                return null;
        }

        return { gte: startDate };
    }

    /**
     * Verify database indexes are properly created
     */
    static async verifyOptimizedIndexes(): Promise<{
        indexesFound: string[];
        recommendations: string[];
    }> {
        try {
            // For SQLite, we can check the sqlite_master table
            const indexes = await prisma.$queryRaw<Array<{ name: string; sql: string }>>`
                SELECT name, sql FROM sqlite_master 
                WHERE type = 'index' AND name LIKE 'idx_%'
                ORDER BY name;
            `;

            const indexesFound = indexes.map(idx => idx.name);
            const recommendations: string[] = [];

            // Expected indexes from our schema
            const expectedIndexes = [
                'idx_security_audit_log_time_type',
                'idx_security_audit_log_user_time',
                'idx_security_audit_log_ip_time',
                'idx_security_audit_log_success_time',
                'idx_ip_analysis_request_time_source',
                'idx_ip_analysis_request_ip_time',
                'idx_ip_analysis_request_installation',
                'idx_ip_analysis_request_risk_score',
                'idx_freemius_installation_active_premium',
                'idx_freemius_installation_product_active',
                'idx_freemius_installation_country',
                'idx_freemius_installation_version',
                'idx_ip_registry_data_expires',
                'idx_ip_registry_data_country',
                'idx_ip_registry_data_threat',
                'idx_freemius_event_type_time',
                'idx_freemius_event_install_time',
                'idx_freemius_event_product_time',
                'idx_user_session_active_time',
                'idx_user_session_expires',
                'idx_rate_limit_tracking_expires',
                'idx_rate_limit_tracking_key_window',
                'idx_admin_activity_user_time',
                'idx_admin_activity_action_time',
                'idx_admin_activity_resource_time'
            ];

            const missingIndexes = expectedIndexes.filter(idx => !indexesFound.includes(idx));

            if (missingIndexes.length > 0) {
                recommendations.push(`Missing indexes: ${missingIndexes.join(', ')}. Run 'npx prisma db push' to create them.`);
            } else {
                recommendations.push('All performance indexes are properly created.');
            }

            console.log(`Found ${indexesFound.length} performance indexes`);
            return { indexesFound, recommendations };
        } catch (error) {
            console.error('Failed to verify database indexes:', error);
            return {
                indexesFound: [],
                recommendations: ['Failed to verify indexes. Check database connection.']
            };
        }
    }

    /**
     * Analyze query performance and suggest optimizations
     */
    static async analyzeQueryPerformance(): Promise<{
        slowQueries: Array<{
            query: string;
            avgTime: number;
            callCount: number;
        }>;
        recommendations: string[];
    }> {
        // This would typically analyze query logs, but for now we'll provide general recommendations
        const recommendations = [
            'Use cursor-based pagination for large datasets instead of OFFSET',
            'Add composite indexes for frequently filtered columns',
            'Use SELECT with specific fields instead of SELECT *',
            'Consider using database views for complex queries',
            'Implement query result caching for expensive operations',
            'Use connection pooling to reduce connection overhead',
            'Consider read replicas for read-heavy operations',
        ];

        return {
            slowQueries: [], // Would be populated from actual query analysis
            recommendations,
        };
    }

    /**
     * Get database statistics for monitoring
     */
    static async getDatabaseStats(): Promise<{
        tableStats: Array<{
            table: string;
            rowCount: number;
            sizeEstimate: string;
        }>;
        indexUsage: Array<{
            index: string;
            usage: number;
        }>;
    }> {
        try {
            // Get table row counts
            const tableStats = await Promise.all([
                this.getTableRowCount('users', 'User'),
                this.getTableRowCount('security_audit_log', 'SecurityAuditLog'),
                this.getTableRowCount('ip_analysis_requests', 'IpAnalysisRequest'),
                this.getTableRowCount('ip_registry_data', 'IpRegistryData'),
                this.getTableRowCount('freemius_installations', 'FreemiusInstallation'),
                this.getTableRowCount('freemius_products', 'FreemiusProduct'),
                this.getTableRowCount('freemius_events', 'FreemiusEvent'),
            ]);

            return {
                tableStats: tableStats.filter(stat => stat !== null) as Array<{
                    table: string;
                    rowCount: number;
                    sizeEstimate: string;
                }>,
                indexUsage: [], // Would be populated from database-specific queries
            };
        } catch (error) {
            console.error('Failed to get database stats:', error);
            return {
                tableStats: [],
                indexUsage: [],
            };
        }
    }

    /**
     * Get row count for a specific table
     */
    private static async getTableRowCount(tableName: string, modelName: string): Promise<{
        table: string;
        rowCount: number;
        sizeEstimate: string;
    } | null> {
        try {
            const count = await (prisma as any)[modelName.charAt(0).toLowerCase() + modelName.slice(1)].count();

            // Rough size estimate (this would be more accurate with database-specific queries)
            const estimatedSizeKB = count * 0.5; // Rough estimate of 0.5KB per row
            const sizeEstimate = estimatedSizeKB > 1024
                ? `${(estimatedSizeKB / 1024).toFixed(2)} MB`
                : `${estimatedSizeKB.toFixed(2)} KB`;

            return {
                table: tableName,
                rowCount: count,
                sizeEstimate,
            };
        } catch (error) {
            console.error(`Failed to get row count for ${tableName}:`, error);
            return null;
        }
    }
}

export default QueryOptimizer;