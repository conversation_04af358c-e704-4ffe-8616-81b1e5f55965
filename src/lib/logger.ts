import * as Sentry from '@sentry/node';

/**
 * Centralized Logger utility with Sentry integration
 * Replaces console logging throughout the application
 * 
 * Note: Sentry is initialized in src/instrument.ts and imported at the top of server.ts
 */
export class Logger {

    /**
     * Filter sensitive data from logs
     */
    private static filterSensitiveData(data: any): any {
        if (typeof data !== 'object' || data === null) {
            return data;
        }

        const sensitiveFields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'cookie', 'session', 'api_key', 'apiKey', 'secretKey'
        ];

        const filtered = { ...data };

        for (const field of sensitiveFields) {
            if (field in filtered) {
                filtered[field] = '[REDACTED]';
            }
        }

        return filtered;
    }

    /**
     * Log info level messages
     */
    static info(message: string, extra?: any, tags?: Record<string, string>): void {
        // Always log to console in development
        if (process.env.NODE_ENV === 'development') {
            console.log(`[INFO] ${message}`, extra ? JSON.stringify(extra, null, 2) : '');
        }

        // Add breadcrumb to Sentry
        Sentry.addBreadcrumb({
            message,
            level: 'info',
            data: extra ? this.filterSensitiveData(extra) : undefined,
            category: 'logger'
        });

        // Set tags if provided
        if (tags) {
            Sentry.setTags(tags);
        }
    }

    /**
     * Log warning level messages
     */
    static warn(message: string, extra?: any, tags?: Record<string, string>): void {
        // Always log to console in development
        if (process.env.NODE_ENV === 'development') {
            console.warn(`[WARN] ${message}`, extra ? JSON.stringify(extra, null, 2) : '');
        }

        // Capture as message in Sentry
        Sentry.captureMessage(message, 'warning');

        // Add breadcrumb
        Sentry.addBreadcrumb({
            message,
            level: 'warning',
            data: extra ? this.filterSensitiveData(extra) : undefined,
            category: 'logger'
        });

        // Set tags if provided
        if (tags) {
            Sentry.setTags(tags);
        }
    }

    /**
     * Log error level messages
     */
    static error(message: string, error?: Error | any, extra?: any, tags?: Record<string, string>): void {
        // Always log to console in development
        if (process.env.NODE_ENV === 'development') {
            console.error(`[ERROR] ${message}`, error, extra ? JSON.stringify(extra, null, 2) : '');
        }

        // Capture exception in Sentry
        if (error instanceof Error) {
            Sentry.captureException(error, {
                tags: { level: 'error', ...tags },
                extra: extra ? this.filterSensitiveData(extra) : undefined,
                contexts: {
                    logger: {
                        message,
                        timestamp: new Date().toISOString()
                    }
                }
            });
        } else {
            // If no Error object, capture as message
            Sentry.captureMessage(`${message}: ${error}`, 'error');
        }

        // Add breadcrumb
        Sentry.addBreadcrumb({
            message,
            level: 'error',
            data: {
                error: error instanceof Error ? error.message : error,
                ...extra ? this.filterSensitiveData(extra) : {}
            },
            category: 'logger'
        });
    }

    /**
     * Log debug level messages (only in development)
     */
    static debug(message: string, extra?: any): void {
        if (process.env.NODE_ENV === 'development') {
            console.debug(`[DEBUG] ${message}`, extra ? JSON.stringify(extra, null, 2) : '');
        }

        // Add breadcrumb in development
        if (process.env.NODE_ENV === 'development') {
            Sentry.addBreadcrumb({
                message,
                level: 'debug',
                data: extra ? this.filterSensitiveData(extra) : undefined,
                category: 'logger'
            });
        }
    }

    /**
     * Set user context for Sentry
     */
    static setUserContext(user: { id?: string; email?: string; role?: string }): void {
        Sentry.setUser({
            id: user.id,
            email: user.email,
            role: user.role
        });
    }

    /**
     * Set additional context for Sentry
     */
    static setContext(key: string, context: any): void {
        Sentry.setContext(key, this.filterSensitiveData(context));
    }

    /**
     * Add tags to Sentry
     */
    static setTags(tags: Record<string, string>): void {
        Sentry.setTags(tags);
    }

    /**
     * Start a Sentry span for performance monitoring
     */
    static startSpan<T>(options: { name: string; op?: string }, callback: () => T): T {
        return Sentry.startSpan(options, callback);
    }

    /**
     * Flush Sentry events (useful for testing or shutdown)
     */
    static async flush(timeout = 2000): Promise<boolean> {
        return await Sentry.flush(timeout);
    }
}

// Export default instance for convenience
export default Logger;