import { PrismaClient } from '@prisma/client';
import { ActivityLogger, AdminAction } from './activity-logger';

const prisma = new PrismaClient();

export interface UserProfileUpdate {
  display_name?: string;
  avatar_url?: string;
  timezone?: string;
  date_format?: string;
  time_format?: string;
  language?: string;
  theme?: string;
  email_notifications?: boolean;
  security_alerts?: boolean;
  system_updates?: boolean;
  activity_digest?: string;
  show_activity?: boolean;
  session_timeout?: number;
}

export class ProfileManager {
  static async getOrCreateProfile(userId: string): Promise<any> {
    let profile = await prisma.userProfileSettings.findUnique({
      where: { user_id: userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
            is_2fa_enabled: true,
            created_at: true,
            updated_at: true,
            last_login: true
          }
        }
      }
    });

    if (!profile) {
      profile = await prisma.userProfileSettings.create({
        data: {
          user_id: userId,
          // Default values are set in schema
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              role: true,
              is_2fa_enabled: true,
              created_at: true,
              updated_at: true,
              last_login: true
            }
          }
        }
      });
    }

    return profile;
  }

  static async updateProfile(
    userId: string, 
    updates: UserProfileUpdate,
    ipAddress?: string,
    userAgent?: string
  ): Promise<any> {
    // Validate updates
    this.validateProfileUpdates(updates);

    const oldProfile = await this.getOrCreateProfile(userId);
    
    const updatedProfile = await prisma.userProfileSettings.update({
      where: { user_id: userId },
      data: {
        ...updates,
        updated_at: new Date()
      },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            role: true,
            is_2fa_enabled: true,
            created_at: true,
            updated_at: true,
            last_login: true
          }
        }
      }
    });

    // Log the profile update
    await ActivityLogger.log({
      userId,
      action: AdminAction.UPDATE_PROFILE,
      resource: 'user_profile',
      resourceId: userId,
      details: {
        changes: this.getChangedFields(oldProfile, updates)
      },
      ipAddress,
      userAgent
    });

    return updatedProfile;
  }

  private static validateProfileUpdates(updates: UserProfileUpdate): void {
    if (updates.timezone && !this.isValidTimezone(updates.timezone)) {
      throw new Error('Invalid timezone');
    }

    if (updates.date_format && !['YYYY-MM-DD', 'MM/DD/YYYY', 'DD/MM/YYYY', 'DD-MM-YYYY'].includes(updates.date_format)) {
      throw new Error('Invalid date format');
    }

    if (updates.time_format && !['12h', '24h'].includes(updates.time_format)) {
      throw new Error('Invalid time format');
    }

    if (updates.language && !this.isValidLanguage(updates.language)) {
      throw new Error('Invalid language');
    }

    if (updates.theme && !['light', 'dark', 'auto'].includes(updates.theme)) {
      throw new Error('Invalid theme');
    }

    if (updates.activity_digest && !['never', 'daily', 'weekly', 'monthly'].includes(updates.activity_digest)) {
      throw new Error('Invalid activity digest frequency');
    }

    if (updates.session_timeout && (updates.session_timeout < 30 || updates.session_timeout > 1440)) {
      throw new Error('Session timeout must be between 30 and 1440 minutes');
    }

    if (updates.display_name && (updates.display_name.length < 2 || updates.display_name.length > 50)) {
      throw new Error('Display name must be between 2 and 50 characters');
    }

    if (updates.avatar_url && !this.isValidUrl(updates.avatar_url)) {
      throw new Error('Invalid avatar URL');
    }
  }

  private static isValidTimezone(timezone: string): boolean {
    try {
      Intl.DateTimeFormat(undefined, { timeZone: timezone });
      return true;
    } catch {
      return false;
    }
  }

  private static isValidLanguage(language: string): boolean {
    const supportedLanguages = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko'];
    return supportedLanguages.includes(language);
  }

  private static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  private static getChangedFields(oldProfile: any, updates: UserProfileUpdate): any {
    const changes: any = {};
    
    Object.keys(updates).forEach(key => {
      const oldValue = oldProfile[key];
      const newValue = updates[key as keyof UserProfileUpdate];
      
      if (oldValue !== newValue) {
        changes[key] = {
          from: oldValue,
          to: newValue
        };
      }
    });

    return changes;
  }

  static async getUserSessions(userId: string): Promise<any[]> {
    const sessions = await prisma.userSession.findMany({
      where: { 
        user_id: userId,
        is_active: true,
        expires_at: {
          gt: new Date()
        }
      },
      orderBy: { last_activity: 'desc' }
    });

    return sessions;
  }

  static async revokeSession(
    sessionId: string, 
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    const session = await prisma.userSession.findFirst({
      where: {
        id: sessionId,
        user_id: userId
      }
    });

    if (!session) {
      throw new Error('Session not found');
    }

    await prisma.userSession.update({
      where: { id: sessionId },
      data: { is_active: false }
    });

    await ActivityLogger.log({
      userId,
      action: AdminAction.REVOKE_SESSION,
      resource: 'user_session',
      resourceId: sessionId,
      details: {
        session_ip: session.ip_address,
        revoked_from_ip: ipAddress
      },
      ipAddress,
      userAgent
    });
  }

  static async revokeAllOtherSessions(
    currentSessionId: string,
    userId: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<number> {
    const result = await prisma.userSession.updateMany({
      where: {
        user_id: userId,
        id: { not: currentSessionId },
        is_active: true
      },
      data: { is_active: false }
    });

    await ActivityLogger.log({
      userId,
      action: AdminAction.REVOKE_ALL_SESSIONS,
      resource: 'user_sessions',
      resourceId: userId,
      details: {
        sessions_revoked: result.count,
        kept_session: currentSessionId
      },
      ipAddress,
      userAgent
    });

    return result.count;
  }
}