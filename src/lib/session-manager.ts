import crypto from 'crypto';
import prisma from './prisma';

// Device fingerprinting interface
export interface DeviceFingerprintComponents {
    userAgent?: string;
    acceptLanguage?: string;
    acceptEncoding?: string;
    screenResolution?: string;
    timezone?: string;
    platform?: string;
    cookieEnabled?: boolean;
    doNotTrack?: string;
}

// Session management interfaces
export interface SessionData {
    sessionToken: string;
    userId: string;
    expiresAt: Date;
    ipAddress: string;
    userAgent?: string;
    deviceFingerprint?: string;
    location?: string;
    securityFlags?: Record<string, any>;
}

export interface SessionValidationResult {
    isValid: boolean;
    user?: any;
    requiresRefresh?: boolean;
    securityAlert?: string;
    session?: SessionData;
}

export interface CreateSessionOptions {
    userId: string;
    ipAddress: string;
    userAgent?: string;
    deviceFingerprint?: string;
    location?: string;
    maxIdleTime?: number; // in seconds, default 8 hours
    absoluteTimeout?: number; // in seconds, default 24 hours
    deviceFingerprintComponents?: DeviceFingerprintComponents;
}

// Session security constants
const DEFAULT_MAX_IDLE_TIME = 8 * 60 * 60; // 8 hours in seconds
const DEFAULT_ABSOLUTE_TIMEOUT = 24 * 60 * 60; // 24 hours in seconds
const MAX_CONCURRENT_SESSIONS = 3;
const SESSION_TOKEN_LENGTH = 64; // bytes for cryptographically secure token

export class SessionManager {
    /**
     * Generate cryptographically secure session token
     */
    private generateSecureSessionToken(): string {
        return crypto.randomBytes(SESSION_TOKEN_LENGTH).toString('hex');
    }

    /**
     * Generate comprehensive device fingerprint from multiple components
     */
    private generateDeviceFingerprint(
        userAgent?: string,
        ipAddress?: string,
        additionalComponents?: DeviceFingerprintComponents
    ): string {
        const components = [
            userAgent || 'unknown',
            ipAddress || 'unknown',
            additionalComponents?.acceptLanguage || 'unknown',
            additionalComponents?.acceptEncoding || 'unknown',
            additionalComponents?.screenResolution || 'unknown',
            additionalComponents?.timezone || 'unknown',
            additionalComponents?.platform || 'unknown',
            additionalComponents?.cookieEnabled?.toString() || 'unknown',
            additionalComponents?.doNotTrack || 'unknown'
        ];

        return crypto
            .createHash('sha256')
            .update(components.join('|'))
            .digest('hex')
            .substring(0, 32); // First 32 chars for storage efficiency
    }

    /**
     * Enhanced device fingerprinting with multiple data points
     */
    generateEnhancedDeviceFingerprint(
        userAgent?: string,
        ipAddress?: string,
        additionalComponents?: DeviceFingerprintComponents
    ): string {
        return this.generateDeviceFingerprint(userAgent, ipAddress, additionalComponents);
    }

    /**
     * Create a new secure session with all security features
     */
    async createSession(options: CreateSessionOptions): Promise<SessionData> {
        const {
            userId,
            ipAddress,
            userAgent,
            deviceFingerprint,
            location,
            maxIdleTime = DEFAULT_MAX_IDLE_TIME,
            absoluteTimeout = DEFAULT_ABSOLUTE_TIMEOUT
        } = options;

        // Generate secure session token
        const sessionToken = this.generateSecureSessionToken();

        // Calculate expiration times
        const now = new Date();
        const expiresAt = new Date(now.getTime() + (absoluteTimeout * 1000));

        // Generate device fingerprint if not provided
        const finalDeviceFingerprint = deviceFingerprint ||
            this.generateDeviceFingerprint(userAgent, ipAddress, options.deviceFingerprintComponents);

        // Limit concurrent sessions before creating new one
        await this.limitConcurrentSessions(userId, MAX_CONCURRENT_SESSIONS - 1);

        // Create session in database
        const session = await prisma.userSession.create({
            data: {
                user_id: userId,
                session_token: sessionToken,
                ip_address: ipAddress,
                user_agent: userAgent,
                location,
                device_fingerprint: finalDeviceFingerprint,
                is_active: true,
                last_activity: now,
                expires_at: expiresAt,
                max_idle_time: maxIdleTime,
                absolute_timeout: absoluteTimeout,
                security_flags: {
                    created_at: now.toISOString(),
                    creation_ip: ipAddress,
                    creation_user_agent: userAgent
                }
            }
        });

        return {
            sessionToken: session.session_token,
            userId: session.user_id,
            expiresAt: session.expires_at,
            ipAddress: session.ip_address,
            userAgent: session.user_agent,
            deviceFingerprint: session.device_fingerprint,
            location: session.location,
            securityFlags: session.security_flags as Record<string, any>
        };
    }

    /**
     * Validate session with comprehensive security checks
     */
    async validateSession(sessionToken: string, currentIpAddress: string): Promise<SessionValidationResult> {
        try {
            // Find session with user data
            const session = await prisma.userSession.findUnique({
                where: { session_token: sessionToken },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            first_name: true,
                            last_name: true,
                            role: true,
                            is_active: true,
                            requires_password_change: true
                        }
                    }
                }
            });

            if (!session) {
                return { isValid: false, securityAlert: 'Session not found' };
            }

            if (!session.is_active) {
                return { isValid: false, securityAlert: 'Session is inactive' };
            }

            if (!session.user.is_active) {
                // Invalidate session for inactive user
                await this.invalidateSession(sessionToken);
                return { isValid: false, securityAlert: 'User account is inactive' };
            }

            const now = new Date();

            // Check absolute timeout
            if (now > session.expires_at) {
                await this.invalidateSession(sessionToken);
                return { isValid: false, securityAlert: 'Session has expired (absolute timeout)' };
            }

            // Check idle timeout
            const idleTimeMs = now.getTime() - session.last_activity.getTime();
            const maxIdleTimeMs = session.max_idle_time * 1000;

            if (idleTimeMs > maxIdleTimeMs) {
                await this.invalidateSession(sessionToken);
                return { isValid: false, securityAlert: 'Session has expired (idle timeout)' };
            }

            // IP address consistency check
            if (session.ip_address !== currentIpAddress) {
                // Log security anomaly
                const securityFlags = session.security_flags as Record<string, any> || {};
                securityFlags.ip_change_detected = {
                    original_ip: session.ip_address,
                    new_ip: currentIpAddress,
                    detected_at: now.toISOString()
                };

                // Invalidate session due to IP change
                await prisma.userSession.update({
                    where: { id: session.id },
                    data: {
                        is_active: false,
                        security_flags: securityFlags
                    }
                });

                return {
                    isValid: false,
                    securityAlert: 'Session invalidated due to IP address change'
                };
            }

            // Check if session needs refresh (within last 10% of idle time)
            const refreshThreshold = maxIdleTimeMs * 0.9;
            const requiresRefresh = idleTimeMs > refreshThreshold;

            // Update last activity
            await prisma.userSession.update({
                where: { id: session.id },
                data: { last_activity: now }
            });

            const sessionData: SessionData = {
                sessionToken: session.session_token,
                userId: session.user_id,
                expiresAt: session.expires_at,
                ipAddress: session.ip_address,
                userAgent: session.user_agent,
                deviceFingerprint: session.device_fingerprint,
                location: session.location,
                securityFlags: session.security_flags as Record<string, any>
            };

            return {
                isValid: true,
                user: session.user,
                requiresRefresh,
                session: sessionData
            };

        } catch (error) {
            console.error('Session validation error:', error);
            return { isValid: false, securityAlert: 'Session validation failed' };
        }
    }

    /**
     * Refresh session with new expiration times
     */
    async refreshSession(sessionToken: string): Promise<SessionData | null> {
        try {
            const session = await prisma.userSession.findUnique({
                where: { session_token: sessionToken }
            });

            if (!session || !session.is_active) {
                return null;
            }

            const now = new Date();
            const newExpiresAt = new Date(now.getTime() + (session.absolute_timeout * 1000));

            const updatedSession = await prisma.userSession.update({
                where: { id: session.id },
                data: {
                    expires_at: newExpiresAt,
                    last_activity: now
                }
            });

            return {
                sessionToken: updatedSession.session_token,
                userId: updatedSession.user_id,
                expiresAt: updatedSession.expires_at,
                ipAddress: updatedSession.ip_address,
                userAgent: updatedSession.user_agent,
                deviceFingerprint: updatedSession.device_fingerprint,
                location: updatedSession.location,
                securityFlags: updatedSession.security_flags as Record<string, any>
            };

        } catch (error) {
            console.error('Session refresh error:', error);
            return null;
        }
    }

    /**
     * Invalidate a specific session
     */
    async invalidateSession(sessionToken: string): Promise<void> {
        await prisma.userSession.updateMany({
            where: { session_token: sessionToken },
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: new Date().toISOString(),
                    invalidation_reason: 'manual_invalidation'
                }
            }
        });
    }

    /**
     * Invalidate all sessions for a user except optionally one
     */
    async invalidateUserSessions(userId: string, excludeToken?: string): Promise<number> {
        const whereClause: any = {
            user_id: userId,
            is_active: true
        };

        if (excludeToken) {
            whereClause.session_token = {
                not: excludeToken
            };
        }

        const result = await prisma.userSession.updateMany({
            where: whereClause,
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: new Date().toISOString(),
                    invalidation_reason: 'user_session_cleanup'
                }
            }
        });

        return result.count;
    }

    /**
     * Limit concurrent sessions per user
     */
    async limitConcurrentSessions(userId: string, maxSessions: number): Promise<void> {
        // Get all active sessions for user, ordered by last activity (oldest first)
        const activeSessions = await prisma.userSession.findMany({
            where: {
                user_id: userId,
                is_active: true
            },
            orderBy: { last_activity: 'asc' }
        });

        // If we have too many sessions, invalidate the oldest ones
        if (activeSessions.length >= maxSessions) {
            const sessionsToInvalidate = activeSessions.slice(0, activeSessions.length - maxSessions + 1);

            const sessionIds = sessionsToInvalidate.map((s: any) => s.id);

            await prisma.userSession.updateMany({
                where: {
                    id: { in: sessionIds }
                },
                data: {
                    is_active: false,
                    security_flags: {
                        invalidated_at: new Date().toISOString(),
                        invalidation_reason: 'concurrent_session_limit_exceeded'
                    }
                }
            });
        }
    }

    /**
     * Clean up expired sessions (maintenance function)
     */
    async cleanupExpiredSessions(): Promise<number> {
        const now = new Date();

        // Find sessions that are expired by absolute timeout or idle timeout
        const expiredSessions = await prisma.userSession.findMany({
            where: {
                OR: [
                    // Absolute timeout expired
                    { expires_at: { lte: now } },
                    // Idle timeout expired
                    {
                        last_activity: {
                            lte: new Date(now.getTime() - (DEFAULT_MAX_IDLE_TIME * 1000))
                        }
                    }
                ],
                is_active: true
            }
        });

        if (expiredSessions.length === 0) {
            return 0;
        }

        const sessionIds = expiredSessions.map((s: any) => s.id);

        const result = await prisma.userSession.updateMany({
            where: {
                id: { in: sessionIds }
            },
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: now.toISOString(),
                    invalidation_reason: 'automatic_cleanup_expired'
                }
            }
        });

        return result.count;
    }

    /**
     * Get active sessions for a user (for security monitoring)
     */
    async getUserActiveSessions(userId: string): Promise<SessionData[]> {
        const sessions = await prisma.userSession.findMany({
            where: {
                user_id: userId,
                is_active: true
            },
            orderBy: { last_activity: 'desc' }
        });

        return sessions.map((session: any) => ({
            sessionToken: session.session_token,
            userId: session.user_id,
            expiresAt: session.expires_at,
            ipAddress: session.ip_address,
            userAgent: session.user_agent,
            deviceFingerprint: session.device_fingerprint,
            location: session.location,
            securityFlags: session.security_flags as Record<string, any>
        }));
    }

    /**
     * Invalidate sessions for security events (password change, role change, etc.)
     */
    async invalidateSessionsForSecurityEvent(userId: string, reason: string, excludeToken?: string): Promise<number> {
        const whereClause: any = {
            user_id: userId,
            is_active: true
        };

        if (excludeToken) {
            whereClause.session_token = {
                not: excludeToken
            };
        }

        const result = await prisma.userSession.updateMany({
            where: whereClause,
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: new Date().toISOString(),
                    invalidation_reason: `security_event_${reason}`
                }
            }
        });

        return result.count;
    }

    /**
     * Detect session anomalies (for monitoring)
     */
    async detectSessionAnomalies(userId: string): Promise<any[]> {
        const sessions = await prisma.userSession.findMany({
            where: {
                user_id: userId,
                is_active: true
            },
            orderBy: { created_at: 'desc' }
        });

        const anomalies: any[] = [];

        // Check for multiple IP addresses
        const uniqueIPs = new Set(sessions.map((s: any) => s.ip_address));
        if (uniqueIPs.size > 1) {
            anomalies.push({
                type: 'multiple_ip_addresses',
                details: {
                    ip_addresses: Array.from(uniqueIPs),
                    session_count: sessions.length
                }
            });
        }

        // Check for suspicious user agents
        const userAgents = sessions.map((s: any) => s.user_agent).filter(Boolean);
        const uniqueUserAgents = new Set(userAgents);
        if (uniqueUserAgents.size > 2) { // Allow for some variation
            anomalies.push({
                type: 'multiple_user_agents',
                details: {
                    user_agents: Array.from(uniqueUserAgents),
                    count: uniqueUserAgents.size
                }
            });
        }

        // Check for rapid session creation
        if (sessions.length >= MAX_CONCURRENT_SESSIONS) {
            const recentSessions = sessions.filter((s: any) => {
                const ageMs = Date.now() - s.created_at.getTime();
                return ageMs < (60 * 60 * 1000); // Within last hour
            });

            if (recentSessions.length >= 2) {
                anomalies.push({
                    type: 'rapid_session_creation',
                    details: {
                        recent_sessions: recentSessions.length,
                        total_sessions: sessions.length
                    }
                });
            }
        }

        return anomalies;
    }

    /**
     * Enhanced IP address change detection with automatic session invalidation
     */
    async detectAndHandleIpChanges(sessionToken: string, newIpAddress: string): Promise<{
        ipChanged: boolean;
        sessionInvalidated: boolean;
        previousIp?: string;
    }> {
        const session = await prisma.userSession.findUnique({
            where: { session_token: sessionToken }
        });

        if (!session || !session.is_active) {
            return { ipChanged: false, sessionInvalidated: false };
        }

        if (session.ip_address !== newIpAddress) {
            // Log the IP change in security flags
            const securityFlags = session.security_flags as Record<string, any> || {};
            securityFlags.ip_change_detected = {
                original_ip: session.ip_address,
                new_ip: newIpAddress,
                detected_at: new Date().toISOString(),
                action_taken: 'session_invalidated'
            };

            // Invalidate the session
            await prisma.userSession.update({
                where: { id: session.id },
                data: {
                    is_active: false,
                    security_flags: securityFlags
                }
            });

            return {
                ipChanged: true,
                sessionInvalidated: true,
                previousIp: session.ip_address
            };
        }

        return { ipChanged: false, sessionInvalidated: false };
    }

    /**
     * Automatic session cleanup with configurable intervals
     */
    async performAutomaticSessionCleanup(): Promise<{
        expiredSessions: number;
        idleTimeoutSessions: number;
        totalCleaned: number;
    }> {
        const now = new Date();
        let expiredCount = 0;
        let idleTimeoutCount = 0;

        // Clean up sessions that exceeded absolute timeout
        const absoluteTimeoutResult = await prisma.userSession.updateMany({
            where: {
                expires_at: { lte: now },
                is_active: true
            },
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: now.toISOString(),
                    invalidation_reason: 'absolute_timeout_expired'
                }
            }
        });
        expiredCount = absoluteTimeoutResult.count;

        // Clean up sessions that exceeded idle timeout
        const idleTimeoutThreshold = new Date(now.getTime() - (DEFAULT_MAX_IDLE_TIME * 1000));
        const idleTimeoutResult = await prisma.userSession.updateMany({
            where: {
                last_activity: { lte: idleTimeoutThreshold },
                is_active: true,
                expires_at: { gt: now } // Not already expired by absolute timeout
            },
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: now.toISOString(),
                    invalidation_reason: 'idle_timeout_expired'
                }
            }
        });
        idleTimeoutCount = idleTimeoutResult.count;

        return {
            expiredSessions: expiredCount,
            idleTimeoutSessions: idleTimeoutCount,
            totalCleaned: expiredCount + idleTimeoutCount
        };
    }

    /**
     * Session invalidation for specific security events
     */
    async invalidateSessionsForSecurityEvents(
        userId: string,
        securityEvent: 'password_change' | 'role_change' | 'account_compromise' | 'admin_action',
        excludeCurrentSession?: string
    ): Promise<{
        invalidatedCount: number;
        securityEvent: string;
    }> {
        const whereClause: any = {
            user_id: userId,
            is_active: true
        };

        if (excludeCurrentSession) {
            whereClause.session_token = {
                not: excludeCurrentSession
            };
        }

        const result = await prisma.userSession.updateMany({
            where: whereClause,
            data: {
                is_active: false,
                security_flags: {
                    invalidated_at: new Date().toISOString(),
                    invalidation_reason: `security_event_${securityEvent}`,
                    security_event: securityEvent
                }
            }
        });

        return {
            invalidatedCount: result.count,
            securityEvent
        };
    }

    /**
     * Enhanced session monitoring with detailed security metrics
     */
    async getSessionSecurityMetrics(userId?: string): Promise<{
        totalActiveSessions: number;
        sessionsWithAnomalies: number;
        multipleIpSessions: number;
        recentlyCreatedSessions: number;
        suspiciousActivityCount: number;
        userSpecificMetrics?: {
            userId: string;
            activeSessions: number;
            anomalies: any[];
        };
    }> {
        const whereClause: any = { is_active: true };
        if (userId) {
            whereClause.user_id = userId;
        }

        const activeSessions = await prisma.userSession.findMany({
            where: whereClause,
            orderBy: { created_at: 'desc' }
        });

        const totalActiveSessions = activeSessions.length;
        let sessionsWithAnomalies = 0;
        let multipleIpSessions = 0;
        let recentlyCreatedSessions = 0;
        let suspiciousActivityCount = 0;

        // Group sessions by user to detect anomalies
        const sessionsByUser = new Map<string, any[]>();
        activeSessions.forEach((session: any) => {
            if (!sessionsByUser.has(session.user_id)) {
                sessionsByUser.set(session.user_id, []);
            }
            sessionsByUser.get(session.user_id)!.push(session);
        });

        // Analyze each user's sessions
        for (const [, userSessions] of sessionsByUser) {
            const uniqueIPs = new Set(userSessions.map((s: any) => s.ip_address));
            if (uniqueIPs.size > 1) {
                multipleIpSessions += userSessions.length;
                sessionsWithAnomalies++;
            }

            // Check for recently created sessions (within last hour)
            const recentSessions = userSessions.filter((s: any) => {
                const ageMs = Date.now() - s.created_at.getTime();
                return ageMs < (60 * 60 * 1000);
            });
            recentlyCreatedSessions += recentSessions.length;

            // Check for suspicious activity patterns
            if (userSessions.length >= MAX_CONCURRENT_SESSIONS && recentSessions.length >= 2) {
                suspiciousActivityCount++;
            }
        }

        const metrics: any = {
            totalActiveSessions,
            sessionsWithAnomalies,
            multipleIpSessions,
            recentlyCreatedSessions,
            suspiciousActivityCount
        };

        // Add user-specific metrics if requested
        if (userId) {
            const userAnomalies = await this.detectSessionAnomalies(userId);
            metrics.userSpecificMetrics = {
                userId,
                activeSessions: activeSessions.filter((s: any) => s.user_id === userId).length,
                anomalies: userAnomalies
            };
        }

        return metrics;
    }

    /**
     * Device fingerprint validation for enhanced security
     */
    async validateDeviceFingerprint(
        sessionToken: string,
        currentFingerprint: string
    ): Promise<{
        isValid: boolean;
        fingerprintChanged: boolean;
        action: 'allow' | 'challenge' | 'deny';
    }> {
        const session = await prisma.userSession.findUnique({
            where: { session_token: sessionToken }
        });

        if (!session || !session.is_active) {
            return { isValid: false, fingerprintChanged: false, action: 'deny' };
        }

        const storedFingerprint = session.device_fingerprint;

        if (!storedFingerprint) {
            // No stored fingerprint, update with current one
            await prisma.userSession.update({
                where: { id: session.id },
                data: { device_fingerprint: currentFingerprint }
            });
            return { isValid: true, fingerprintChanged: false, action: 'allow' };
        }

        if (storedFingerprint === currentFingerprint) {
            return { isValid: true, fingerprintChanged: false, action: 'allow' };
        }

        // Fingerprint has changed - this could indicate device change or session hijacking
        const securityFlags = session.security_flags as Record<string, any> || {};
        securityFlags.fingerprint_change_detected = {
            original_fingerprint: storedFingerprint,
            new_fingerprint: currentFingerprint,
            detected_at: new Date().toISOString()
        };

        await prisma.userSession.update({
            where: { id: session.id },
            data: { security_flags: securityFlags }
        });

        // For now, we'll challenge but not deny - this can be configured based on security policy
        return { isValid: false, fingerprintChanged: true, action: 'challenge' };
    }
}

// Export singleton instance
export const sessionManager = new SessionManager();

// Export utility functions for backward compatibility
export async function createSession(userId: string, ipAddress: string, userAgent?: string): Promise<SessionData> {
    return sessionManager.createSession({
        userId,
        ipAddress,
        userAgent
    });
}

export async function validateSession(sessionToken: string, ipAddress: string = 'unknown'): Promise<SessionValidationResult> {
    return sessionManager.validateSession(sessionToken, ipAddress);
}

export async function invalidateSession(sessionToken: string): Promise<void> {
    return sessionManager.invalidateSession(sessionToken);
}

export async function cleanupExpiredSessions(): Promise<number> {
    return sessionManager.cleanupExpiredSessions();
}