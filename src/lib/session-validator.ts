import prisma from './prisma';
import { Logger } from './logger';
import { SecurityLogger, SecurityEventType } from './security-logger';

export interface SessionValidationOptions {
    checkIpAddress?: boolean;
    allowIpChange?: boolean;
    checkDeviceFingerprint?: boolean;
    allowDeviceFingerprintChange?: boolean;
    maxIdleTimeOverride?: number; // in seconds
    enableSecurityLogging?: boolean;
}

export interface SessionValidationResult {
    isValid: boolean;
    user?: {
        id: string;
        email: string;
        first_name: string;
        last_name: string;
        role: string;
        is_active: boolean;
        requires_password_change: boolean;
    };
    session?: {
        sessionToken: string;
        userId: string;
        expiresAt: Date;
        ipAddress: string;
        userAgent?: string;
        deviceFingerprint?: string;
        location?: string;
        lastActivity: Date;
        createdAt: Date;
        securityFlags?: Record<string, any>;
    };
    requiresRefresh?: boolean;
    securityAlert?: string;
    validationErrors?: string[];
    securityEvents?: SecurityEvent[];
}

export interface SecurityEvent {
    type: 'ip_change' | 'device_change' | 'suspicious_activity' | 'session_anomaly';
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    details: Record<string, any>;
    timestamp: Date;
    action: 'logged' | 'session_invalidated' | 'user_notified' | 'admin_alerted';
}

export interface SessionRenewalOptions {
    extendIdleTime?: boolean;
    extendAbsoluteTime?: boolean;
    updateLastActivity?: boolean;
    logRenewal?: boolean;
}

export interface ActivityTrackingData {
    endpoint?: string;
    method?: string;
    userAgent?: string;
    ipAddress?: string;
    requestId?: string;
    metadata?: Record<string, any>;
}

/**
 * Comprehensive session validator with security monitoring and activity tracking
 */
export class SessionValidator {
    private defaultOptions: Required<SessionValidationOptions> = {
        checkIpAddress: true,
        allowIpChange: false,
        checkDeviceFingerprint: true,
        allowDeviceFingerprintChange: true,
        maxIdleTimeOverride: 0, // 0 means use session's own max_idle_time
        enableSecurityLogging: true
    };

    constructor(private options: SessionValidationOptions = {}) {
        this.options = { ...this.defaultOptions, ...options };
    }

    /**
     * Validate session with comprehensive security checks
     */
    async validateSession(
        sessionToken: string,
        currentIpAddress: string,
        currentDeviceFingerprint?: string,
        activityData?: ActivityTrackingData
    ): Promise<SessionValidationResult> {
        const validationErrors: string[] = [];
        const securityEvents: SecurityEvent[] = [];
        let requiresRefresh = false;

        try {
            // Input validation
            if (!sessionToken || typeof sessionToken !== 'string') {
                return {
                    isValid: false,
                    securityAlert: 'Invalid session token format',
                    validationErrors: ['Session token is required and must be a string']
                };
            }

            if (!currentIpAddress || typeof currentIpAddress !== 'string') {
                return {
                    isValid: false,
                    securityAlert: 'Invalid IP address format',
                    validationErrors: ['IP address is required and must be a string']
                };
            }

            // Find session with user data
            const session = await prisma.userSession.findUnique({
                where: { session_token: sessionToken },
                include: {
                    user: {
                        select: {
                            id: true,
                            email: true,
                            first_name: true,
                            last_name: true,
                            role: true,
                            is_active: true,
                            requires_password_change: true,
                            last_login_ip: true,
                            failed_login_attempts: true,
                            locked_until: true
                        }
                    }
                }
            });

            if (!session) {
                await this.logSecurityEvent({
                    type: 'session_anomaly',
                    severity: 'medium',
                    description: 'Session not found in database',
                    details: {
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        ipAddress: currentIpAddress,
                        userAgent: activityData?.userAgent
                    },
                    timestamp: new Date(),
                    action: 'logged'
                });

                return {
                    isValid: false,
                    securityAlert: 'Session not found',
                    validationErrors: ['Session does not exist or has been removed']
                };
            }

            // Check if session is active
            if (!session.is_active) {
                await this.logSecurityEvent({
                    type: 'session_anomaly',
                    severity: 'low',
                    description: 'Attempt to use inactive session',
                    details: {
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        userId: session.user_id,
                        ipAddress: currentIpAddress
                    },
                    timestamp: new Date(),
                    action: 'logged'
                });

                return {
                    isValid: false,
                    securityAlert: 'Session is inactive',
                    validationErrors: ['Session has been deactivated']
                };
            }

            // Check if user account is active
            if (!session.user.is_active) {
                // Invalidate session for inactive user
                await this.invalidateSession(sessionToken, 'user_account_inactive');

                await this.logSecurityEvent({
                    type: 'session_anomaly',
                    severity: 'high',
                    description: 'Session used by inactive user account',
                    details: {
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        userId: session.user.id,
                        email: session.user.email,
                        ipAddress: currentIpAddress
                    },
                    timestamp: new Date(),
                    action: 'session_invalidated'
                });

                return {
                    isValid: false,
                    securityAlert: 'User account is inactive',
                    validationErrors: ['User account has been deactivated']
                };
            }

            // Check if user account is locked
            if (session.user.locked_until && session.user.locked_until > new Date()) {
                await this.invalidateSession(sessionToken, 'user_account_locked');

                await this.logSecurityEvent({
                    type: 'session_anomaly',
                    severity: 'high',
                    description: 'Session used by locked user account',
                    details: {
                        sessionToken: sessionToken.substring(0, 8) + '...',
                        userId: session.user.id,
                        email: session.user.email,
                        lockedUntil: session.user.locked_until,
                        ipAddress: currentIpAddress
                    },
                    timestamp: new Date(),
                    action: 'session_invalidated'
                });

                return {
                    isValid: false,
                    securityAlert: 'User account is locked',
                    validationErrors: ['User account is temporarily locked']
                };
            }

            const now = new Date();

            // Check absolute timeout
            if (now > session.expires_at) {
                await this.invalidateSession(sessionToken, 'absolute_timeout_expired');

                return {
                    isValid: false,
                    securityAlert: 'Session has expired (absolute timeout)',
                    validationErrors: ['Session has exceeded maximum allowed time']
                };
            }

            // Check idle timeout
            const maxIdleTime = this.options.maxIdleTimeOverride || session.max_idle_time;
            const idleTimeMs = now.getTime() - session.last_activity.getTime();
            const maxIdleTimeMs = maxIdleTime * 1000;

            if (idleTimeMs > maxIdleTimeMs) {
                await this.invalidateSession(sessionToken, 'idle_timeout_expired');

                return {
                    isValid: false,
                    securityAlert: 'Session has expired (idle timeout)',
                    validationErrors: ['Session has been idle for too long']
                };
            }

            // IP address validation
            if (this.options.checkIpAddress && session.ip_address !== currentIpAddress) {
                const securityEvent: SecurityEvent = {
                    type: 'ip_change',
                    severity: 'high',
                    description: 'IP address change detected during session',
                    details: {
                        originalIp: session.ip_address,
                        newIp: currentIpAddress,
                        userId: session.user.id,
                        email: session.user.email,
                        sessionAge: now.getTime() - session.created_at.getTime()
                    },
                    timestamp: now,
                    action: this.options.allowIpChange ? 'logged' : 'session_invalidated'
                };

                securityEvents.push(securityEvent);
                await this.logSecurityEvent(securityEvent);

                if (!this.options.allowIpChange) {
                    // Update session with IP change detection
                    const securityFlags = session.security_flags as Record<string, any> || {};
                    securityFlags.ip_change_detected = {
                        original_ip: session.ip_address,
                        new_ip: currentIpAddress,
                        detected_at: now.toISOString(),
                        action_taken: 'session_invalidated'
                    };

                    await prisma.userSession.update({
                        where: { id: session.id },
                        data: {
                            is_active: false,
                            security_flags: securityFlags
                        }
                    });

                    return {
                        isValid: false,
                        securityAlert: 'Session invalidated due to IP address change',
                        validationErrors: ['IP address change detected'],
                        securityEvents
                    };
                } else {
                    // Log IP change but allow session to continue
                    const securityFlags = session.security_flags as Record<string, any> || {};
                    securityFlags.ip_change_logged = {
                        original_ip: session.ip_address,
                        new_ip: currentIpAddress,
                        detected_at: now.toISOString(),
                        action_taken: 'logged_and_allowed'
                    };

                    await prisma.userSession.update({
                        where: { id: session.id },
                        data: { security_flags: securityFlags }
                    });

                    validationErrors.push('IP address change detected but allowed');
                }
            }

            // Device fingerprint validation
            if (this.options.checkDeviceFingerprint && currentDeviceFingerprint && session.device_fingerprint) {
                if (session.device_fingerprint !== currentDeviceFingerprint) {
                    const securityEvent: SecurityEvent = {
                        type: 'device_change',
                        severity: 'medium',
                        description: 'Device fingerprint change detected',
                        details: {
                            originalFingerprint: session.device_fingerprint.substring(0, 16) + '...',
                            newFingerprint: currentDeviceFingerprint.substring(0, 16) + '...',
                            userId: session.user.id,
                            email: session.user.email
                        },
                        timestamp: now,
                        action: this.options.allowDeviceFingerprintChange ? 'logged' : 'session_invalidated'
                    };

                    securityEvents.push(securityEvent);
                    await this.logSecurityEvent(securityEvent);

                    if (!this.options.allowDeviceFingerprintChange) {
                        await this.invalidateSession(sessionToken, 'device_fingerprint_change');

                        return {
                            isValid: false,
                            securityAlert: 'Session invalidated due to device change',
                            validationErrors: ['Device fingerprint change detected'],
                            securityEvents
                        };
                    } else {
                        validationErrors.push('Device fingerprint change detected but allowed');
                    }
                }
            }

            // Check if session needs refresh (within last 10% of idle time)
            const refreshThreshold = maxIdleTimeMs * 0.9;
            requiresRefresh = idleTimeMs > refreshThreshold;

            // Detect suspicious activity patterns
            const suspiciousActivity = await this.detectSuspiciousActivity(session.user.id, currentIpAddress, activityData);
            if (suspiciousActivity.length > 0) {
                securityEvents.push(...suspiciousActivity);
                for (const event of suspiciousActivity) {
                    await this.logSecurityEvent(event);
                }
            }

            // Update session activity
            await this.updateSessionActivity(sessionToken, currentIpAddress, activityData);

            // Prepare session data for response
            const sessionData = {
                sessionToken: session.session_token,
                userId: session.user_id,
                expiresAt: session.expires_at,
                ipAddress: session.ip_address,
                userAgent: session.user_agent || undefined,
                deviceFingerprint: session.device_fingerprint || undefined,
                location: session.location || undefined,
                lastActivity: session.last_activity,
                createdAt: session.created_at,
                securityFlags: session.security_flags as Record<string, any> || undefined
            };

            return {
                isValid: true,
                user: session.user,
                session: sessionData,
                requiresRefresh,
                validationErrors: validationErrors.length > 0 ? validationErrors : undefined,
                securityEvents: securityEvents.length > 0 ? securityEvents : undefined
            };

        } catch (error) {
            Logger.error('Session validation error', error, {
                sessionToken: sessionToken.substring(0, 8) + '...',
                ipAddress: currentIpAddress
            });

            await this.logSecurityEvent({
                type: 'session_anomaly',
                severity: 'critical',
                description: 'System error during session validation',
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    sessionToken: sessionToken.substring(0, 8) + '...',
                    ipAddress: currentIpAddress
                },
                timestamp: new Date(),
                action: 'logged'
            });

            return {
                isValid: false,
                securityAlert: 'Session validation failed due to system error',
                validationErrors: ['System error during validation']
            };
        }
    }

    /**
     * Renew session with updated expiration times
     */
    async renewSession(
        sessionToken: string,
        options: SessionRenewalOptions = {}
    ): Promise<{ success: boolean; newExpiresAt?: Date; error?: string }> {
        try {
            const session = await prisma.userSession.findUnique({
                where: { session_token: sessionToken }
            });

            if (!session || !session.is_active) {
                return { success: false, error: 'Session not found or inactive' };
            }

            const now = new Date();
            const updateData: any = {};

            if (options.updateLastActivity !== false) {
                updateData.last_activity = now;
            }

            if (options.extendAbsoluteTime !== false) {
                const newExpiresAt = new Date(now.getTime() + (session.absolute_timeout * 1000));
                updateData.expires_at = newExpiresAt;
            }

            const updatedSession = await prisma.userSession.update({
                where: { id: session.id },
                data: updateData
            });

            if (options.logRenewal !== false) {
                Logger.info('Session renewed', {
                    sessionToken: sessionToken.substring(0, 8) + '...',
                    userId: session.user_id,
                    newExpiresAt: updatedSession.expires_at
                });
            }

            return {
                success: true,
                newExpiresAt: updatedSession.expires_at
            };

        } catch (error) {
            Logger.error('Session renewal error', error, {
                sessionToken: sessionToken.substring(0, 8) + '...'
            });

            return {
                success: false,
                error: 'Failed to renew session'
            };
        }
    }

    /**
     * Update session activity with tracking data
     */
    async updateSessionActivity(
        sessionToken: string,
        ipAddress: string,
        activityData?: ActivityTrackingData
    ): Promise<void> {
        try {
            const now = new Date();
            const updateData: any = {
                last_activity: now
            };

            // Update security flags with activity data if provided
            if (activityData) {
                const session = await prisma.userSession.findUnique({
                    where: { session_token: sessionToken },
                    select: { security_flags: true }
                });

                if (session) {
                    const securityFlags = session.security_flags as Record<string, any> || {};

                    if (!securityFlags.activity_log) {
                        securityFlags.activity_log = [];
                    }

                    // Keep only last 10 activities to prevent excessive data growth
                    if (securityFlags.activity_log.length >= 10) {
                        securityFlags.activity_log = securityFlags.activity_log.slice(-9);
                    }

                    securityFlags.activity_log.push({
                        timestamp: now.toISOString(),
                        endpoint: activityData.endpoint,
                        method: activityData.method,
                        userAgent: activityData.userAgent,
                        ipAddress: ipAddress,
                        requestId: activityData.requestId,
                        metadata: activityData.metadata
                    });

                    updateData.security_flags = securityFlags;
                }
            }

            await prisma.userSession.updateMany({
                where: { session_token: sessionToken },
                data: updateData
            });

        } catch (error) {
            Logger.error('Failed to update session activity', error, {
                sessionToken: sessionToken.substring(0, 8) + '...'
            });
        }
    }

    /**
     * Invalidate session with reason logging
     */
    async invalidateSession(sessionToken: string, reason: string): Promise<void> {
        try {
            const securityFlags = {
                invalidated_at: new Date().toISOString(),
                invalidation_reason: reason
            };

            await prisma.userSession.updateMany({
                where: { session_token: sessionToken },
                data: {
                    is_active: false,
                    security_flags: securityFlags
                }
            });

            Logger.info('Session invalidated', {
                sessionToken: sessionToken.substring(0, 8) + '...',
                reason
            });

        } catch (error) {
            Logger.error('Failed to invalidate session', error, {
                sessionToken: sessionToken.substring(0, 8) + '...',
                reason
            });
        }
    }

    /**
     * Detect suspicious activity patterns
     */
    private async detectSuspiciousActivity(
        userId: string,
        currentIpAddress: string,
        activityData?: ActivityTrackingData
    ): Promise<SecurityEvent[]> {
        const events: SecurityEvent[] = [];

        try {
            // Get recent sessions for this user
            const recentSessions = await prisma.userSession.findMany({
                where: {
                    user_id: userId,
                    is_active: true,
                    created_at: {
                        gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
                    }
                },
                orderBy: { created_at: 'desc' }
            });

            // Check for multiple concurrent sessions from different IPs
            const uniqueIPs = new Set(recentSessions.map(s => s.ip_address));
            if (uniqueIPs.size > 3) {
                events.push({
                    type: 'suspicious_activity',
                    severity: 'high',
                    description: 'Multiple concurrent sessions from different IP addresses',
                    details: {
                        userId,
                        uniqueIpCount: uniqueIPs.size,
                        ipAddresses: Array.from(uniqueIPs),
                        sessionCount: recentSessions.length
                    },
                    timestamp: new Date(),
                    action: 'logged'
                });
            }

            // Check for rapid session creation
            const recentSessionsLastHour = recentSessions.filter(s => {
                const ageMs = Date.now() - s.created_at.getTime();
                return ageMs < (60 * 60 * 1000); // Within last hour
            });

            if (recentSessionsLastHour.length >= 5) {
                events.push({
                    type: 'suspicious_activity',
                    severity: 'medium',
                    description: 'Rapid session creation detected',
                    details: {
                        userId,
                        sessionsLastHour: recentSessionsLastHour.length,
                        currentIp: currentIpAddress
                    },
                    timestamp: new Date(),
                    action: 'logged'
                });
            }

            // Check for unusual user agent patterns
            if (activityData?.userAgent) {
                const userAgents = recentSessions
                    .map(s => s.user_agent)
                    .filter(Boolean);

                const uniqueUserAgents = new Set(userAgents);
                if (uniqueUserAgents.size > 3) {
                    events.push({
                        type: 'suspicious_activity',
                        severity: 'low',
                        description: 'Multiple different user agents detected',
                        details: {
                            userId,
                            uniqueUserAgentCount: uniqueUserAgents.size,
                            currentUserAgent: activityData.userAgent.substring(0, 100) + '...'
                        },
                        timestamp: new Date(),
                        action: 'logged'
                    });
                }
            }

        } catch (error) {
            Logger.error('Error detecting suspicious activity', error, { userId });
        }

        return events;
    }

    /**
     * Log security events to the security audit system
     */
    private async logSecurityEvent(event: SecurityEvent): Promise<void> {
        if (!this.options.enableSecurityLogging) {
            return;
        }

        try {
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                userId: event.details.userId,
                email: event.details.email,
                ipAddress: event.details.ipAddress || event.details.newIp || event.details.currentIp,
                userAgent: event.details.userAgent || event.details.currentUserAgent,
                success: event.action !== 'session_invalidated',
                errorMessage: event.action === 'session_invalidated' ? event.description : undefined,
                details: {
                    securityEventType: event.type,
                    severity: event.severity,
                    description: event.description,
                    action: event.action,
                    timestamp: event.timestamp,
                    ...event.details
                }
            });

        } catch (error) {
            Logger.error('Failed to log security event', error, { event });
        }
    }

    /**
     * Get session validation statistics for monitoring
     */
    async getValidationStatistics(timeframe: 'hour' | 'day' | 'week' = 'day'): Promise<{
        totalValidations: number;
        successfulValidations: number;
        failedValidations: number;
        securityEvents: number;
        ipChangeEvents: number;
        deviceChangeEvents: number;
        suspiciousActivityEvents: number;
    }> {
        try {
            const timeframeMs = {
                hour: 60 * 60 * 1000,
                day: 24 * 60 * 60 * 1000,
                week: 7 * 24 * 60 * 60 * 1000
            }[timeframe];

            const since = new Date(Date.now() - timeframeMs);

            // Get security audit logs for session-related events
            const securityLogs = await prisma.securityAuditLog.findMany({
                where: {
                    created_at: { gte: since },
                    event_type: {
                        in: ['SESSION_INVALID', 'SECURITY_ALERT_TRIGGERED', 'AUTHENTICATION_SUCCESS', 'AUTHENTICATION_FAILED']
                    }
                }
            });

            const totalValidations = securityLogs.length;
            const successfulValidations = securityLogs.filter(log => log.success).length;
            const failedValidations = totalValidations - successfulValidations;

            // Count specific security events
            const securityEventLogs = securityLogs.filter(log =>
                log.event_type === 'SECURITY_ALERT_TRIGGERED'
            );

            const ipChangeEvents = securityEventLogs.filter(log =>
                log.details && typeof log.details === 'object' &&
                'securityEventType' in log.details &&
                log.details.securityEventType === 'ip_change'
            ).length;

            const deviceChangeEvents = securityEventLogs.filter(log =>
                log.details && typeof log.details === 'object' &&
                'securityEventType' in log.details &&
                log.details.securityEventType === 'device_change'
            ).length;

            const suspiciousActivityEvents = securityEventLogs.filter(log =>
                log.details && typeof log.details === 'object' &&
                'securityEventType' in log.details &&
                log.details.securityEventType === 'suspicious_activity'
            ).length;

            return {
                totalValidations,
                successfulValidations,
                failedValidations,
                securityEvents: securityEventLogs.length,
                ipChangeEvents,
                deviceChangeEvents,
                suspiciousActivityEvents
            };

        } catch (error) {
            Logger.error('Failed to get validation statistics', error);
            return {
                totalValidations: 0,
                successfulValidations: 0,
                failedValidations: 0,
                securityEvents: 0,
                ipChangeEvents: 0,
                deviceChangeEvents: 0,
                suspiciousActivityEvents: 0
            };
        }
    }
}

// Export singleton instance with default options
export const sessionValidator = new SessionValidator();

// Export utility functions for backward compatibility
export async function validateSession(
    sessionToken: string,
    currentIpAddress: string,
    currentDeviceFingerprint?: string,
    activityData?: ActivityTrackingData
): Promise<SessionValidationResult> {
    return sessionValidator.validateSession(sessionToken, currentIpAddress, currentDeviceFingerprint, activityData);
}

export async function renewSession(
    sessionToken: string,
    options?: SessionRenewalOptions
): Promise<{ success: boolean; newExpiresAt?: Date; error?: string }> {
    return sessionValidator.renewSession(sessionToken, options);
}

export async function invalidateSession(sessionToken: string, reason: string): Promise<void> {
    return sessionValidator.invalidateSession(sessionToken, reason);
}