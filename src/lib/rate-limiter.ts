import { prisma } from './prisma';
import { SecurityLogger, SecurityEventType } from './security-logger';

export interface RateLimitResult {
    allowed: boolean;
    remaining: number;
    resetTime: Date;
    retryAfter?: number;
}

export interface RateLimitConfig {
    windowMs: number; // Time window in milliseconds
    maxRequests: number; // Maximum requests per window
    keyGenerator?: (identifier: string) => string; // Custom key generation
    skipSuccessfulRequests?: boolean; // Don't count successful requests
    skipFailedRequests?: boolean; // Don't count failed requests
}

export class RateLimiter {
    constructor() {
        // SecurityLogger uses static methods, no need to instantiate
    }

    /**
     * Check if a request should be rate limited using sliding window algorithm
     */
    async checkRateLimit(
        identifier: string,
        config: RateLimitConfig
    ): Promise<RateLimitResult> {
        const key = config.keyGenerator ? config.keyGenerator(identifier) : identifier;
        const now = new Date();
        const windowStart = new Date(now.getTime() - config.windowMs);

        try {
            // Clean up expired entries first
            await this.cleanupExpiredEntries();

            // Get current request count for this identifier within the window
            const currentCount = await this.getCurrentRequestCount(key, windowStart);

            if (currentCount >= config.maxRequests) {
                // Rate limit exceeded
                const oldestEntry = await this.getOldestEntry(key);
                const resetTime = oldestEntry
                    ? new Date(oldestEntry.window_start.getTime() + config.windowMs)
                    : new Date(now.getTime() + config.windowMs);

                const retryAfter = Math.ceil((resetTime.getTime() - now.getTime()) / 1000);

                // Log rate limit violation
                await SecurityLogger.logSecurityViolation({
                    eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
                    ipAddress: identifier.startsWith('ip:') ? identifier.substring(3) : 'unknown',
                    userAgent: undefined,
                    success: false,
                    details: {
                        violationType: 'rate_limit',
                        requestCount: currentCount,
                        timeWindow: `${config.windowMs}ms`,
                        endpoint: key
                    }
                });

                return {
                    allowed: false,
                    remaining: 0,
                    resetTime,
                    retryAfter
                };
            }

            // Request is allowed
            const remaining = config.maxRequests - currentCount - 1;
            const resetTime = new Date(now.getTime() + config.windowMs);

            return {
                allowed: true,
                remaining: Math.max(0, remaining),
                resetTime
            };
        } catch (error) {
            console.error('Rate limiting error:', error);
            // In case of error, allow the request but log the issue
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                ipAddress: identifier.startsWith('ip:') ? identifier.substring(3) : 'unknown',
                userAgent: undefined,
                success: false,
                errorMessage: error instanceof Error ? error.message : 'Unknown error',
                details: {
                    identifier: key,
                    error: error instanceof Error ? error.message : 'Unknown error',
                    fallbackAllowed: true,
                    component: 'rate_limiter'
                }
            });

            return {
                allowed: true,
                remaining: config.maxRequests - 1,
                resetTime: new Date(now.getTime() + config.windowMs)
            };
        }
    }

    /**
     * Record a request for rate limiting purposes
     */
    async recordRequest(identifier: string, windowMs: number = 60000): Promise<void> {
        const now = new Date();
        const windowStart = new Date(Math.floor(now.getTime() / windowMs) * windowMs);
        const expiresAt = new Date(windowStart.getTime() + windowMs);

        try {
            await prisma.rateLimitTracking.upsert({
                where: {
                    key_identifier_window_start: {
                        key_identifier: identifier,
                        window_start: windowStart
                    }
                },
                update: {
                    request_count: {
                        increment: 1
                    }
                },
                create: {
                    key_identifier: identifier,
                    request_count: 1,
                    window_start: windowStart,
                    expires_at: expiresAt
                }
            });
        } catch (error) {
            console.error('Error recording request for rate limiting:', error);
            // Don't throw error to avoid breaking the request flow
        }
    }

    /**
     * Get remaining requests for a given identifier
     */
    async getRemainingRequests(
        identifier: string,
        maxRequests: number,
        windowMs: number
    ): Promise<number> {
        const windowStart = new Date(Date.now() - windowMs);
        const currentCount = await this.getCurrentRequestCount(identifier, windowStart);
        return Math.max(0, maxRequests - currentCount);
    }

    /**
     * Reset rate limit for a specific identifier
     */
    async resetRateLimit(identifier: string): Promise<void> {
        try {
            await prisma.rateLimitTracking.deleteMany({
                where: {
                    key_identifier: identifier
                }
            });

            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SYSTEM_SETTINGS_CHANGED,
                userId: undefined, // This would be set by the calling context
                email: undefined,
                ipAddress: 'system',
                userAgent: undefined,
                resource: 'rate_limit',
                action: 'reset',
                success: true,
                details: {
                    identifier,
                    resetBy: 'admin',
                    component: 'rate_limiter'
                }
            });
        } catch (error) {
            console.error('Error resetting rate limit:', error);
            throw error;
        }
    }

    /**
     * Get current request count for an identifier within a time window
     */
    private async getCurrentRequestCount(identifier: string, windowStart: Date): Promise<number> {
        const result = await prisma.rateLimitTracking.aggregate({
            where: {
                key_identifier: identifier,
                window_start: {
                    gte: windowStart
                }
            },
            _sum: {
                request_count: true
            }
        });

        return result._sum.request_count || 0;
    }

    /**
     * Get the oldest entry for an identifier (used for calculating reset time)
     */
    private async getOldestEntry(identifier: string) {
        return await prisma.rateLimitTracking.findFirst({
            where: {
                key_identifier: identifier
            },
            orderBy: {
                window_start: 'asc'
            }
        });
    }

    /**
     * Clean up expired rate limit entries
     */
    private async cleanupExpiredEntries(): Promise<void> {
        const now = new Date();

        try {
            await prisma.rateLimitTracking.deleteMany({
                where: {
                    expires_at: {
                        lt: now
                    }
                }
            });
        } catch (error) {
            console.error('Error cleaning up expired rate limit entries:', error);
            // Don't throw error to avoid breaking the request flow
        }
    }

    /**
     * Get rate limit statistics for monitoring
     */
    async getRateLimitStats(identifier?: string): Promise<{
        totalEntries: number;
        activeWindows: number;
        topIdentifiers: Array<{ identifier: string; requestCount: number }>;
    }> {
        const now = new Date();

        const [totalEntries, activeWindows, topIdentifiers] = await Promise.all([
            // Total entries
            prisma.rateLimitTracking.count(identifier ? {
                where: { key_identifier: identifier }
            } : undefined),

            // Active windows (not expired)
            prisma.rateLimitTracking.count({
                where: {
                    expires_at: { gte: now },
                    ...(identifier && { key_identifier: identifier })
                }
            }),

            // Top identifiers by request count
            prisma.rateLimitTracking.groupBy({
                by: ['key_identifier'],
                _sum: {
                    request_count: true
                },
                where: {
                    expires_at: { gte: now },
                    ...(identifier && { key_identifier: identifier })
                },
                orderBy: {
                    _sum: {
                        request_count: 'desc'
                    }
                },
                take: 10
            })
        ]);

        return {
            totalEntries,
            activeWindows,
            topIdentifiers: topIdentifiers.map((item: any) => ({
                identifier: item.key_identifier,
                requestCount: item._sum.request_count || 0
            }))
        };
    }
}

// Pre-configured rate limiters for common use cases
export const rateLimiters = {
    // General API rate limiting: 100 requests per minute
    general: new RateLimiter(),

    // Authentication endpoints: 10 requests per minute
    auth: new RateLimiter(),

    // Admin operations: 50 requests per minute
    admin: new RateLimiter(),

    // Password reset: 3 requests per hour
    passwordReset: new RateLimiter()
};

// Rate limit configurations
export const rateLimitConfigs = {
    general: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 100,
        keyGenerator: (ip: string) => `general:${ip}`
    },

    auth: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 10,
        keyGenerator: (ip: string) => `auth:${ip}`
    },

    admin: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 50,
        keyGenerator: (ip: string) => `admin:${ip}`
    },

    passwordReset: {
        windowMs: 60 * 60 * 1000, // 1 hour
        maxRequests: 3,
        keyGenerator: (ip: string) => `password-reset:${ip}`
    },

    // Per-user rate limits
    userSpecific: {
        windowMs: 60 * 1000, // 1 minute
        maxRequests: 200,
        keyGenerator: (userId: string) => `user:${userId}`
    }
} as const;

// Helper functions for common rate limiting scenarios
export const rateLimitHelpers = {
    /**
     * Check general API rate limit by IP
     */
    checkGeneralLimit: async (ipAddress: string): Promise<RateLimitResult> => {
        return rateLimiters.general.checkRateLimit(ipAddress, rateLimitConfigs.general);
    },

    /**
     * Check authentication rate limit by IP
     */
    checkAuthLimit: async (ipAddress: string): Promise<RateLimitResult> => {
        return rateLimiters.auth.checkRateLimit(ipAddress, rateLimitConfigs.auth);
    },

    /**
     * Check admin operation rate limit by IP
     */
    checkAdminLimit: async (ipAddress: string): Promise<RateLimitResult> => {
        return rateLimiters.admin.checkRateLimit(ipAddress, rateLimitConfigs.admin);
    },

    /**
     * Check password reset rate limit by IP
     */
    checkPasswordResetLimit: async (ipAddress: string): Promise<RateLimitResult> => {
        return rateLimiters.passwordReset.checkRateLimit(ipAddress, rateLimitConfigs.passwordReset);
    },

    /**
     * Check user-specific rate limit
     */
    checkUserLimit: async (userId: string): Promise<RateLimitResult> => {
        return rateLimiters.general.checkRateLimit(userId, rateLimitConfigs.userSpecific);
    },

    /**
     * Record a request for general rate limiting
     */
    recordGeneralRequest: async (ipAddress: string): Promise<void> => {
        return rateLimiters.general.recordRequest(
            rateLimitConfigs.general.keyGenerator(ipAddress),
            rateLimitConfigs.general.windowMs
        );
    },

    /**
     * Record an authentication request
     */
    recordAuthRequest: async (ipAddress: string): Promise<void> => {
        return rateLimiters.auth.recordRequest(
            rateLimitConfigs.auth.keyGenerator(ipAddress),
            rateLimitConfigs.auth.windowMs
        );
    }
};