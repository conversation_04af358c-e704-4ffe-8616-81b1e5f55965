import { prisma } from './prisma';

// Rate limiting configuration for external APIs
interface ExternalApiConfig {
    name: string;
    maxRequestsPerMinute: number;
    maxRequestsPerHour: number;
    maxRequestsPerDay: number;
    burstLimit: number; // Maximum requests in a short burst
    burstWindowMs: number; // Time window for burst detection
    backoffMultiplier: number; // Exponential backoff multiplier
    maxBackoffMs: number; // Maximum backoff time
}

// Rate limit result
interface RateLimitResult {
    allowed: boolean;
    remainingMinute: number;
    remainingHour: number;
    remainingDay: number;
    resetTimeMinute: Date;
    resetTimeHour: Date;
    resetTimeDay: Date;
    retryAfterMs?: number;
    backoffMs?: number;
}

// Rate limit tracking entry
interface RateLimitEntry {
    requestsThisMinute: number;
    requestsThisHour: number;
    requestsThisDay: number;
    burstRequests: number;
    lastRequestTime: Date;
    currentBackoffMs: number;
    lastBackoffTime?: Date;
    minuteWindowStart: Date;
    hourWindowStart: Date;
    dayWindowStart: Date;
    burstWindowStart: Date;
}

class ExternalApiRateLimiter {
    private rateLimits: Map<string, RateLimitEntry> = new Map();
    private configs: Map<string, ExternalApiConfig> = new Map();

    constructor() {
        // Initialize default configurations for external APIs
        this.configs.set('freemius', {
            name: 'Freemius API',
            maxRequestsPerMinute: 30,
            maxRequestsPerHour: 1000,
            maxRequestsPerDay: 10000,
            burstLimit: 5,
            burstWindowMs: 10000, // 10 seconds
            backoffMultiplier: 2,
            maxBackoffMs: 300000, // 5 minutes
        });

        this.configs.set('ipregistry', {
            name: 'IP Registry API',
            maxRequestsPerMinute: 60,
            maxRequestsPerHour: 2000,
            maxRequestsPerDay: 20000,
            burstLimit: 10,
            burstWindowMs: 5000, // 5 seconds
            backoffMultiplier: 1.5,
            maxBackoffMs: 120000, // 2 minutes
        });

        // Clean up old entries periodically
        setInterval(() => this.cleanup(), 60000); // Every minute
    }

    /**
     * Check if a request is allowed for the specified API
     */
    async checkRateLimit(apiName: string): Promise<RateLimitResult> {
        const config = this.configs.get(apiName);
        if (!config) {
            throw new Error(`Unknown API: ${apiName}`);
        }

        const now = new Date();
        let entry = this.rateLimits.get(apiName);

        if (!entry) {
            entry = this.createNewEntry(now);
            this.rateLimits.set(apiName, entry);
        }

        // Update time windows if needed
        this.updateTimeWindows(entry, now);

        // Check if we're in a backoff period
        if (entry.lastBackoffTime && entry.currentBackoffMs > 0) {
            const timeSinceBackoff = now.getTime() - entry.lastBackoffTime.getTime();
            if (timeSinceBackoff < entry.currentBackoffMs) {
                return {
                    allowed: false,
                    remainingMinute: config.maxRequestsPerMinute - entry.requestsThisMinute,
                    remainingHour: config.maxRequestsPerHour - entry.requestsThisHour,
                    remainingDay: config.maxRequestsPerDay - entry.requestsThisDay,
                    resetTimeMinute: new Date(entry.minuteWindowStart.getTime() + 60000),
                    resetTimeHour: new Date(entry.hourWindowStart.getTime() + 3600000),
                    resetTimeDay: new Date(entry.dayWindowStart.getTime() + 86400000),
                    retryAfterMs: entry.currentBackoffMs - timeSinceBackoff,
                    backoffMs: entry.currentBackoffMs,
                };
            } else {
                // Backoff period has expired, reset backoff
                entry.currentBackoffMs = 0;
                entry.lastBackoffTime = undefined;
            }
        }

        // Check burst limit
        if (this.isBurstLimitExceeded(entry, config, now)) {
            this.applyBackoff(entry, config);
            return this.createRateLimitResult(entry, config, false, entry.currentBackoffMs);
        }

        // Check rate limits
        const isAllowed =
            entry.requestsThisMinute < config.maxRequestsPerMinute &&
            entry.requestsThisHour < config.maxRequestsPerHour &&
            entry.requestsThisDay < config.maxRequestsPerDay;

        if (!isAllowed) {
            this.applyBackoff(entry, config);
        }

        return this.createRateLimitResult(entry, config, isAllowed);
    }

    /**
     * Record a successful request
     */
    async recordRequest(apiName: string): Promise<void> {
        const entry = this.rateLimits.get(apiName);
        if (!entry) {
            return;
        }

        const now = new Date();
        entry.requestsThisMinute++;
        entry.requestsThisHour++;
        entry.requestsThisDay++;
        entry.lastRequestTime = now;

        // Update burst tracking
        if (now.getTime() - entry.burstWindowStart.getTime() <= this.configs.get(apiName)!.burstWindowMs) {
            entry.burstRequests++;
        } else {
            entry.burstRequests = 1;
            entry.burstWindowStart = now;
        }

        // Reset backoff on successful request (if not in burst)
        if (entry.burstRequests <= this.configs.get(apiName)!.burstLimit) {
            entry.currentBackoffMs = 0;
            entry.lastBackoffTime = undefined;
        }

        // Persist to database for cross-instance synchronization
        await this.persistRateLimit(apiName, entry);
    }

    /**
     * Record a failed request (applies additional backoff)
     */
    async recordFailure(apiName: string, error?: Error): Promise<void> {
        const config = this.configs.get(apiName);
        const entry = this.rateLimits.get(apiName);

        if (!config || !entry) {
            return;
        }

        // Apply exponential backoff for failures
        if (entry.currentBackoffMs === 0) {
            entry.currentBackoffMs = 1000; // Start with 1 second
        } else {
            entry.currentBackoffMs = Math.min(
                entry.currentBackoffMs * config.backoffMultiplier,
                config.maxBackoffMs
            );
        }

        entry.lastBackoffTime = new Date();

        // Log the failure for monitoring
        console.warn(`External API failure for ${apiName}:`, {
            error: error?.message,
            backoffMs: entry.currentBackoffMs,
            timestamp: new Date().toISOString(),
        });

        await this.persistRateLimit(apiName, entry);
    }

    /**
     * Get current rate limit status for an API
     */
    getRateLimitStatus(apiName: string): RateLimitResult | null {
        const config = this.configs.get(apiName);
        const entry = this.rateLimits.get(apiName);

        if (!config || !entry) {
            return null;
        }

        const now = new Date();
        this.updateTimeWindows(entry, now);

        return this.createRateLimitResult(entry, config, true);
    }

    /**
     * Get rate limit statistics for monitoring
     */
    getStatistics(): Array<{
        apiName: string;
        config: ExternalApiConfig;
        currentUsage: {
            requestsThisMinute: number;
            requestsThisHour: number;
            requestsThisDay: number;
            burstRequests: number;
        };
        status: 'healthy' | 'throttled' | 'backoff';
        backoffMs: number;
        lastRequestTime?: Date;
    }> {
        const stats: Array<any> = [];

        for (const [apiName, config] of this.configs) {
            const entry = this.rateLimits.get(apiName);

            let status: 'healthy' | 'throttled' | 'backoff' = 'healthy';
            if (entry?.currentBackoffMs && entry.currentBackoffMs > 0) {
                status = 'backoff';
            } else if (entry) {
                const utilizationMinute = (entry.requestsThisMinute / config.maxRequestsPerMinute) * 100;
                const utilizationHour = (entry.requestsThisHour / config.maxRequestsPerHour) * 100;

                if (utilizationMinute > 80 || utilizationHour > 80) {
                    status = 'throttled';
                }
            }

            stats.push({
                apiName,
                config,
                currentUsage: entry ? {
                    requestsThisMinute: entry.requestsThisMinute,
                    requestsThisHour: entry.requestsThisHour,
                    requestsThisDay: entry.requestsThisDay,
                    burstRequests: entry.burstRequests,
                } : {
                    requestsThisMinute: 0,
                    requestsThisHour: 0,
                    requestsThisDay: 0,
                    burstRequests: 0,
                },
                status,
                backoffMs: entry?.currentBackoffMs || 0,
                lastRequestTime: entry?.lastRequestTime,
            });
        }

        return stats;
    }

    /**
     * Reset rate limits for an API (useful for testing or manual intervention)
     */
    resetRateLimit(apiName: string): void {
        this.rateLimits.delete(apiName);
    }

    /**
     * Update API configuration
     */
    updateConfig(apiName: string, config: Partial<ExternalApiConfig>): void {
        const existingConfig = this.configs.get(apiName);
        if (existingConfig) {
            this.configs.set(apiName, { ...existingConfig, ...config });
        }
    }

    /**
     * Create a new rate limit entry
     */
    private createNewEntry(now: Date): RateLimitEntry {
        return {
            requestsThisMinute: 0,
            requestsThisHour: 0,
            requestsThisDay: 0,
            burstRequests: 0,
            lastRequestTime: now,
            currentBackoffMs: 0,
            minuteWindowStart: now,
            hourWindowStart: now,
            dayWindowStart: now,
            burstWindowStart: now,
        };
    }

    /**
     * Update time windows and reset counters if needed
     */
    private updateTimeWindows(entry: RateLimitEntry, now: Date): void {
        // Reset minute window
        if (now.getTime() - entry.minuteWindowStart.getTime() >= 60000) {
            entry.requestsThisMinute = 0;
            entry.minuteWindowStart = now;
        }

        // Reset hour window
        if (now.getTime() - entry.hourWindowStart.getTime() >= 3600000) {
            entry.requestsThisHour = 0;
            entry.hourWindowStart = now;
        }

        // Reset day window
        if (now.getTime() - entry.dayWindowStart.getTime() >= 86400000) {
            entry.requestsThisDay = 0;
            entry.dayWindowStart = now;
        }
    }

    /**
     * Check if burst limit is exceeded
     */
    private isBurstLimitExceeded(entry: RateLimitEntry, config: ExternalApiConfig, now: Date): boolean {
        if (now.getTime() - entry.burstWindowStart.getTime() > config.burstWindowMs) {
            entry.burstRequests = 0;
            entry.burstWindowStart = now;
        }

        return entry.burstRequests >= config.burstLimit;
    }

    /**
     * Apply exponential backoff
     */
    private applyBackoff(entry: RateLimitEntry, config: ExternalApiConfig): void {
        if (entry.currentBackoffMs === 0) {
            entry.currentBackoffMs = 1000; // Start with 1 second
        } else {
            entry.currentBackoffMs = Math.min(
                entry.currentBackoffMs * config.backoffMultiplier,
                config.maxBackoffMs
            );
        }

        entry.lastBackoffTime = new Date();
    }

    /**
     * Create rate limit result object
     */
    private createRateLimitResult(
        entry: RateLimitEntry,
        config: ExternalApiConfig,
        allowed: boolean,
        retryAfterMs?: number
    ): RateLimitResult {
        return {
            allowed,
            remainingMinute: Math.max(0, config.maxRequestsPerMinute - entry.requestsThisMinute),
            remainingHour: Math.max(0, config.maxRequestsPerHour - entry.requestsThisHour),
            remainingDay: Math.max(0, config.maxRequestsPerDay - entry.requestsThisDay),
            resetTimeMinute: new Date(entry.minuteWindowStart.getTime() + 60000),
            resetTimeHour: new Date(entry.hourWindowStart.getTime() + 3600000),
            resetTimeDay: new Date(entry.dayWindowStart.getTime() + 86400000),
            retryAfterMs,
            backoffMs: entry.currentBackoffMs,
        };
    }

    /**
     * Persist rate limit data to database for cross-instance synchronization
     */
    private async persistRateLimit(apiName: string, entry: RateLimitEntry): Promise<void> {
        try {
            await prisma.rateLimitTracking.upsert({
                where: {
                    key_identifier_window_start: {
                        key_identifier: `external_api:${apiName}`,
                        window_start: entry.minuteWindowStart,
                    }
                },
                update: {
                    request_count: entry.requestsThisMinute,
                    expires_at: new Date(entry.minuteWindowStart.getTime() + 60000),
                },
                create: {
                    key_identifier: `external_api:${apiName}`,
                    request_count: entry.requestsThisMinute,
                    window_start: entry.minuteWindowStart,
                    expires_at: new Date(entry.minuteWindowStart.getTime() + 60000),
                }
            });
        } catch (error) {
            console.error(`Failed to persist rate limit for ${apiName}:`, error);
        }
    }

    /**
     * Load rate limit data from database
     */
    private async loadRateLimit(apiName: string): Promise<RateLimitEntry | null> {
        try {
            const record = await prisma.rateLimitTracking.findFirst({
                where: {
                    key_identifier: `external_api:${apiName}`,
                    expires_at: { gt: new Date() }
                },
                orderBy: { window_start: 'desc' }
            });

            if (!record) {
                return null;
            }

            const now = new Date();
            return {
                requestsThisMinute: record.request_count,
                requestsThisHour: 0, // Would need additional records for hour/day tracking
                requestsThisDay: 0,
                burstRequests: 0,
                lastRequestTime: record.created_at,
                currentBackoffMs: 0,
                minuteWindowStart: record.window_start,
                hourWindowStart: now,
                dayWindowStart: now,
                burstWindowStart: now,
            };
        } catch (error) {
            console.error(`Failed to load rate limit for ${apiName}:`, error);
            return null;
        }
    }

    /**
     * Clean up expired entries
     */
    private cleanup(): void {
        const now = new Date();
        const cutoffTime = now.getTime() - 86400000; // 24 hours ago

        for (const [apiName, entry] of this.rateLimits) {
            if (entry.lastRequestTime.getTime() < cutoffTime) {
                this.rateLimits.delete(apiName);
            }
        }
    }
}

// Export singleton instance
export const externalApiRateLimiter = new ExternalApiRateLimiter();

// Export types
export type { ExternalApiConfig, RateLimitResult };