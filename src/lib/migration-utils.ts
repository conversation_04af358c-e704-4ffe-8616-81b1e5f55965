import { prisma } from './prisma';
import { UserRole } from '../generated/prisma';

export interface MigrationResult {
    success: boolean;
    message: string;
    details: {
        usersUpdated: number;
        errors: string[];
    };
}

export class MigrationUtils {
    /**
     * Migrate user roles from old values to new enum values
     */
    async migrateUserRoles(): Promise<MigrationResult> {
        const errors: string[] = [];
        let usersUpdated = 0;

        try {
            // Get all users with potentially old role values
            const users = await prisma.user.findMany();

            for (const user of users) {
                let newRole: UserRole | null = null;

                // Map old role values to new enum values
                switch (user.role.toUpperCase()) {
                    case 'ADMIN':
                    case 'ADMINISTRATOR':
                        newRole = UserRole.SUPER_ADMIN;
                        break;
                    case 'SUPER_ADMIN':
                    case 'SUPERADMIN':
                        newRole = UserRole.SUPER_ADMIN;
                        break;
                    case 'DEV':
                    case 'DEVELOPER':
                    case 'DEVELOPMENT':
                        newRole = UserRole.DEV;
                        break;
                    case 'MARKETING':
                    case 'MARKETER':
                        newRole = UserRole.MARKETING;
                        break;
                    case 'SALES':
                    case 'SALESPERSON':
                        newRole = UserRole.SALES;
                        break;
                    default:
                        // Check if it's already a valid role
                        if (Object.values(UserRole).includes(user.role as UserRole)) {
                            continue; // Skip, already valid
                        } else {
                            errors.push(`Unknown role '${user.role}' for user ${user.email}. Setting to DEV as default.`);
                            newRole = UserRole.DEV;
                        }
                }

                if (newRole && newRole !== user.role) {
                    await prisma.user.update({
                        where: { id: user.id },
                        data: { role: newRole }
                    });
                    usersUpdated++;
                }
            }

            return {
                success: true,
                message: `Successfully migrated user roles. Updated ${usersUpdated} users.`,
                details: {
                    usersUpdated,
                    errors
                }
            };

        } catch (error) {
            return {
                success: false,
                message: `Failed to migrate user roles: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    usersUpdated,
                    errors: [...errors, error instanceof Error ? error.message : 'Unknown error']
                }
            };
        }
    }

    /**
     * Ensure database has proper constraints and indexes
     */
    async validateDatabaseStructure(): Promise<MigrationResult> {
        const errors: string[] = [];

        try {
            // Test that all required tables exist by running simple queries
            await prisma.user.count();
            await prisma.freemiusProduct.count();
            await prisma.freemiusInstallation.count();
            await prisma.freemiusEvent.count();
            await prisma.ipRegistryData.count();
            await prisma.ipAnalysisRequest.count();

            // Test foreign key constraints
            const testInstallation = await prisma.freemiusInstallation.findFirst({
                include: { product: true }
            });

            const testEvent = await prisma.freemiusEvent.findFirst({
                include: {
                    product: true,
                    installation: true
                }
            });

            const testAnalysisRequest = await prisma.ipAnalysisRequest.findFirst({
                include: {
                    installation: true,
                    ip_data: true
                }
            });

            return {
                success: true,
                message: 'Database structure validation passed',
                details: {
                    usersUpdated: 0,
                    errors
                }
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                success: false,
                message: 'Database structure validation failed',
                details: {
                    usersUpdated: 0,
                    errors
                }
            };
        }
    }

    /**
     * Initialize default data if needed
     */
    async initializeDefaultData(): Promise<MigrationResult> {
        const errors: string[] = [];
        let usersUpdated = 0;

        try {
            // Check if we have any super admin users
            const superAdminCount = await prisma.user.count({
                where: { role: UserRole.SUPER_ADMIN }
            });

            if (superAdminCount === 0) {
                // Check if there are any users at all
                const totalUsers = await prisma.user.count();

                if (totalUsers > 0) {
                    // Promote the first user to super admin
                    const firstUser = await prisma.user.findFirst({
                        orderBy: { created_at: 'asc' }
                    });

                    if (firstUser) {
                        await prisma.user.update({
                            where: { id: firstUser.id },
                            data: { role: UserRole.SUPER_ADMIN }
                        });
                        usersUpdated++;
                    }
                }
            }

            return {
                success: true,
                message: `Default data initialization completed. ${usersUpdated > 0 ? `Promoted ${usersUpdated} user(s) to SUPER_ADMIN.` : 'No changes needed.'}`,
                details: {
                    usersUpdated,
                    errors
                }
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                success: false,
                message: 'Failed to initialize default data',
                details: {
                    usersUpdated,
                    errors
                }
            };
        }
    }

    /**
     * Run all migration tasks
     */
    async runAllMigrations(): Promise<MigrationResult> {
        const allErrors: string[] = [];
        let totalUsersUpdated = 0;

        try {
            // 1. Migrate user roles
            const rolesMigration = await this.migrateUserRoles();
            if (!rolesMigration.success) {
                allErrors.push(`Role migration failed: ${rolesMigration.message}`);
            }
            allErrors.push(...rolesMigration.details.errors);
            totalUsersUpdated += rolesMigration.details.usersUpdated;

            // 2. Validate database structure
            const structureValidation = await this.validateDatabaseStructure();
            if (!structureValidation.success) {
                allErrors.push(`Structure validation failed: ${structureValidation.message}`);
            }
            allErrors.push(...structureValidation.details.errors);

            // 3. Initialize default data
            const dataInitialization = await this.initializeDefaultData();
            if (!dataInitialization.success) {
                allErrors.push(`Data initialization failed: ${dataInitialization.message}`);
            }
            allErrors.push(...dataInitialization.details.errors);
            totalUsersUpdated += dataInitialization.details.usersUpdated;

            const success = allErrors.length === 0;

            return {
                success,
                message: success
                    ? `All migrations completed successfully. Updated ${totalUsersUpdated} users total.`
                    : `Migrations completed with ${allErrors.length} errors.`,
                details: {
                    usersUpdated: totalUsersUpdated,
                    errors: allErrors
                }
            };

        } catch (error) {
            return {
                success: false,
                message: `Migration process failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                details: {
                    usersUpdated: totalUsersUpdated,
                    errors: [...allErrors, error instanceof Error ? error.message : 'Unknown error']
                }
            };
        }
    }
}

export const migrationUtils = new MigrationUtils();