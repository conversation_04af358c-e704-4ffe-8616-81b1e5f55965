import { SecurityLogger, SecurityEventType } from './security-logger';
import { Logger } from './logger';

/**
 * Security alerting system for detecting and responding to suspicious activities
 */

interface UnauthorizedAttemptTracker {
    userId?: string;
    ipAddress: string;
    attempts: number;
    lastAttempt: Date;
    resources: string[];
    firstAttempt: Date;
}

interface SecurityAlert {
    id: string;
    type: 'REPEATED_UNAUTHORIZED_ACCESS' | 'SUSPICIOUS_IP_ACTIVITY' | 'ROLE_ESCALATION_ATTEMPT' | 'BRUTE_FORCE_ATTACK';
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
    userId?: string;
    ipAddress: string;
    metadata: Record<string, any>;
    createdAt: Date;
    resolved: boolean;
    resolvedAt?: Date;
    resolvedBy?: string;
    resolvedReason?: string;
}

interface AlertRule {
    id: string;
    name: string;
    type: SecurityAlert['type'];
    enabled: boolean;
    threshold: number;
    timeWindow: number; // in milliseconds
    severity: SecurityAlert['severity'];
    description: string;
    actions: AlertAction[];
}

interface AlertAction {
    type: 'LOG' | 'EMAIL' | 'BLOCK_IP' | 'DISABLE_USER' | 'WEBHOOK';
    config: Record<string, any>;
}

/**
 * Security Alerting Service
 */
export class SecurityAlerting {
    private static unauthorizedAttempts = new Map<string, UnauthorizedAttemptTracker>();
    private static alerts = new Map<string, SecurityAlert>();
    private static alertRules: AlertRule[] = [
        {
            id: 'repeated-unauthorized-access',
            name: 'Repeated Unauthorized Access',
            type: 'REPEATED_UNAUTHORIZED_ACCESS',
            enabled: true,
            threshold: 5,
            timeWindow: 15 * 60 * 1000, // 15 minutes
            severity: 'MEDIUM',
            description: 'Multiple unauthorized access attempts from the same source',
            actions: [
                { type: 'LOG', config: {} },
                { type: 'EMAIL', config: { recipients: ['<EMAIL>'] } }
            ]
        },
        {
            id: 'critical-unauthorized-access',
            name: 'Critical Unauthorized Access',
            type: 'REPEATED_UNAUTHORIZED_ACCESS',
            enabled: true,
            threshold: 10,
            timeWindow: 15 * 60 * 1000, // 15 minutes
            severity: 'HIGH',
            description: 'Critical number of unauthorized access attempts - potential attack',
            actions: [
                { type: 'LOG', config: {} },
                { type: 'EMAIL', config: { recipients: ['<EMAIL>'] } },
                { type: 'BLOCK_IP', config: { duration: 3600000 } } // 1 hour
            ]
        },
        {
            id: 'role-escalation-attempt',
            name: 'Role Escalation Attempt',
            type: 'ROLE_ESCALATION_ATTEMPT',
            enabled: true,
            threshold: 3,
            timeWindow: 5 * 60 * 1000, // 5 minutes
            severity: 'HIGH',
            description: 'Attempts to access resources requiring higher privileges',
            actions: [
                { type: 'LOG', config: {} },
                { type: 'EMAIL', config: { recipients: ['<EMAIL>'] } }
            ]
        },
        {
            id: 'suspicious-ip-activity',
            name: 'Suspicious IP Activity',
            type: 'SUSPICIOUS_IP_ACTIVITY',
            enabled: true,
            threshold: 20,
            timeWindow: 60 * 60 * 1000, // 1 hour
            severity: 'MEDIUM',
            description: 'High volume of requests from single IP address',
            actions: [
                { type: 'LOG', config: {} }
            ]
        }
    ];

    /**
     * Track unauthorized access attempt and trigger alerts if thresholds are met
     */
    static async trackUnauthorizedAttempt(
        userId: string | undefined,
        ipAddress: string,
        resource: string,
        userAgent: string,
        requiredRole?: string,
        requiredPermissions?: string[]
    ): Promise<void> {
        const key = userId || ipAddress;
        const now = new Date();

        let tracker = this.unauthorizedAttempts.get(key);

        if (!tracker) {
            tracker = {
                userId,
                ipAddress,
                attempts: 1,
                lastAttempt: now,
                firstAttempt: now,
                resources: [resource]
            };
        } else {
            tracker.attempts++;
            tracker.lastAttempt = now;
            if (!tracker.resources.includes(resource)) {
                tracker.resources.push(resource);
            }
        }

        this.unauthorizedAttempts.set(key, tracker);

        // Log the unauthorized attempt
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.AUTHORIZATION_FAILED,
            userId,
            ipAddress,
            userAgent,
            resource,
            action: 'unauthorized_access_attempt',
            success: false,
            details: {
                reason: 'insufficient_permissions',
                required_role: requiredRole,
                required_permissions: requiredPermissions,
                attempt_count: tracker.attempts,
                resources_attempted: tracker.resources,
                time_since_first_attempt: now.getTime() - tracker.firstAttempt.getTime()
            }
        });

        // Check alert rules
        await this.evaluateAlertRules(tracker, 'REPEATED_UNAUTHORIZED_ACCESS');

        // Check for role escalation attempts
        if (requiredRole || (requiredPermissions && requiredPermissions.length > 0)) {
            await this.evaluateAlertRules(tracker, 'ROLE_ESCALATION_ATTEMPT');
        }

        // Cleanup old attempts
        this.cleanupOldAttempts();
    }

    /**
     * Track suspicious IP activity
     */
    static async trackSuspiciousIpActivity(
        ipAddress: string,
        userAgent: string,
        endpoint: string,
        requestCount: number
    ): Promise<void> {
        const tracker: UnauthorizedAttemptTracker = {
            ipAddress,
            attempts: requestCount,
            lastAttempt: new Date(),
            firstAttempt: new Date(Date.now() - 60 * 60 * 1000), // Assume 1 hour window
            resources: [endpoint]
        };

        await this.evaluateAlertRules(tracker, 'SUSPICIOUS_IP_ACTIVITY');

        // Log suspicious activity
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            ipAddress,
            userAgent,
            resource: endpoint,
            action: 'suspicious_ip_activity',
            success: false,
            details: {
                request_count: requestCount,
                time_window: '1_hour',
                alert_type: 'suspicious_ip_activity'
            }
        });
    }

    /**
     * Evaluate alert rules against current activity
     */
    private static async evaluateAlertRules(
        tracker: UnauthorizedAttemptTracker,
        alertType: SecurityAlert['type']
    ): Promise<void> {
        const applicableRules = this.alertRules.filter(
            rule => rule.enabled && rule.type === alertType
        );

        for (const rule of applicableRules) {
            const timeWindow = rule.timeWindow;
            const timeSinceFirst = tracker.lastAttempt.getTime() - tracker.firstAttempt.getTime();

            // Check if attempts exceed threshold within time window
            if (tracker.attempts >= rule.threshold && timeSinceFirst <= timeWindow) {
                await this.triggerAlert(rule, tracker);
            }
        }
    }

    /**
     * Trigger a security alert
     */
    private static async triggerAlert(
        rule: AlertRule,
        tracker: UnauthorizedAttemptTracker
    ): Promise<void> {
        const alertId = `${rule.id}-${tracker.ipAddress}-${Date.now()}`;

        const alert: SecurityAlert = {
            id: alertId,
            type: rule.type,
            severity: rule.severity,
            description: rule.description,
            userId: tracker.userId,
            ipAddress: tracker.ipAddress,
            metadata: {
                attempts: tracker.attempts,
                resources: tracker.resources,
                timeWindow: rule.timeWindow,
                threshold: rule.threshold,
                firstAttempt: tracker.firstAttempt,
                lastAttempt: tracker.lastAttempt,
                ruleName: rule.name
            },
            createdAt: new Date(),
            resolved: false
        };

        this.alerts.set(alertId, alert);

        // Log the alert
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: tracker.userId,
            ipAddress: tracker.ipAddress,
            resource: 'security_alert',
            action: 'alert_triggered',
            success: true,
            details: {
                alert_id: alertId,
                alert_type: rule.type,
                severity: rule.severity,
                attempts: tracker.attempts,
                resources: tracker.resources,
                rule_name: rule.name,
                threshold_exceeded: true
            }
        });

        // Execute alert actions
        await this.executeAlertActions(rule.actions, alert);

        Logger.warn(`Security alert triggered: ${rule.name}`, {
            alertId,
            severity: rule.severity,
            ipAddress: tracker.ipAddress,
            userId: tracker.userId,
            attempts: tracker.attempts,
            resources: tracker.resources
        });
    }

    /**
     * Execute alert actions
     */
    private static async executeAlertActions(
        actions: AlertAction[],
        alert: SecurityAlert
    ): Promise<void> {
        for (const action of actions) {
            try {
                switch (action.type) {
                    case 'LOG':
                        Logger.error(`SECURITY ALERT: ${alert.description}`, {
                            alertId: alert.id,
                            severity: alert.severity,
                            ipAddress: alert.ipAddress,
                            userId: alert.userId,
                            metadata: alert.metadata
                        });
                        break;

                    case 'EMAIL':
                        // Email notification would be implemented here
                        Logger.info(`Email alert sent for security incident: ${alert.id}`, {
                            recipients: action.config.recipients,
                            alertType: alert.type,
                            severity: alert.severity
                        });
                        break;

                    case 'BLOCK_IP':
                        // IP blocking would be implemented here
                        Logger.warn(`IP blocking triggered for security incident: ${alert.id}`, {
                            ipAddress: alert.ipAddress,
                            duration: action.config.duration,
                            alertType: alert.type
                        });
                        break;

                    case 'DISABLE_USER':
                        // User disabling would be implemented here
                        if (alert.userId) {
                            Logger.warn(`User disable triggered for security incident: ${alert.id}`, {
                                userId: alert.userId,
                                alertType: alert.type
                            });
                        }
                        break;

                    case 'WEBHOOK':
                        // Webhook notification would be implemented here
                        Logger.info(`Webhook notification sent for security incident: ${alert.id}`, {
                            webhookUrl: action.config.url,
                            alertType: alert.type
                        });
                        break;
                }
            } catch (error) {
                Logger.error(`Failed to execute alert action: ${action.type}`, error, {
                    alertId: alert.id,
                    actionType: action.type
                });
            }
        }
    }

    /**
     * Get all active alerts
     */
    static getActiveAlerts(): SecurityAlert[] {
        return Array.from(this.alerts.values()).filter(alert => !alert.resolved);
    }

    /**
     * Get alert by ID
     */
    static getAlert(alertId: string): SecurityAlert | undefined {
        return this.alerts.get(alertId);
    }

    /**
     * Resolve an alert
     */
    static async resolveAlert(
        alertId: string,
        resolvedBy: string,
        reason?: string
    ): Promise<boolean> {
        const alert = this.alerts.get(alertId);
        if (!alert) {
            return false;
        }

        alert.resolved = true;
        alert.resolvedAt = new Date();
        alert.resolvedBy = resolvedBy;
        alert.resolvedReason = reason;

        // Log alert resolution
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: resolvedBy,
            ipAddress: alert.ipAddress,
            resource: 'security_alert',
            action: 'alert_resolved',
            success: true,
            details: {
                alert_id: alertId,
                alert_type: alert.type,
                resolved_reason: reason,
                resolution_time: alert.resolvedAt.getTime() - alert.createdAt.getTime()
            }
        });

        Logger.info(`Security alert resolved: ${alertId}`, {
            alertType: alert.type,
            resolvedBy,
            reason,
            resolutionTime: alert.resolvedAt.getTime() - alert.createdAt.getTime()
        });

        return true;
    }

    /**
     * Suppress an alert for a specified duration
     */
    static async suppressAlert(
        alertId: string,
        duration: number,
        reason?: string
    ): Promise<boolean> {
        const alert = this.alerts.get(alertId);
        if (!alert) {
            return false;
        }

        // Add suppression metadata
        alert.metadata.suppressed = true;
        alert.metadata.suppressedUntil = new Date(Date.now() + duration);
        alert.metadata.suppressionReason = reason;

        Logger.info(`Security alert suppressed: ${alertId}`, {
            alertType: alert.type,
            duration,
            reason,
            suppressedUntil: alert.metadata.suppressedUntil
        });

        return true;
    }

    /**
     * Get alert rules
     */
    static getAlertRules(): AlertRule[] {
        return [...this.alertRules];
    }

    /**
     * Update alert rule
     */
    static updateAlertRule(ruleId: string, updates: Partial<AlertRule>): boolean {
        const ruleIndex = this.alertRules.findIndex(rule => rule.id === ruleId);
        if (ruleIndex === -1) {
            return false;
        }

        this.alertRules[ruleIndex] = { ...this.alertRules[ruleIndex], ...updates };
        return true;
    }

    /**
     * Clean up old unauthorized attempts
     */
    private static cleanupOldAttempts(): void {
        const now = Date.now();
        const maxAge = 60 * 60 * 1000; // 1 hour

        for (const [key, tracker] of this.unauthorizedAttempts.entries()) {
            if (now - tracker.lastAttempt.getTime() > maxAge) {
                this.unauthorizedAttempts.delete(key);
            }
        }
    }

    /**
     * Perform security health check
     */
    static async performSecurityHealthCheck(): Promise<{
        status: 'healthy' | 'warning' | 'critical';
        activeAlerts: number;
        criticalAlerts: number;
        recentUnauthorizedAttempts: number;
        recommendations: string[];
    }> {
        const activeAlerts = this.getActiveAlerts();
        const criticalAlerts = activeAlerts.filter(alert => alert.severity === 'CRITICAL').length;
        const highAlerts = activeAlerts.filter(alert => alert.severity === 'HIGH').length;
        const recentUnauthorizedAttempts = this.unauthorizedAttempts.size;

        let status: 'healthy' | 'warning' | 'critical' = 'healthy';
        const recommendations: string[] = [];

        if (criticalAlerts > 0) {
            status = 'critical';
            recommendations.push(`${criticalAlerts} critical security alerts require immediate attention`);
        } else if (highAlerts > 2 || recentUnauthorizedAttempts > 10) {
            status = 'warning';
            if (highAlerts > 2) {
                recommendations.push(`${highAlerts} high-severity alerts detected`);
            }
            if (recentUnauthorizedAttempts > 10) {
                recommendations.push(`${recentUnauthorizedAttempts} recent unauthorized access attempts`);
            }
        }

        if (activeAlerts.length === 0 && recentUnauthorizedAttempts === 0) {
            recommendations.push('Security status is healthy - no active threats detected');
        }

        return {
            status,
            activeAlerts: activeAlerts.length,
            criticalAlerts,
            recentUnauthorizedAttempts,
            recommendations
        };
    }

    /**
     * Generate security report
     */
    static async generateSecurityReport(
        reportType: 'DAILY' | 'WEEKLY' | 'MONTHLY',
        timeRange: { start: Date; end: Date }
    ): Promise<{
        reportType: string;
        timeRange: { start: Date; end: Date };
        summary: {
            totalAlerts: number;
            alertsBySeverity: Record<string, number>;
            alertsByType: Record<string, number>;
            topIpAddresses: Array<{ ip: string; attempts: number }>;
            topResources: Array<{ resource: string; attempts: number }>;
        };
        alerts: SecurityAlert[];
        recommendations: string[];
    }> {
        const alerts = Array.from(this.alerts.values()).filter(
            alert => alert.createdAt >= timeRange.start && alert.createdAt <= timeRange.end
        );

        const alertsBySeverity = alerts.reduce((acc, alert) => {
            acc[alert.severity] = (acc[alert.severity] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const alertsByType = alerts.reduce((acc, alert) => {
            acc[alert.type] = (acc[alert.type] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const ipCounts = alerts.reduce((acc, alert) => {
            acc[alert.ipAddress] = (acc[alert.ipAddress] || 0) + 1;
            return acc;
        }, {} as Record<string, number>);

        const topIpAddresses = Object.entries(ipCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([ip, attempts]) => ({ ip, attempts }));

        const resourceCounts = alerts.reduce((acc, alert) => {
            const resources = alert.metadata.resources || [];
            resources.forEach((resource: string) => {
                acc[resource] = (acc[resource] || 0) + 1;
            });
            return acc;
        }, {} as Record<string, number>);

        const topResources = Object.entries(resourceCounts)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([resource, attempts]) => ({ resource, attempts }));

        const recommendations: string[] = [];

        if (alerts.length === 0) {
            recommendations.push('No security alerts during this period - maintain current security posture');
        } else {
            if (alertsBySeverity.CRITICAL > 0) {
                recommendations.push(`Review and address ${alertsBySeverity.CRITICAL} critical security alerts`);
            }
            if (topIpAddresses.length > 0) {
                recommendations.push(`Monitor top attacking IP addresses: ${topIpAddresses.slice(0, 3).map(ip => ip.ip).join(', ')}`);
            }
            if (topResources.length > 0) {
                recommendations.push(`Strengthen protection for frequently targeted resources: ${topResources.slice(0, 3).map(r => r.resource).join(', ')}`);
            }
        }

        return {
            reportType,
            timeRange,
            summary: {
                totalAlerts: alerts.length,
                alertsBySeverity,
                alertsByType,
                topIpAddresses,
                topResources
            },
            alerts,
            recommendations
        };
    }
}

// Export convenience functions
export const trackUnauthorizedAttempt = SecurityAlerting.trackUnauthorizedAttempt.bind(SecurityAlerting);
export const getActiveAlerts = SecurityAlerting.getActiveAlerts.bind(SecurityAlerting);
export const resolveAlert = SecurityAlerting.resolveAlert.bind(SecurityAlerting);
export const suppressAlert = SecurityAlerting.suppressAlert.bind(SecurityAlerting);
export const performSecurityHealthCheck = SecurityAlerting.performSecurityHealthCheck.bind(SecurityAlerting);
export const generateSecurityReport = SecurityAlerting.generateSecurityReport.bind(SecurityAlerting);