import { prisma } from './prisma';
// Dynamic imports to avoid initialization errors when API keys are missing

export interface MaintenanceTask {
    name: string;
    description: string;
    frequency: 'hourly' | 'daily' | 'weekly' | 'monthly';
    lastRun?: Date;
    nextRun?: Date;
    enabled: boolean;
}

export interface MaintenanceResult {
    taskName: string;
    success: boolean;
    message: string;
    details: {
        recordsProcessed: number;
        recordsDeleted: number;
        recordsUpdated: number;
        errors: string[];
    };
    executionTime: number;
}

export class MaintenanceScheduler {
    private freemiusService?: any;
    private ipService?: any;

    constructor() {
        // Services will be initialized dynamically when needed
    }

    private async getFreemiusService() {
        if (!this.freemiusService) {
            try {
                const { FreemiusService } = await import('../services/freemius-service');
                this.freemiusService = new FreemiusService();
            } catch (error) {
                console.warn('FreemiusService not available:', error instanceof Error ? error.message : 'Unknown error');
                return null;
            }
        }
        return this.freemiusService;
    }

    private async getIpService() {
        if (!this.ipService) {
            try {
                const { IpIntelligenceService } = await import('../services/ip-intelligence-service');
                this.ipService = new IpIntelligenceService();
            } catch (error) {
                console.warn('IpIntelligenceService not available:', error instanceof Error ? error.message : 'Unknown error');
                return null;
            }
        }
        return this.ipService;
    }

    /**
     * Get all available maintenance tasks
     */
    getMaintenanceTasks(): MaintenanceTask[] {
        return [
            {
                name: 'cleanup_expired_ip_data',
                description: 'Remove expired IP registry data older than 30 days',
                frequency: 'daily',
                enabled: true
            },
            {
                name: 'refresh_stale_ip_data',
                description: 'Refresh IP data that is close to expiring (within 1 day)',
                frequency: 'daily',
                enabled: true
            },
            {
                name: 'sync_freemius_data',
                description: 'Synchronize Freemius products and installations',
                frequency: 'hourly',
                enabled: true
            },
            {
                name: 'cleanup_expired_sessions',
                description: 'Remove expired user sessions and tokens',
                frequency: 'hourly',
                enabled: true
            },
            {
                name: 'cleanup_old_audit_logs',
                description: 'Archive audit logs older than 90 days',
                frequency: 'weekly',
                enabled: true
            },
            {
                name: 'optimize_database',
                description: 'Run database optimization and statistics update',
                frequency: 'weekly',
                enabled: true
            },
            {
                name: 'cleanup_processed_events',
                description: 'Remove old processed Freemius events (older than 30 days)',
                frequency: 'daily',
                enabled: true
            },
            {
                name: 'validate_data_integrity',
                description: 'Run data integrity checks and fix common issues',
                frequency: 'daily',
                enabled: true
            }
        ];
    }

    /**
     * Run all scheduled maintenance tasks
     */
    async runScheduledMaintenance(): Promise<MaintenanceResult[]> {
        const results: MaintenanceResult[] = [];
        const tasks = this.getMaintenanceTasks().filter(task => task.enabled);

        for (const task of tasks) {
            try {
                const result = await this.runMaintenanceTask(task.name);
                results.push(result);
            } catch (error) {
                results.push({
                    taskName: task.name,
                    success: false,
                    message: `Task failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
                    details: {
                        recordsProcessed: 0,
                        recordsDeleted: 0,
                        recordsUpdated: 0,
                        errors: [error instanceof Error ? error.message : 'Unknown error']
                    },
                    executionTime: 0
                });
            }
        }

        return results;
    }

    /**
     * Run a specific maintenance task
     */
    async runMaintenanceTask(taskName: string): Promise<MaintenanceResult> {
        const startTime = Date.now();

        switch (taskName) {
            case 'cleanup_expired_ip_data':
                return await this.cleanupExpiredIpData(startTime);
            case 'refresh_stale_ip_data':
                return await this.refreshStaleIpData(startTime);
            case 'sync_freemius_data':
                return await this.syncFreemiusData(startTime);
            case 'cleanup_expired_sessions':
                return await this.cleanupExpiredSessions(startTime);
            case 'cleanup_old_audit_logs':
                return await this.cleanupOldAuditLogs(startTime);
            case 'optimize_database':
                return await this.optimizeDatabase(startTime);
            case 'cleanup_processed_events':
                return await this.cleanupProcessedEvents(startTime);
            case 'validate_data_integrity':
                return await this.validateDataIntegrity(startTime);
            default:
                throw new Error(`Unknown maintenance task: ${taskName}`);
        }
    }

    /**
     * Clean up expired IP data (older than 30 days past expiration)
     */
    private async cleanupExpiredIpData(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsDeleted = 0;

        try {
            // Delete IP data that expired more than 30 days ago
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

            const deleteResult = await prisma.ipRegistryData.deleteMany({
                where: {
                    data_expires_at: { lt: thirtyDaysAgo }
                }
            });

            recordsDeleted = deleteResult.count;

            return {
                taskName: 'cleanup_expired_ip_data',
                success: true,
                message: `Successfully cleaned up ${recordsDeleted} expired IP records`,
                details: {
                    recordsProcessed: recordsDeleted,
                    recordsDeleted,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'cleanup_expired_ip_data',
                success: false,
                message: 'Failed to cleanup expired IP data',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Refresh IP data that is close to expiring
     */
    private async refreshStaleIpData(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsUpdated = 0;

        try {
            // Find IP data that expires within the next 24 hours
            const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);

            const staleIpData = await prisma.ipRegistryData.findMany({
                where: {
                    data_expires_at: { lt: tomorrow }
                },
                take: 100 // Limit to avoid overwhelming the API
            });

            for (const ipRecord of staleIpData) {
                try {
                    const ipService = await this.getIpService();
                    if (ipService) {
                        await ipService.refreshIpData(ipRecord.ip_address);
                        recordsUpdated++;
                    } else {
                        errors.push(`IpIntelligenceService not available for refreshing IP ${ipRecord.ip_address}`);
                    }
                } catch (error) {
                    errors.push(`Failed to refresh IP ${ipRecord.ip_address}: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }

            return {
                taskName: 'refresh_stale_ip_data',
                success: true,
                message: `Successfully refreshed ${recordsUpdated} IP records`,
                details: {
                    recordsProcessed: staleIpData.length,
                    recordsDeleted: 0,
                    recordsUpdated,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'refresh_stale_ip_data',
                success: false,
                message: 'Failed to refresh stale IP data',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Synchronize Freemius data
     */
    private async syncFreemiusData(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsUpdated = 0;

        try {
            const freemiusService = await this.getFreemiusService();
            if (!freemiusService) {
                throw new Error('FreemiusService not available');
            }

            // Sync products
            const productSyncResult = await freemiusService.syncProducts();
            recordsUpdated += productSyncResult.productsUpdated || 0;

            // Sync installations
            const installationSyncResult = await freemiusService.syncInstallations();
            recordsUpdated += installationSyncResult.installationsUpdated || 0;

            return {
                taskName: 'sync_freemius_data',
                success: true,
                message: `Successfully synced Freemius data: ${productSyncResult.productsUpdated || 0} products, ${installationSyncResult.installationsUpdated || 0} installations`,
                details: {
                    recordsProcessed: recordsUpdated,
                    recordsDeleted: 0,
                    recordsUpdated,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'sync_freemius_data',
                success: false,
                message: 'Failed to sync Freemius data',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Clean up expired sessions and tokens
     */
    private async cleanupExpiredSessions(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsDeleted = 0;

        try {
            const now = new Date();

            // Clean up expired sessions
            const expiredSessions = await prisma.userSession.deleteMany({
                where: {
                    expires_at: { lt: now }
                }
            });
            recordsDeleted += expiredSessions.count;

            // Clean up expired CSRF tokens
            const expiredCsrfTokens = await prisma.csrfToken.deleteMany({
                where: {
                    expires_at: { lt: now }
                }
            });
            recordsDeleted += expiredCsrfTokens.count;

            // Clean up expired password reset tokens
            const expiredResetTokens = await prisma.passwordResetToken.deleteMany({
                where: {
                    expires_at: { lt: now }
                }
            });
            recordsDeleted += expiredResetTokens.count;

            // Clean up expired rate limit tracking
            const expiredRateLimit = await prisma.rateLimitTracking.deleteMany({
                where: {
                    expires_at: { lt: now }
                }
            });
            recordsDeleted += expiredRateLimit.count;

            return {
                taskName: 'cleanup_expired_sessions',
                success: true,
                message: `Successfully cleaned up ${recordsDeleted} expired records`,
                details: {
                    recordsProcessed: recordsDeleted,
                    recordsDeleted,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'cleanup_expired_sessions',
                success: false,
                message: 'Failed to cleanup expired sessions',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Clean up old audit logs (archive logs older than 90 days)
     */
    private async cleanupOldAuditLogs(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsDeleted = 0;

        try {
            const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);

            // Clean up old security audit logs
            const oldAuditLogs = await prisma.securityAuditLog.deleteMany({
                where: {
                    created_at: { lt: ninetyDaysAgo }
                }
            });
            recordsDeleted += oldAuditLogs.count;

            // Clean up old admin activities
            const oldActivities = await prisma.adminActivity.deleteMany({
                where: {
                    created_at: { lt: ninetyDaysAgo }
                }
            });
            recordsDeleted += oldActivities.count;

            return {
                taskName: 'cleanup_old_audit_logs',
                success: true,
                message: `Successfully archived ${recordsDeleted} old audit records`,
                details: {
                    recordsProcessed: recordsDeleted,
                    recordsDeleted,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'cleanup_old_audit_logs',
                success: false,
                message: 'Failed to cleanup old audit logs',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Optimize database performance
     */
    private async optimizeDatabase(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];

        try {
            // For SQLite, run VACUUM and ANALYZE
            await prisma.$executeRaw`VACUUM`;
            await prisma.$executeRaw`ANALYZE`;

            return {
                taskName: 'optimize_database',
                success: true,
                message: 'Successfully optimized database',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'optimize_database',
                success: false,
                message: 'Failed to optimize database',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Clean up old processed Freemius events
     */
    private async cleanupProcessedEvents(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsDeleted = 0;

        try {
            const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

            const oldEvents = await prisma.freemiusEvent.deleteMany({
                where: {
                    AND: [
                        { processing_status: 'processed' },
                        { processed_at: { lt: thirtyDaysAgo } }
                    ]
                }
            });
            recordsDeleted = oldEvents.count;

            return {
                taskName: 'cleanup_processed_events',
                success: true,
                message: `Successfully cleaned up ${recordsDeleted} old processed events`,
                details: {
                    recordsProcessed: recordsDeleted,
                    recordsDeleted,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'cleanup_processed_events',
                success: false,
                message: 'Failed to cleanup processed events',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }

    /**
     * Validate data integrity and fix common issues
     */
    private async validateDataIntegrity(startTime: number): Promise<MaintenanceResult> {
        const errors: string[] = [];
        let recordsUpdated = 0;

        try {
            const { dataValidator } = await import('./data-validation');

            // Run validation
            const validationResult = await dataValidator.validateDatabase();

            if (!validationResult.isValid || validationResult.warnings.length > 0) {
                // Try to fix issues
                const fixResult = await dataValidator.fixDataIntegrityIssues();
                recordsUpdated = fixResult.fixed.length;
                errors.push(...fixResult.errors);
            }

            return {
                taskName: 'validate_data_integrity',
                success: validationResult.isValid,
                message: `Data integrity check completed. Fixed ${recordsUpdated} issues.`,
                details: {
                    recordsProcessed: validationResult.summary.totalUsers,
                    recordsDeleted: 0,
                    recordsUpdated,
                    errors: [...validationResult.errors, ...errors]
                },
                executionTime: Date.now() - startTime
            };

        } catch (error) {
            errors.push(error instanceof Error ? error.message : 'Unknown error');
            return {
                taskName: 'validate_data_integrity',
                success: false,
                message: 'Failed to validate data integrity',
                details: {
                    recordsProcessed: 0,
                    recordsDeleted: 0,
                    recordsUpdated: 0,
                    errors
                },
                executionTime: Date.now() - startTime
            };
        }
    }
}

export const maintenanceScheduler = new MaintenanceScheduler();