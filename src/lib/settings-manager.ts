import { PrismaClient } from '@prisma/client';
import { ActivityLogger, AdminAction } from './activity-logger';

const prisma = new PrismaClient();

export interface SystemSettingDefinition {
  key: string;
  defaultValue: any;
  category: string;
  description: string;
  dataType: 'string' | 'number' | 'boolean' | 'json' | 'array';
  isPublic: boolean;
  requiresRestart: boolean;
  validationRules?: any;
}

export class SettingsManager {
  private static defaultSettings: SystemSettingDefinition[] = [
    // Security Settings
    {
      key: 'security.session_timeout',
      defaultValue: 480, // 8 hours in minutes
      category: 'security',
      description: 'Default session timeout in minutes',
      dataType: 'number',
      isPublic: false,
      requiresRestart: false,
      validationRules: { min: 30, max: 1440 }
    },
    {
      key: 'security.max_login_attempts',
      defaultValue: 5,
      category: 'security',
      description: 'Maximum login attempts before account lockout',
      dataType: 'number',
      isPublic: false,
      requiresRestart: false,
      validationRules: { min: 3, max: 10 }
    },
    {
      key: 'security.lockout_duration',
      defaultValue: 30,
      category: 'security',
      description: 'Account lockout duration in minutes',
      dataType: 'number',
      isPublic: false,
      requiresRestart: false,
      validationRules: { min: 5, max: 1440 }
    },
    {
      key: 'security.require_2fa',
      defaultValue: true,
      category: 'security',
      description: 'Require 2FA for all admin accounts',
      dataType: 'boolean',
      isPublic: false,
      requiresRestart: false
    },
    {
      key: 'security.allowed_domains',
      defaultValue: [],
      category: 'security',
      description: 'Allowed email domains for admin accounts',
      dataType: 'array',
      isPublic: false,
      requiresRestart: false
    },

    // API Settings
    {
      key: 'api.rate_limit_requests',
      defaultValue: 1000,
      category: 'api',
      description: 'API rate limit requests per hour',
      dataType: 'number',
      isPublic: true,
      requiresRestart: true,
      validationRules: { min: 100, max: 10000 }
    },
    {
      key: 'api.ip_cache_duration',
      defaultValue: 2880, // 2 days in minutes
      category: 'api',
      description: 'IP data cache duration in minutes',
      dataType: 'number',
      isPublic: true,
      requiresRestart: false,
      validationRules: { min: 60, max: 10080 }
    },
    {
      key: 'api.ipregistry_api_key',
      defaultValue: '',
      category: 'api',
      description: 'ipRegistry API key',
      dataType: 'string',
      isPublic: false,
      requiresRestart: true
    },
    {
      key: 'api.freemius_secret_key',
      defaultValue: '',
      category: 'api',
      description: 'Freemius webhook secret key',
      dataType: 'string',
      isPublic: false,
      requiresRestart: true
    },

    // Notification Settings
    {
      key: 'notifications.smtp_host',
      defaultValue: '',
      category: 'notifications',
      description: 'SMTP server host',
      dataType: 'string',
      isPublic: false,
      requiresRestart: true
    },
    {
      key: 'notifications.smtp_port',
      defaultValue: 587,
      category: 'notifications',
      description: 'SMTP server port',
      dataType: 'number',
      isPublic: false,
      requiresRestart: true,
      validationRules: { min: 1, max: 65535 }
    },
    {
      key: 'notifications.from_email',
      defaultValue: '<EMAIL>',
      category: 'notifications',
      description: 'From email address for notifications',
      dataType: 'string',
      isPublic: false,
      requiresRestart: true
    },

    // Maintenance Settings
    {
      key: 'maintenance.log_retention_days',
      defaultValue: 90,
      category: 'maintenance',
      description: 'Log retention period in days',
      dataType: 'number',
      isPublic: true,
      requiresRestart: false,
      validationRules: { min: 7, max: 365 }
    },
    {
      key: 'maintenance.auto_cleanup_enabled',
      defaultValue: true,
      category: 'maintenance',
      description: 'Enable automatic cleanup of old logs',
      dataType: 'boolean',
      isPublic: true,
      requiresRestart: false
    }
  ];

  static async initializeDefaultSettings(): Promise<void> {
    for (const setting of this.defaultSettings) {
      const existing = await prisma.systemSettings.findUnique({
        where: { key: setting.key }
      });

      if (!existing) {
        await prisma.systemSettings.create({
          data: {
            key: setting.key,
            value: setting.defaultValue,
            category: setting.category,
            description: setting.description,
            data_type: setting.dataType,
            is_public: setting.isPublic,
            requires_restart: setting.requiresRestart,
            validation_rules: setting.validationRules || null,
            created_by: 'system' // Will be updated when first super admin is created
          }
        });
      }
    }
  }

  static async getSetting(key: string): Promise<any> {
    const setting = await prisma.systemSettings.findUnique({
      where: { key }
    });

    if (!setting) {
      const defaultSetting = this.defaultSettings.find(s => s.key === key);
      return defaultSetting?.defaultValue || null;
    }

    return setting.value;
  }

  static async getSettingsByCategory(category: string, userRole: string): Promise<any[]> {
    const isPublicOnly = userRole !== 'super_admin';
    
    const settings = await prisma.systemSettings.findMany({
      where: {
        category,
        ...(isPublicOnly && { is_public: true })
      },
      orderBy: { key: 'asc' }
    });

    return settings;
  }

  static async getAllSettings(userRole: string): Promise<any[]> {
    const isPublicOnly = userRole !== 'super_admin';
    
    const settings = await prisma.systemSettings.findMany({
      where: isPublicOnly ? { is_public: true } : {},
      include: {
        created_by_user: {
          select: { email: true }
        },
        updated_by_user: {
          select: { email: true }
        }
      },
      orderBy: [{ category: 'asc' }, { key: 'asc' }]
    });

    return settings;
  }

  static async updateSetting(
    key: string, 
    value: any, 
    userId: string, 
    reason?: string,
    ipAddress?: string,
    userAgent?: string
  ): Promise<void> {
    const setting = await prisma.systemSettings.findUnique({
      where: { key }
    });

    if (!setting) {
      throw new Error(`Setting ${key} not found`);
    }

    // Validate the new value
    if (setting.validation_rules) {
      this.validateSettingValue(value, setting.validation_rules, setting.data_type);
    }

    const oldValue = setting.value;

    // Update the setting
    await prisma.systemSettings.update({
      where: { key },
      data: {
        value,
        updated_by: userId,
        updated_at: new Date()
      }
    });

    // Log the change
    await prisma.settingsChangeLog.create({
      data: {
        setting_key: key,
        old_value: oldValue,
        new_value: value,
        changed_by: userId,
        change_reason: reason,
        ip_address: ipAddress,
        user_agent: userAgent
      }
    });

    // Log admin activity
    await ActivityLogger.log({
      userId,
      action: AdminAction.UPDATE_SYSTEM_SETTING,
      resource: 'system_settings',
      resourceId: key,
      details: {
        key,
        oldValue,
        newValue: value,
        reason
      },
      ipAddress,
      userAgent
    });
  }

  private static validateSettingValue(value: any, rules: any, dataType: string): void {
    switch (dataType) {
      case 'number':
        if (typeof value !== 'number') {
          throw new Error('Value must be a number');
        }
        if (rules.min !== undefined && value < rules.min) {
          throw new Error(`Value must be at least ${rules.min}`);
        }
        if (rules.max !== undefined && value > rules.max) {
          throw new Error(`Value must be at most ${rules.max}`);
        }
        break;
      
      case 'string':
        if (typeof value !== 'string') {
          throw new Error('Value must be a string');
        }
        if (rules.minLength !== undefined && value.length < rules.minLength) {
          throw new Error(`Value must be at least ${rules.minLength} characters`);
        }
        if (rules.maxLength !== undefined && value.length > rules.maxLength) {
          throw new Error(`Value must be at most ${rules.maxLength} characters`);
        }
        if (rules.pattern && !new RegExp(rules.pattern).test(value)) {
          throw new Error('Value does not match required pattern');
        }
        break;
      
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new Error('Value must be a boolean');
        }
        break;
      
      case 'array':
        if (!Array.isArray(value)) {
          throw new Error('Value must be an array');
        }
        break;
    }
  }

  static async getSettingsChangeLog(settingKey?: string, limit: number = 100): Promise<any[]> {
    const logs = await prisma.settingsChangeLog.findMany({
      where: settingKey ? { setting_key: settingKey } : {},
      include: {
        changed_by_user: {
          select: { email: true, role: true }
        }
      },
      orderBy: { created_at: 'desc' },
      take: limit
    });

    return logs;
  }
}