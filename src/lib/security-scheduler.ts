import { SecurityAlerting } from './security-alerting';
import { SecurityLogger, SecurityEventType } from './security-logger';

/**
 * Security Alert Scheduler
 * 
 * Manages periodic evaluation of security alert rules and automated responses.
 * Runs background tasks for security monitoring and alerting.
 */
export class SecurityScheduler {
    private static intervals: Map<string, NodeJS.Timeout> = new Map();
    private static isInitialized = false;

    /**
     * Initialize the security scheduler with default intervals
     */
    static async initialize(): Promise<void> {
        if (this.isInitialized) {
            console.log('[SECURITY SCHEDULER] Already initialized');
            return;
        }

        try {
            // Initialize security alerting system
            await SecurityAlerting.initializeDefaultRules();

            // Start periodic alert evaluation (every 5 minutes)
            this.scheduleTask('alert-evaluation', async () => {
                await this.evaluateSecurityAlerts();
            }, 5 * 60 * 1000); // 5 minutes

            // Start periodic health checks (every 15 minutes)
            this.scheduleTask('health-check', async () => {
                await this.performHealthCheck();
            }, 15 * 60 * 1000); // 15 minutes

            // Start daily report generation (every 24 hours)
            this.scheduleTask('daily-report', async () => {
                await this.generateDailyReport();
            }, 24 * 60 * 60 * 1000); // 24 hours

            // Start log cleanup (every 6 hours)
            this.scheduleTask('log-cleanup', async () => {
                await this.performLogCleanup();
            }, 6 * 60 * 60 * 1000); // 6 hours

            this.isInitialized = true;
            console.log('[SECURITY SCHEDULER] Initialized successfully');

            // Log scheduler initialization
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.SYSTEM_SETTINGS_CHANGED,
                ipAddress: 'system',
                resource: 'security_scheduler',
                action: 'initialized',
                success: true,
                details: {
                    tasks: Array.from(this.intervals.keys()),
                    timestamp: new Date().toISOString(),
                },
            });
        } catch (error) {
            console.error('[SECURITY SCHEDULER] Failed to initialize:', error);
            throw error;
        }
    }

    /**
     * Schedule a recurring task
     */
    private static scheduleTask(taskName: string, task: () => Promise<void>, intervalMs: number): void {
        // Clear existing interval if it exists
        const existingInterval = this.intervals.get(taskName);
        if (existingInterval) {
            clearInterval(existingInterval);
        }

        // Schedule the task
        const interval = setInterval(async () => {
            try {
                console.log(`[SECURITY SCHEDULER] Executing task: ${taskName}`);
                await task();
                console.log(`[SECURITY SCHEDULER] Completed task: ${taskName}`);
            } catch (error) {
                console.error(`[SECURITY SCHEDULER] Task ${taskName} failed:`, error);

                // Log task failure
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                    ipAddress: 'system',
                    resource: 'security_scheduler',
                    action: 'task_failed',
                    success: false,
                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                    details: {
                        taskName,
                        error: error instanceof Error ? error.stack : error,
                    },
                });
            }
        }, intervalMs);

        this.intervals.set(taskName, interval);
        console.log(`[SECURITY SCHEDULER] Scheduled task: ${taskName} (interval: ${intervalMs}ms)`);
    }

    /**
     * Evaluate security alert rules
     */
    private static async evaluateSecurityAlerts(): Promise<void> {
        try {
            const triggeredAlerts = await SecurityAlerting.evaluateAlertRules();

            if (triggeredAlerts.length > 0) {
                console.log(`[SECURITY SCHEDULER] Triggered ${triggeredAlerts.length} security alerts`);

                // Log alert evaluation
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
                    ipAddress: 'system',
                    resource: 'security_scheduler',
                    action: 'alerts_evaluated',
                    success: true,
                    details: {
                        triggeredAlerts: triggeredAlerts.length,
                        alertTypes: triggeredAlerts.map(a => a.type),
                    },
                });
            }
        } catch (error) {
            console.error('[SECURITY SCHEDULER] Failed to evaluate security alerts:', error);
            throw error;
        }
    }

    /**
     * Perform security health check
     */
    private static async performHealthCheck(): Promise<void> {
        try {
            const healthCheck = await SecurityAlerting.performSecurityHealthCheck();

            // Log health check results
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                ipAddress: 'system',
                resource: 'security_scheduler',
                action: 'health_check_performed',
                success: true,
                details: {
                    overallHealth: healthCheck.overall,
                    checksPerformed: healthCheck.checks.length,
                    failedChecks: healthCheck.checks.filter(c => c.status === 'FAIL').length,
                    warnings: healthCheck.checks.filter(c => c.status === 'WARN').length,
                    recommendations: healthCheck.recommendations.length,
                },
            });

            // If health is critical, trigger immediate alert
            if (healthCheck.overall === 'CRITICAL') {
                console.warn('[SECURITY SCHEDULER] Critical health status detected');

                // Could trigger immediate notification here
                await this.handleCriticalHealthStatus(healthCheck);
            }
        } catch (error) {
            console.error('[SECURITY SCHEDULER] Failed to perform health check:', error);
            throw error;
        }
    }

    /**
     * Generate daily security report
     */
    private static async generateDailyReport(): Promise<void> {
        try {
            const now = new Date();
            const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

            const report = await SecurityAlerting.generateSecurityReport('DAILY', {
                start: yesterday,
                end: now,
            });

            console.log(`[SECURITY SCHEDULER] Generated daily security report: ${report.id}`);

            // Log report generation
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                ipAddress: 'system',
                resource: 'security_scheduler',
                action: 'daily_report_generated',
                success: true,
                details: {
                    reportId: report.id,
                    totalAlerts: report.summary.totalAlerts,
                    criticalAlerts: report.summary.criticalAlerts,
                    timeRange: report.timeRange,
                },
            });

            // Store report or send to administrators
            await this.handleDailyReport(report);
        } catch (error) {
            console.error('[SECURITY SCHEDULER] Failed to generate daily report:', error);
            throw error;
        }
    }

    /**
     * Perform log cleanup and maintenance
     */
    private static async performLogCleanup(): Promise<void> {
        try {
            const { SecurityLogger } = await import('./security-logger');

            // Clean up logs older than 90 days
            const cleanedCount = await SecurityLogger.cleanupOldLogs(90);

            console.log(`[SECURITY SCHEDULER] Cleaned up ${cleanedCount} old log entries`);

            // Log cleanup activity
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                ipAddress: 'system',
                resource: 'security_scheduler',
                action: 'log_cleanup_performed',
                success: true,
                details: {
                    cleanedEntries: cleanedCount,
                    retentionDays: 90,
                },
            });
        } catch (error) {
            console.error('[SECURITY SCHEDULER] Failed to perform log cleanup:', error);
            throw error;
        }
    }

    /**
     * Handle critical health status
     */
    private static async handleCriticalHealthStatus(healthCheck: any): Promise<void> {
        // Implementation would send immediate notifications to administrators
        // For now, just log the critical status
        console.warn('[SECURITY SCHEDULER] CRITICAL HEALTH STATUS DETECTED');
        console.warn('Failed checks:', healthCheck.checks.filter((c: any) => c.status === 'FAIL'));
        console.warn('Recommendations:', healthCheck.recommendations);

        // Could integrate with notification systems:
        // - Send email alerts
        // - Send Slack/Teams notifications
        // - Trigger PagerDuty alerts
        // - Send SMS notifications
    }

    /**
     * Handle daily report
     */
    private static async handleDailyReport(report: any): Promise<void> {
        // Implementation would store the report and/or send to administrators
        console.log('[SECURITY SCHEDULER] Daily report generated:', {
            id: report.id,
            totalAlerts: report.summary.totalAlerts,
            criticalAlerts: report.summary.criticalAlerts,
        });

        // Could integrate with reporting systems:
        // - Store in database
        // - Send via email
        // - Upload to cloud storage
        // - Post to dashboard
    }

    /**
     * Stop all scheduled tasks
     */
    static stop(): void {
        console.log('[SECURITY SCHEDULER] Stopping all scheduled tasks');

        for (const [taskName, interval] of this.intervals.entries()) {
            clearInterval(interval);
            console.log(`[SECURITY SCHEDULER] Stopped task: ${taskName}`);
        }

        this.intervals.clear();
        this.isInitialized = false;
    }

    /**
     * Get status of all scheduled tasks
     */
    static getStatus(): {
        isInitialized: boolean;
        activeTasks: string[];
        taskCount: number;
    } {
        return {
            isInitialized: this.isInitialized,
            activeTasks: Array.from(this.intervals.keys()),
            taskCount: this.intervals.size,
        };
    }

    /**
     * Manually trigger a specific task
     */
    static async triggerTask(taskName: string): Promise<boolean> {
        try {
            switch (taskName) {
                case 'alert-evaluation':
                    await this.evaluateSecurityAlerts();
                    break;
                case 'health-check':
                    await this.performHealthCheck();
                    break;
                case 'daily-report':
                    await this.generateDailyReport();
                    break;
                case 'log-cleanup':
                    await this.performLogCleanup();
                    break;
                default:
                    console.warn(`[SECURITY SCHEDULER] Unknown task: ${taskName}`);
                    return false;
            }

            console.log(`[SECURITY SCHEDULER] Manually triggered task: ${taskName}`);
            return true;
        } catch (error) {
            console.error(`[SECURITY SCHEDULER] Failed to trigger task ${taskName}:`, error);
            return false;
        }
    }

    /**
     * Update task interval
     */
    static updateTaskInterval(taskName: string, intervalMs: number): boolean {
        if (!this.intervals.has(taskName)) {
            console.warn(`[SECURITY SCHEDULER] Task not found: ${taskName}`);
            return false;
        }

        // Get the task function (this is a simplified approach)
        // In a real implementation, you'd store the task functions separately
        let taskFunction: () => Promise<void>;

        switch (taskName) {
            case 'alert-evaluation':
                taskFunction = () => this.evaluateSecurityAlerts();
                break;
            case 'health-check':
                taskFunction = () => this.performHealthCheck();
                break;
            case 'daily-report':
                taskFunction = () => this.generateDailyReport();
                break;
            case 'log-cleanup':
                taskFunction = () => this.performLogCleanup();
                break;
            default:
                return false;
        }

        // Reschedule the task with new interval
        this.scheduleTask(taskName, taskFunction, intervalMs);
        console.log(`[SECURITY SCHEDULER] Updated interval for task ${taskName}: ${intervalMs}ms`);

        return true;
    }
}

// Export convenience functions
export const initializeSecurityScheduler = () => SecurityScheduler.initialize();
export const stopSecurityScheduler = () => SecurityScheduler.stop();
export const getSchedulerStatus = () => SecurityScheduler.getStatus();
export const triggerSecurityTask = (taskName: string) => SecurityScheduler.triggerTask(taskName);