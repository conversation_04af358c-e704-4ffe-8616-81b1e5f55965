export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidPassword(password: string): boolean {
  // Minimum 12 characters as per business rules
  return password.length >= 12;
}

export function isValidIpAddress(ip: string): boolean {
  // IPv4 regex
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // IPv6 regex (simplified)
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
  
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function validateLoginForm(email: string, password: string): string[] {
  const errors: string[] = [];
  
  if (!email.trim()) {
    errors.push('Email is required');
  } else if (!isValidEmail(email)) {
    errors.push('Please enter a valid email address');
  }
  
  if (!password) {
    errors.push('Password is required');
  }
  
  return errors;
}

export function validatePasswordChange(currentPassword: string, newPassword: string, confirmPassword: string): string[] {
  const errors: string[] = [];
  
  if (!currentPassword) {
    errors.push('Current password is required');
  }
  
  if (!newPassword) {
    errors.push('New password is required');
  } else if (!isValidPassword(newPassword)) {
    errors.push('New password must be at least 12 characters long');
  }
  
  if (newPassword !== confirmPassword) {
    errors.push('New passwords do not match');
  }
  
  return errors;
}

export function validateCreateUser(email: string, password: string, role: string): string[] {
  const errors: string[] = [];
  
  if (!email.trim()) {
    errors.push('Email is required');
  } else if (!isValidEmail(email)) {
    errors.push('Please enter a valid email address');
  }
  
  if (!password) {
    errors.push('Password is required');
  } else if (!isValidPassword(password)) {
    errors.push('Password must be at least 12 characters long');
  }
  
  if (!role) {
    errors.push('Role is required');
  } else if (!['dev', 'marketing', 'sales'].includes(role)) {
    errors.push('Invalid role selected');
  }
  
  return errors;
}

export function sanitizeInput(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}