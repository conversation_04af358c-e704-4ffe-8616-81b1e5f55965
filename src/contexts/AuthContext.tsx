import { createContext, useContext, useReducer, useEffect, ReactNode, useCallback, useRef } from 'react';
import { User, AuthState, LoginCredentials, TwoFactorCredentials, ChangePasswordRequest, CreateAdminRequest, AdminCreationResult } from '../types';
import { api } from '../services/api';

// Enhanced security state interfaces
interface LockoutInfo {
  isLocked: boolean;
  lockoutExpires?: Date;
  failedAttempts: number;
  remainingTime?: number;
}

interface SecurityEvent {
  type: 'SESSION_EXPIRED' | 'FORCED_LOGOUT' | 'ACCOUNT_LOCKED' | 'SECURITY_VIOLATION' | 'SESSION_ANOMALY';
  message: string;
  timestamp: Date;
  requiresAction?: boolean;
}

interface EnhancedAuthState extends AuthState {
  lockoutInfo?: LockoutInfo;
  securityEvents: SecurityEvent[];
  sessionExpiryWarning?: {
    show: boolean;
    expiresAt: Date;
    canRefresh: boolean;
  };
  isSessionValidating: boolean;
}

interface AuthContextType extends EnhancedAuthState {
  login: (credentials: LoginCredentials) => Promise<{ requires2FA: boolean; requiresPasswordChange?: boolean; lockoutInfo?: LockoutInfo }>;
  verify2FA: (credentials: TwoFactorCredentials) => Promise<void>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  changePassword: (data: ChangePasswordRequest) => Promise<void>;
  createAdminUser: (data: CreateAdminRequest) => Promise<AdminCreationResult>;
  validateSession: () => Promise<{ isValid: boolean; requiresRefresh?: boolean }>;
  refreshSession: () => Promise<void>;
  getUserSessions: () => Promise<any[]>;
  revokeSession: (sessionId: string) => Promise<void>;
  revokeOtherSessions: () => Promise<{ revokedCount: number }>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (token: string, newPassword: string) => Promise<void>;
  getSessionAnomalies: () => Promise<any[]>;

  // Enhanced security functions
  clearSecurityEvents: () => void;
  dismissSessionWarning: () => void;
  handleSecurityEvent: (event: SecurityEvent) => void;
  checkAccountLockout: () => Promise<LockoutInfo | null>;
  startSessionMonitoring: () => void;
  stopSessionMonitoring: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'REQUIRE_2FA' }
  | { type: 'REQUIRE_PASSWORD_CHANGE'; payload: User }
  | { type: 'ACCOUNT_LOCKED'; payload: LockoutInfo }
  | { type: 'SESSION_EXPIRED' }
  | { type: 'LOGOUT' }
  | { type: 'AUTH_ERROR' }
  | { type: 'ADD_SECURITY_EVENT'; payload: SecurityEvent }
  | { type: 'CLEAR_SECURITY_EVENTS' }
  | { type: 'SET_SESSION_WARNING'; payload: { show: boolean; expiresAt?: Date; canRefresh?: boolean } }
  | { type: 'SET_SESSION_VALIDATING'; payload: boolean }
  | { type: 'UPDATE_LOCKOUT_INFO'; payload: LockoutInfo | undefined };

const initialState: EnhancedAuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  requires2FA: false,
  lockoutInfo: undefined,
  securityEvents: [],
  sessionExpiryWarning: undefined,
  isSessionValidating: false,
};

function authReducer(state: EnhancedAuthState, action: AuthAction): EnhancedAuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        requires2FA: false,
        lockoutInfo: undefined,
        sessionExpiryWarning: undefined,
      };
    case 'REQUIRE_2FA':
      return {
        ...state,
        requires2FA: true,
        isLoading: false,
      };
    case 'REQUIRE_PASSWORD_CHANGE':
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        isLoading: false,
        requires2FA: false,
        lockoutInfo: undefined,
      };
    case 'ACCOUNT_LOCKED':
      return {
        ...initialState,
        isLoading: false,
        lockoutInfo: action.payload,
        securityEvents: [
          ...state.securityEvents,
          {
            type: 'ACCOUNT_LOCKED',
            message: `Account locked due to multiple failed login attempts. Try again in ${Math.ceil((action.payload.lockoutExpires?.getTime() || 0 - Date.now()) / 60000)} minutes.`,
            timestamp: new Date(),
            requiresAction: false,
          }
        ],
      };
    case 'SESSION_EXPIRED':
      return {
        ...initialState,
        isLoading: false,
        securityEvents: [
          ...state.securityEvents,
          {
            type: 'SESSION_EXPIRED',
            message: 'Your session has expired. Please log in again.',
            timestamp: new Date(),
            requiresAction: true,
          }
        ],
      };
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'AUTH_ERROR':
      return {
        ...initialState,
        isLoading: false,
      };
    case 'ADD_SECURITY_EVENT':
      return {
        ...state,
        securityEvents: [...state.securityEvents.slice(-9), action.payload], // Keep last 10 events
      };
    case 'CLEAR_SECURITY_EVENTS':
      return {
        ...state,
        securityEvents: [],
      };
    case 'SET_SESSION_WARNING':
      return {
        ...state,
        sessionExpiryWarning: action.payload.show ? {
          show: action.payload.show,
          expiresAt: action.payload.expiresAt || new Date(),
          canRefresh: action.payload.canRefresh || false,
        } : undefined,
      };
    case 'SET_SESSION_VALIDATING':
      return {
        ...state,
        isSessionValidating: action.payload,
      };
    case 'UPDATE_LOCKOUT_INFO':
      return {
        ...state,
        lockoutInfo: action.payload,
      };
    default:
      return state;
  }
}

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [state, dispatch] = useReducer(authReducer, initialState);
  const sessionMonitorRef = useRef<NodeJS.Timeout | null>(null);
  const lockoutTimerRef = useRef<NodeJS.Timeout | null>(null);

  const login = async (credentials: LoginCredentials): Promise<{ requires2FA: boolean; requiresPasswordChange?: boolean; lockoutInfo?: LockoutInfo }> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const response = await api.login(credentials);

      if (!response.success) {
        // Handle account lockout
        if (response.error?.includes('locked') && response.data?.lockoutInfo) {
          const lockoutInfo: LockoutInfo = {
            isLocked: true,
            lockoutExpires: response.data.lockoutInfo.lockoutExpires ? new Date(response.data.lockoutInfo.lockoutExpires) : undefined,
            failedAttempts: response.data.lockoutInfo.failedAttempts || 0,
            remainingTime: response.data.lockoutInfo.remainingTime,
          };
          dispatch({ type: 'ACCOUNT_LOCKED', payload: lockoutInfo });
          startLockoutTimer(lockoutInfo);
          return { requires2FA: false, lockoutInfo };
        }
        throw new Error(response.error || 'Login failed');
      }

      if (response.data?.requires2FA) {
        dispatch({ type: 'REQUIRE_2FA' });
        return { requires2FA: true };
      }

      if (response.data?.user) {
        if (response.data.requiresPasswordChange) {
          dispatch({ type: 'REQUIRE_PASSWORD_CHANGE', payload: response.data.user });
          return { requires2FA: false, requiresPasswordChange: true };
        }

        if (response.data.sessionToken) {
          localStorage.setItem('auth_token', response.data.sessionToken);
        }
        dispatch({ type: 'LOGIN_SUCCESS', payload: response.data.user });
        startSessionMonitoring();
        return { requires2FA: false };
      }

      throw new Error('Invalid response from server');
    } catch (error) {
      dispatch({ type: 'AUTH_ERROR' });
      throw error;
    }
  };

  // Enhanced security functions
  const handleSecurityEvent = useCallback((event: SecurityEvent) => {
    dispatch({ type: 'ADD_SECURITY_EVENT', payload: event });

    // Handle specific security events
    switch (event.type) {
      case 'SESSION_EXPIRED':
        dispatch({ type: 'SESSION_EXPIRED' });
        localStorage.removeItem('auth_token');
        break;
      case 'FORCED_LOGOUT':
        logout();
        break;
      case 'ACCOUNT_LOCKED':
        dispatch({ type: 'LOGOUT' });
        localStorage.removeItem('auth_token');
        break;
    }
  }, []);

  const clearSecurityEvents = useCallback(() => {
    dispatch({ type: 'CLEAR_SECURITY_EVENTS' });
  }, []);

  const dismissSessionWarning = useCallback(() => {
    dispatch({ type: 'SET_SESSION_WARNING', payload: { show: false } });
  }, []);

  const checkAccountLockout = useCallback(async (): Promise<LockoutInfo | null> => {
    try {
      // This would be an API call to check current lockout status
      // For now, return the current lockout info from state
      return state.lockoutInfo || null;
    } catch (error) {
      console.error('Failed to check account lockout:', error);
      return null;
    }
  }, [state.lockoutInfo]);

  const startLockoutTimer = useCallback((lockoutInfo: LockoutInfo) => {
    if (lockoutTimerRef.current) {
      clearInterval(lockoutTimerRef.current);
    }

    if (lockoutInfo.lockoutExpires) {
      const updateRemainingTime = () => {
        const now = Date.now();
        const expiresAt = lockoutInfo.lockoutExpires!.getTime();
        const remainingMs = expiresAt - now;

        if (remainingMs <= 0) {
          // Lockout expired
          dispatch({ type: 'UPDATE_LOCKOUT_INFO', payload: undefined });
          if (lockoutTimerRef.current) {
            clearInterval(lockoutTimerRef.current);
            lockoutTimerRef.current = null;
          }
        } else {
          const remainingMinutes = Math.ceil(remainingMs / 60000);
          dispatch({
            type: 'UPDATE_LOCKOUT_INFO',
            payload: { ...lockoutInfo, remainingTime: remainingMinutes }
          });
        }
      };

      updateRemainingTime();
      lockoutTimerRef.current = setInterval(updateRemainingTime, 60000); // Update every minute
    }
  }, []);

  const startSessionMonitoring = useCallback(() => {
    if (sessionMonitorRef.current) {
      clearInterval(sessionMonitorRef.current);
    }

    sessionMonitorRef.current = setInterval(async () => {
      if (state.isAuthenticated && !state.isSessionValidating) {
        dispatch({ type: 'SET_SESSION_VALIDATING', payload: true });

        try {
          const result = await validateSession();

          if (!result.isValid) {
            handleSecurityEvent({
              type: 'SESSION_EXPIRED',
              message: 'Your session has expired due to inactivity.',
              timestamp: new Date(),
              requiresAction: true,
            });
          } else if (result.requiresRefresh) {
            // Show session expiry warning
            const expiresAt = new Date(Date.now() + 5 * 60 * 1000); // 5 minutes warning
            dispatch({
              type: 'SET_SESSION_WARNING',
              payload: {
                show: true,
                expiresAt,
                canRefresh: true
              }
            });
          }
        } catch (error) {
          console.error('Session validation error:', error);
          handleSecurityEvent({
            type: 'SESSION_ANOMALY',
            message: 'Session validation failed. Please log in again.',
            timestamp: new Date(),
            requiresAction: true,
          });
        } finally {
          dispatch({ type: 'SET_SESSION_VALIDATING', payload: false });
        }
      }
    }, 5 * 60 * 1000); // Check every 5 minutes
  }, [state.isAuthenticated, state.isSessionValidating, handleSecurityEvent]);

  const stopSessionMonitoring = useCallback(() => {
    if (sessionMonitorRef.current) {
      clearInterval(sessionMonitorRef.current);
      sessionMonitorRef.current = null;
    }
    if (lockoutTimerRef.current) {
      clearInterval(lockoutTimerRef.current);
      lockoutTimerRef.current = null;
    }
  }, []);

  const verify2FA = async (credentials: TwoFactorCredentials): Promise<void> => {
    dispatch({ type: 'SET_LOADING', payload: true });

    try {
      const response = await api.verify2FA(credentials);

      if (response.data?.user && response.data?.token) {
        localStorage.setItem('auth_token', response.data.token);
        dispatch({ type: 'LOGIN_SUCCESS', payload: response.data.user });
        startSessionMonitoring();
      } else {
        throw new Error('Invalid 2FA token');
      }
    } catch (error) {
      dispatch({ type: 'AUTH_ERROR' });
      throw error;
    }
  };

  const logout = async (): Promise<void> => {
    try {
      await api.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      stopSessionMonitoring();
      localStorage.removeItem('auth_token');
      dispatch({ type: 'LOGOUT' });
    }
  };

  const refreshAuth = async (): Promise<void> => {
    const token = localStorage.getItem('auth_token');

    if (!token) {
      dispatch({ type: 'SET_LOADING', payload: false });
      return;
    }

    try {
      const response = await api.getProfile();

      if (response.data?.user) {
        dispatch({ type: 'LOGIN_SUCCESS', payload: response.data.user });
        startSessionMonitoring();
      } else {
        throw new Error('Invalid token');
      }
    } catch (error) {
      localStorage.removeItem('auth_token');
      dispatch({ type: 'AUTH_ERROR' });
    }
  };

  const changePassword = async (data: ChangePasswordRequest): Promise<void> => {
    const response = await api.changePassword(data);
    if (!response.success) {
      throw new Error(response.error || 'Failed to change password');
    }
  };

  const createAdminUser = async (data: CreateAdminRequest): Promise<AdminCreationResult> => {
    const response = await api.createAdminUser(data);
    if (!response.success) {
      throw new Error(response.error || 'Failed to create admin user');
    }
    return response.data || { success: false, error: 'No data returned' };
  };

  const validateSession = async (): Promise<{ isValid: boolean; requiresRefresh?: boolean }> => {
    try {
      const response = await api.validateSession();
      if (response.success && response.data) {
        if (response.data.requiresRefresh) {
          await refreshSession();
        }
        return { isValid: true, requiresRefresh: response.data.requiresRefresh };
      }
      return { isValid: false };
    } catch (error) {
      return { isValid: false };
    }
  };

  const refreshSession = async (): Promise<void> => {
    try {
      const response = await api.refreshSession();
      if (response.success && response.data?.sessionToken) {
        localStorage.setItem('auth_token', response.data.sessionToken);
        dispatch({ type: 'LOGIN_SUCCESS', payload: response.data.user });
        dispatch({ type: 'SET_SESSION_WARNING', payload: { show: false } });

        handleSecurityEvent({
          type: 'SESSION_ANOMALY',
          message: 'Session refreshed successfully.',
          timestamp: new Date(),
          requiresAction: false,
        });
      }
    } catch (error) {
      handleSecurityEvent({
        type: 'SESSION_EXPIRED',
        message: 'Failed to refresh session. Please log in again.',
        timestamp: new Date(),
        requiresAction: true,
      });
      throw error;
    }
  };

  const getUserSessions = async (): Promise<any[]> => {
    const response = await api.getSessionList();
    if (!response.success) {
      throw new Error(response.error || 'Failed to get user sessions');
    }
    return response.data?.sessions || [];
  };

  const revokeSession = async (sessionId: string): Promise<void> => {
    const response = await api.revokeSession(sessionId);
    if (!response.success) {
      throw new Error(response.error || 'Failed to revoke session');
    }
  };

  const revokeOtherSessions = async (): Promise<{ revokedCount: number }> => {
    const response = await api.revokeOtherSessions();
    if (!response.success) {
      throw new Error(response.error || 'Failed to revoke other sessions');
    }
    return response.data || { revokedCount: 0 };
  };

  const requestPasswordReset = async (email: string): Promise<void> => {
    const response = await api.requestPasswordReset(email);
    if (!response.success) {
      throw new Error(response.error || 'Failed to request password reset');
    }
  };

  const resetPassword = async (token: string, newPassword: string): Promise<void> => {
    const response = await api.resetPassword(token, newPassword);
    if (!response.success) {
      throw new Error(response.error || 'Failed to reset password');
    }
  };

  const getSessionAnomalies = async (): Promise<any[]> => {
    const response = await api.getSessionAnomalies();
    if (!response.success) {
      throw new Error(response.error || 'Failed to get session anomalies');
    }
    return response.data?.anomalies || [];
  };

  useEffect(() => {
    refreshAuth();

    // Cleanup on unmount
    return () => {
      stopSessionMonitoring();
    };
  }, [stopSessionMonitoring]);

  // Start session monitoring when user becomes authenticated
  useEffect(() => {
    if (state.isAuthenticated && !sessionMonitorRef.current) {
      startSessionMonitoring();
    } else if (!state.isAuthenticated && sessionMonitorRef.current) {
      stopSessionMonitoring();
    }
  }, [state.isAuthenticated, startSessionMonitoring, stopSessionMonitoring]);

  const value: AuthContextType = {
    ...state,
    login,
    verify2FA,
    logout,
    refreshAuth,
    changePassword,
    createAdminUser,
    validateSession,
    refreshSession,
    getUserSessions,
    revokeSession,
    revokeOtherSessions,
    requestPasswordReset,
    resetPassword,
    getSessionAnomalies,

    // Enhanced security functions
    clearSecurityEvents,
    dismissSessionWarning,
    handleSecurityEvent,
    checkAccountLockout,
    startSessionMonitoring,
    stopSessionMonitoring,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}