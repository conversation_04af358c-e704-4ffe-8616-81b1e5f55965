
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.16.3
 * Query Engine version: bb420e667c1820a8c05a38023385f6cc7ef8e83a
 */
Prisma.prismaVersion = {
  client: "6.16.3",
  engine: "bb420e667c1820a8c05a38023385f6cc7ef8e83a"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  password: 'password',
  first_name: 'first_name',
  last_name: 'last_name',
  role: 'role',
  is_active: 'is_active',
  created_at: 'created_at',
  updated_at: 'updated_at',
  password_changed_at: 'password_changed_at',
  requires_password_change: 'requires_password_change',
  failed_login_attempts: 'failed_login_attempts',
  locked_until: 'locked_until',
  last_login_ip: 'last_login_ip',
  two_factor_secret: 'two_factor_secret',
  two_factor_enabled: 'two_factor_enabled'
};

exports.Prisma.UserProfileSettingsScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  display_name: 'display_name',
  avatar_url: 'avatar_url',
  timezone: 'timezone',
  date_format: 'date_format',
  time_format: 'time_format',
  language: 'language',
  theme: 'theme',
  email_notifications: 'email_notifications',
  security_alerts: 'security_alerts',
  system_updates: 'system_updates',
  activity_digest: 'activity_digest',
  show_activity: 'show_activity',
  session_timeout: 'session_timeout',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SystemSettingsScalarFieldEnum = {
  id: 'id',
  key: 'key',
  value: 'value',
  category: 'category',
  description: 'description',
  data_type: 'data_type',
  is_public: 'is_public',
  requires_restart: 'requires_restart',
  validation_rules: 'validation_rules',
  created_by: 'created_by',
  updated_by: 'updated_by',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.SettingsChangeLogScalarFieldEnum = {
  id: 'id',
  setting_key: 'setting_key',
  old_value: 'old_value',
  new_value: 'new_value',
  changed_by: 'changed_by',
  change_reason: 'change_reason',
  ip_address: 'ip_address',
  user_agent: 'user_agent',
  created_at: 'created_at'
};

exports.Prisma.UserSessionScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  session_token: 'session_token',
  refresh_token: 'refresh_token',
  ip_address: 'ip_address',
  user_agent: 'user_agent',
  location: 'location',
  device_fingerprint: 'device_fingerprint',
  is_active: 'is_active',
  last_activity: 'last_activity',
  expires_at: 'expires_at',
  security_flags: 'security_flags',
  max_idle_time: 'max_idle_time',
  absolute_timeout: 'absolute_timeout',
  created_at: 'created_at'
};

exports.Prisma.AdminActivityScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  action: 'action',
  resource: 'resource',
  resource_id: 'resource_id',
  details: 'details',
  ip_address: 'ip_address',
  user_agent: 'user_agent',
  created_at: 'created_at'
};

exports.Prisma.ManualTaskScalarFieldEnum = {
  id: 'id',
  title: 'title',
  description: 'description',
  status: 'status',
  priority: 'priority',
  assigned_to: 'assigned_to',
  created_by: 'created_by',
  due_date: 'due_date',
  completed_at: 'completed_at',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.AccountLockoutScalarFieldEnum = {
  id: 'id',
  email: 'email',
  failed_attempts: 'failed_attempts',
  locked_until: 'locked_until',
  last_attempt_ip: 'last_attempt_ip',
  created_at: 'created_at',
  updated_at: 'updated_at'
};

exports.Prisma.PasswordHistoryScalarFieldEnum = {
  id: 'id',
  user_id: 'user_id',
  password_hash: 'password_hash',
  created_at: 'created_at'
};

exports.Prisma.SecurityAuditLogScalarFieldEnum = {
  id: 'id',
  event_type: 'event_type',
  user_id: 'user_id',
  email: 'email',
  ip_address: 'ip_address',
  user_agent: 'user_agent',
  resource: 'resource',
  action: 'action',
  details: 'details',
  success: 'success',
  error_message: 'error_message',
  created_at: 'created_at'
};

exports.Prisma.RateLimitTrackingScalarFieldEnum = {
  id: 'id',
  key_identifier: 'key_identifier',
  request_count: 'request_count',
  window_start: 'window_start',
  expires_at: 'expires_at',
  created_at: 'created_at'
};

exports.Prisma.CsrfTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  user_id: 'user_id',
  expires_at: 'expires_at',
  created_at: 'created_at'
};

exports.Prisma.PasswordResetTokenScalarFieldEnum = {
  id: 'id',
  token: 'token',
  user_id: 'user_id',
  expires_at: 'expires_at',
  is_used: 'is_used',
  created_at: 'created_at'
};

exports.Prisma.FreemiusProductScalarFieldEnum = {
  id: 'id',
  secret_key: 'secret_key',
  public_key: 'public_key',
  created: 'created',
  updated: 'updated',
  parent_plugin_id: 'parent_plugin_id',
  developer_id: 'developer_id',
  store_id: 'store_id',
  slug: 'slug',
  title: 'title',
  environment: 'environment',
  icon: 'icon',
  default_plan_id: 'default_plan_id',
  plans: 'plans',
  features: 'features',
  money_back_period: 'money_back_period',
  refund_policy: 'refund_policy',
  annual_renewals_discount: 'annual_renewals_discount',
  renewals_discount_type: 'renewals_discount_type',
  is_released: 'is_released',
  is_sdk_required: 'is_sdk_required',
  is_pricing_visible: 'is_pricing_visible',
  is_wp_org_compliant: 'is_wp_org_compliant',
  installs_count: 'installs_count',
  active_installs_count: 'active_installs_count',
  free_releases_count: 'free_releases_count',
  premium_releases_count: 'premium_releases_count',
  total_purchases: 'total_purchases',
  total_subscriptions: 'total_subscriptions',
  total_renewals: 'total_renewals',
  total_failed_purchases: 'total_failed_purchases',
  earnings: 'earnings',
  type: 'type',
  is_static: 'is_static',
  api_last_synced_at: 'api_last_synced_at',
  api_sync_errors: 'api_sync_errors',
  last_synced_at: 'last_synced_at',
  sync_status: 'sync_status'
};

exports.Prisma.FreemiusInstallationScalarFieldEnum = {
  id: 'id',
  secret_key: 'secret_key',
  public_key: 'public_key',
  created: 'created',
  updated: 'updated',
  site_id: 'site_id',
  plugin_id: 'plugin_id',
  user_id: 'user_id',
  url: 'url',
  title: 'title',
  version: 'version',
  plan_id: 'plan_id',
  license_id: 'license_id',
  trial_plan_id: 'trial_plan_id',
  trial_ends: 'trial_ends',
  subscription_id: 'subscription_id',
  gross: 'gross',
  country_code: 'country_code',
  language: 'language',
  platform_version: 'platform_version',
  sdk_version: 'sdk_version',
  programming_language_version: 'programming_language_version',
  is_active: 'is_active',
  is_disconnected: 'is_disconnected',
  is_premium: 'is_premium',
  is_uninstalled: 'is_uninstalled',
  is_locked: 'is_locked',
  source: 'source',
  upgraded: 'upgraded',
  last_seen_at: 'last_seen_at',
  last_served_update_version: 'last_served_update_version',
  is_beta: 'is_beta',
  validation_status: 'validation_status',
  last_validated_at: 'last_validated_at',
  validation_errors: 'validation_errors',
  api_last_synced_at: 'api_last_synced_at',
  api_sync_errors: 'api_sync_errors',
  last_synced_at: 'last_synced_at',
  sync_status: 'sync_status'
};

exports.Prisma.FreemiusEventScalarFieldEnum = {
  id: 'id',
  created: 'created',
  updated: 'updated',
  type: 'type',
  developer_id: 'developer_id',
  plugin_id: 'plugin_id',
  user_id: 'user_id',
  install_id: 'install_id',
  data: 'data',
  event_trigger: 'event_trigger',
  process_time: 'process_time',
  objects: 'objects',
  received_at: 'received_at',
  processed_at: 'processed_at',
  processing_status: 'processing_status'
};

exports.Prisma.IpRegistryDataScalarFieldEnum = {
  id: 'id',
  ip_address: 'ip_address',
  country_code: 'country_code',
  country_name: 'country_name',
  continent_code: 'continent_code',
  continent_name: 'continent_name',
  region_code: 'region_code',
  region_name: 'region_name',
  city: 'city',
  postal_code: 'postal_code',
  latitude: 'latitude',
  longitude: 'longitude',
  timezone: 'timezone',
  isp: 'isp',
  organization: 'organization',
  asn: 'asn',
  asn_name: 'asn_name',
  isp_domain: 'isp_domain',
  route_prefix: 'route_prefix',
  is_threat: 'is_threat',
  threat_types: 'threat_types',
  is_bogon: 'is_bogon',
  is_cloud_provider: 'is_cloud_provider',
  is_tor: 'is_tor',
  is_proxy: 'is_proxy',
  is_vpn: 'is_vpn',
  is_malware: 'is_malware',
  is_abuser: 'is_abuser',
  is_attacker: 'is_attacker',
  is_relay: 'is_relay',
  is_tor_exit: 'is_tor_exit',
  is_anonymous: 'is_anonymous',
  connection_type: 'connection_type',
  user_agent: 'user_agent',
  hostname: 'hostname',
  ip_type: 'ip_type',
  raw_response: 'raw_response',
  created_at: 'created_at',
  updated_at: 'updated_at',
  last_refreshed_at: 'last_refreshed_at',
  data_expires_at: 'data_expires_at',
  refresh_count: 'refresh_count'
};

exports.Prisma.IpAnalysisRequestScalarFieldEnum = {
  id: 'id',
  ip_address: 'ip_address',
  installation_id: 'installation_id',
  request_source: 'request_source',
  client_ip: 'client_ip',
  user_agent: 'user_agent',
  referer: 'referer',
  request_headers: 'request_headers',
  request_body: 'request_body',
  response_body: 'response_body',
  analysis_result: 'analysis_result',
  risk_score: 'risk_score',
  recommendation: 'recommendation',
  processing_time_ms: 'processing_time_ms',
  response_time_ms: 'response_time_ms',
  used_cached_data: 'used_cached_data',
  requested_at: 'requested_at',
  completed_at: 'completed_at'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.JsonNullValueInput = {
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};
exports.UserRole = exports.$Enums.UserRole = {
  SUPER_ADMIN: 'SUPER_ADMIN',
  DEV: 'DEV',
  MARKETING: 'MARKETING',
  SALES: 'SALES'
};

exports.AdminActionType = exports.$Enums.AdminActionType = {
  CREATE_USER: 'CREATE_USER',
  UPDATE_USER: 'UPDATE_USER',
  DELETE_USER: 'DELETE_USER',
  ASSIGN_TASK: 'ASSIGN_TASK',
  COMPLETE_TASK: 'COMPLETE_TASK',
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  PASSWORD_RESET: 'PASSWORD_RESET',
  BULK_OPERATION: 'BULK_OPERATION'
};

exports.TaskStatus = exports.$Enums.TaskStatus = {
  PENDING: 'PENDING',
  IN_PROGRESS: 'IN_PROGRESS',
  COMPLETED: 'COMPLETED',
  CANCELLED: 'CANCELLED'
};

exports.TaskPriority = exports.$Enums.TaskPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT'
};

exports.Prisma.ModelName = {
  User: 'User',
  UserProfileSettings: 'UserProfileSettings',
  SystemSettings: 'SystemSettings',
  SettingsChangeLog: 'SettingsChangeLog',
  UserSession: 'UserSession',
  AdminActivity: 'AdminActivity',
  ManualTask: 'ManualTask',
  AccountLockout: 'AccountLockout',
  PasswordHistory: 'PasswordHistory',
  SecurityAuditLog: 'SecurityAuditLog',
  RateLimitTracking: 'RateLimitTracking',
  CsrfToken: 'CsrfToken',
  PasswordResetToken: 'PasswordResetToken',
  FreemiusProduct: 'FreemiusProduct',
  FreemiusInstallation: 'FreemiusInstallation',
  FreemiusEvent: 'FreemiusEvent',
  IpRegistryData: 'IpRegistryData',
  IpAnalysisRequest: 'IpAnalysisRequest'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
