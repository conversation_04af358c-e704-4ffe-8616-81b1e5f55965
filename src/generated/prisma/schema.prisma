// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id         String   @id @default(cuid())
  email      String   @unique
  password   String
  first_name String
  last_name  String
  role       UserRole @default(DEV)
  is_active  <PERSON>olean  @default(true)
  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  // Enhanced security fields
  password_changed_at      DateTime?
  requires_password_change Boolean   @default(false)
  failed_login_attempts    Int       @default(0)
  locked_until             DateTime?
  last_login_ip            String?
  two_factor_secret        String?
  two_factor_enabled       Boolean   @default(false)

  // Profile settings
  profile_settings UserProfileSettings?

  // Relations
  admin_activities        AdminActivity[]
  manual_tasks            ManualTask[]
  system_settings_created SystemSettings[]
  system_settings_updated SystemSettings[]     @relation("SystemSettingsUpdatedBy")
  settings_changes        SettingsChangeLog[]
  sessions                UserSession[]
  password_history        PasswordHistory[]
  csrf_tokens             CsrfToken[]
  password_reset_tokens   PasswordResetToken[]

  @@map("users")
}

model UserProfileSettings {
  id      String @id @default(cuid())
  user_id String @unique
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Display preferences
  display_name String?
  avatar_url   String?
  timezone     String  @default("UTC")
  date_format  String  @default("YYYY-MM-DD")
  time_format  String  @default("24h") // "12h" or "24h"
  language     String  @default("en")
  theme        String  @default("light") // "light", "dark", "auto"

  // Notification preferences
  email_notifications Boolean @default(true)
  security_alerts     Boolean @default(true)
  system_updates      Boolean @default(true)
  activity_digest     String  @default("weekly") // "never", "daily", "weekly", "monthly"

  // Privacy settings
  show_activity   Boolean @default(false) // Show activity to other admins
  session_timeout Int     @default(480) // Minutes (8 hours default)

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("user_profile_settings")
}

model SystemSettings {
  id               String  @id @default(cuid())
  key              String  @unique
  value            Json
  category         String // "security", "api", "notifications", "maintenance"
  description      String?
  data_type        String // "string", "number", "boolean", "json", "array"
  is_public        Boolean @default(false) // Can non-super-admins see this?
  requires_restart Boolean @default(false) // Does changing this require system restart?
  validation_rules Json? // JSON schema for validation

  created_by      String
  created_by_user User    @relation(fields: [created_by], references: [id])
  updated_by      String?
  updated_by_user User?   @relation("SystemSettingsUpdatedBy", fields: [updated_by], references: [id])

  created_at DateTime @default(now())
  updated_at DateTime @updatedAt

  @@map("system_settings")
}

model SettingsChangeLog {
  id              String  @id @default(cuid())
  setting_key     String
  old_value       Json?
  new_value       Json
  changed_by      String
  changed_by_user User    @relation(fields: [changed_by], references: [id])
  change_reason   String?
  ip_address      String?
  user_agent      String?

  created_at DateTime @default(now())

  @@map("settings_change_log")
}

model UserSession {
  id      String @id @default(cuid())
  user_id String
  user    User   @relation(fields: [user_id], references: [id], onDelete: Cascade)

  session_token      String  @unique
  refresh_token      String? @unique
  ip_address         String
  user_agent         String?
  location           String? // Derived from IP
  device_fingerprint String?

  is_active     Boolean  @default(true)
  last_activity DateTime @default(now())
  expires_at    DateTime

  // Enhanced security fields
  security_flags   Json? // Additional security metadata
  max_idle_time    Int   @default(28800) // 8 hours in seconds
  absolute_timeout Int   @default(86400) // 24 hours in seconds

  created_at DateTime @default(now())

  @@index([is_active, last_activity(sort: Desc)], name: "idx_user_session_active_time")
  @@index([expires_at], name: "idx_user_session_expires")
  @@map("user_sessions")
}

model AdminActivity {
  id          String          @id @default(cuid())
  user_id     String
  action      AdminActionType
  resource    String // e.g., "user", "task", "system"
  resource_id String? // ID of the affected resource
  details     Json? // Additional context about the action
  ip_address  String?
  user_agent  String?
  created_at  DateTime        @default(now())

  // Relations
  user User @relation(fields: [user_id], references: [id])

  @@index([user_id, created_at(sort: Desc)], name: "idx_admin_activity_user_time")
  @@index([action, created_at(sort: Desc)], name: "idx_admin_activity_action_time")
  @@index([resource, created_at(sort: Desc)], name: "idx_admin_activity_resource_time")
  @@map("admin_activities")
}

model ManualTask {
  id           String       @id @default(cuid())
  title        String
  description  String?
  status       TaskStatus   @default(PENDING)
  priority     TaskPriority @default(MEDIUM)
  assigned_to  String
  created_by   String
  due_date     DateTime?
  completed_at DateTime?
  created_at   DateTime     @default(now())
  updated_at   DateTime     @updatedAt

  // Relations
  user User @relation(fields: [assigned_to], references: [id])

  @@map("manual_tasks")
}

// New security-focused tables

model AccountLockout {
  id              String    @id @default(cuid())
  email           String    @unique
  failed_attempts Int       @default(0)
  locked_until    DateTime?
  last_attempt_ip String
  created_at      DateTime  @default(now())
  updated_at      DateTime  @updatedAt

  @@map("account_lockouts")
}

model PasswordHistory {
  id            String   @id @default(cuid())
  user_id       String
  user          User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  password_hash String
  created_at    DateTime @default(now())

  @@map("password_history")
}

model SecurityAuditLog {
  id            String   @id @default(cuid())
  event_type    String
  user_id       String?
  email         String?
  ip_address    String
  user_agent    String?
  resource      String?
  action        String?
  details       Json?
  success       Boolean
  error_message String?
  created_at    DateTime @default(now())

  @@index([created_at(sort: Desc), event_type], name: "idx_security_audit_log_time_type")
  @@index([user_id, created_at(sort: Desc)], name: "idx_security_audit_log_user_time")
  @@index([ip_address, created_at(sort: Desc)], name: "idx_security_audit_log_ip_time")
  @@index([success, created_at(sort: Desc)], name: "idx_security_audit_log_success_time")
  @@map("security_audit_log")
}

model RateLimitTracking {
  id             String   @id @default(cuid())
  key_identifier String
  request_count  Int      @default(1)
  window_start   DateTime
  expires_at     DateTime
  created_at     DateTime @default(now())

  @@unique([key_identifier, window_start])
  @@index([expires_at], name: "idx_rate_limit_tracking_expires")
  @@index([key_identifier, window_start(sort: Desc)], name: "idx_rate_limit_tracking_key_window")
  @@map("rate_limit_tracking")
}

model CsrfToken {
  id         String   @id @default(cuid())
  token      String   @unique
  user_id    String
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  expires_at DateTime
  created_at DateTime @default(now())

  @@map("csrf_tokens")
}

model PasswordResetToken {
  id         String   @id @default(cuid())
  token      String   @unique
  user_id    String
  user       User     @relation(fields: [user_id], references: [id], onDelete: Cascade)
  expires_at DateTime
  is_used    Boolean  @default(false)
  created_at DateTime @default(now())

  @@map("password_reset_tokens")
}

// GuardGeo Platform Models

model FreemiusProduct {
  id                       String    @id // Freemius product ID
  secret_key               String?
  public_key               String?
  created                  DateTime
  updated                  DateTime?
  parent_plugin_id         String?
  developer_id             String
  store_id                 String
  slug                     String
  title                    String
  environment              Int       @default(0)
  icon                     String?
  default_plan_id          String?
  plans                    String? // Comma-separated plan IDs
  features                 String? // Comma-separated feature IDs
  money_back_period        Int       @default(0)
  refund_policy            String? // "flexible", "moderate", "strict"
  annual_renewals_discount Int?
  renewals_discount_type   String? // "percentage", "dollar"
  is_released              Boolean   @default(true)
  is_sdk_required          Boolean   @default(true)
  is_pricing_visible       Boolean   @default(true)
  is_wp_org_compliant      Boolean   @default(true)
  installs_count           Int       @default(0)
  active_installs_count    Int       @default(0)
  free_releases_count      Int       @default(0)
  premium_releases_count   Int       @default(0)
  total_purchases          Int       @default(0)
  total_subscriptions      Int       @default(0)
  total_renewals           Int       @default(0)
  total_failed_purchases   Int       @default(0)
  earnings                 Float     @default(0)
  type                     String // plugin, theme, widget, template
  is_static                Boolean   @default(false)

  // Enhanced tracking fields for API synchronization
  api_last_synced_at DateTime?
  api_sync_errors    Json? // Store sync error details
  last_synced_at     DateTime  @default(now())
  sync_status        String    @default("active") // active, error, pending

  // Relations
  installations FreemiusInstallation[]
  events        FreemiusEvent[]

  @@map("freemius_products")
}

model FreemiusInstallation {
  id                           String    @id // Freemius installation ID
  secret_key                   String?
  public_key                   String?
  created                      DateTime
  updated                      DateTime?
  site_id                      String
  plugin_id                    String
  user_id                      String
  url                          String?
  title                        String?
  version                      String
  plan_id                      String?
  license_id                   String?
  trial_plan_id                String?
  trial_ends                   DateTime?
  subscription_id              String?
  gross                        Float     @default(0)
  country_code                 String?
  language                     String?
  platform_version             String?
  sdk_version                  String?
  programming_language_version String?
  is_active                    Boolean   @default(true)
  is_disconnected              Boolean   @default(false)
  is_premium                   Boolean   @default(false)
  is_uninstalled               Boolean   @default(false)
  is_locked                    Boolean   @default(false)
  source                       Int       @default(0)
  upgraded                     DateTime?
  last_seen_at                 DateTime?
  last_served_update_version   String?
  is_beta                      Boolean   @default(false)

  // Enhanced validation tracking fields
  validation_status String    @default("pending") // "valid", "invalid", "pending"
  last_validated_at DateTime?
  validation_errors Json? // Store validation error details

  // Enhanced tracking fields for API synchronization
  api_last_synced_at DateTime?
  api_sync_errors    Json? // Store sync error details
  last_synced_at     DateTime  @default(now())
  sync_status        String    @default("active") // active, error, pending

  // Relations
  product     FreemiusProduct     @relation(fields: [plugin_id], references: [id])
  events      FreemiusEvent[]
  ip_requests IpAnalysisRequest[]

  @@index([is_active, is_premium, created(sort: Desc)], name: "idx_freemius_installation_active_premium")
  @@index([plugin_id, is_active, last_seen_at(sort: Desc)], name: "idx_freemius_installation_product_active")
  @@index([country_code, is_active], name: "idx_freemius_installation_country")
  @@index([version, is_active], name: "idx_freemius_installation_version")
  @@index([validation_status, last_validated_at(sort: Desc)], name: "idx_freemius_installation_validation")
  @@map("freemius_installations")
}

model FreemiusEvent {
  id            String    @id // Freemius event ID
  created       DateTime
  updated       DateTime?
  type          String // Event type (license.activated, etc.)
  developer_id  String
  plugin_id     String?
  user_id       String?
  install_id    String?
  data          Json? // Event-specific data
  event_trigger String // system, developer, plugin, user, install
  process_time  DateTime?
  objects       Json? // Related objects (user, install, license, payment)

  // Local tracking fields
  received_at       DateTime  @default(now())
  processed_at      DateTime?
  processing_status String    @default("pending") // pending, processed, error

  // Relations
  product      FreemiusProduct?      @relation(fields: [plugin_id], references: [id])
  installation FreemiusInstallation? @relation(fields: [install_id], references: [id])

  @@index([type, created(sort: Desc)], name: "idx_freemius_event_type_time")
  @@index([install_id, created(sort: Desc)], name: "idx_freemius_event_install_time")
  @@index([plugin_id, created(sort: Desc)], name: "idx_freemius_event_product_time")
  @@map("freemius_events")
}

model IpRegistryData {
  id         String @id @default(cuid())
  ip_address String @unique

  // Core geolocation data
  country_code   String?
  country_name   String?
  continent_code String? // Enhanced: continent code from ipRegistry
  continent_name String? // Enhanced: continent name from ipRegistry
  region_code    String?
  region_name    String?
  city           String?
  postal_code    String?
  latitude       Float?
  longitude      Float?
  timezone       String?

  // ISP and network data
  isp          String?
  organization String?
  asn          String?
  asn_name     String?
  isp_domain   String? // Enhanced: ISP domain from connection.domain
  route_prefix String? // Enhanced: route prefix from connection.route

  // Security and threat data
  is_threat         Boolean @default(false)
  threat_types      Json? // Array of threat types
  is_bogon          Boolean @default(false)
  is_cloud_provider Boolean @default(false)
  is_tor            Boolean @default(false)
  is_proxy          Boolean @default(false)
  is_vpn            Boolean @default(false)
  is_malware        Boolean @default(false)
  is_abuser         Boolean @default(false) // Enhanced: from security.is_abuser
  is_attacker       Boolean @default(false) // Enhanced: from security.is_attacker
  is_relay          Boolean @default(false) // Enhanced: from security.is_relay
  is_tor_exit       Boolean @default(false) // Enhanced: from security.is_tor_exit
  is_anonymous      Boolean @default(false) // Enhanced: from security.is_anonymous

  // Additional metadata
  connection_type String?
  user_agent      String?
  hostname        String? // Enhanced: hostname from ipRegistry
  ip_type         String? // Enhanced: IPv4/IPv6 type

  // Complete ipRegistry API response storage
  raw_response Json // Enhanced: Complete API response - now required

  // Data freshness tracking
  created_at        DateTime @default(now())
  updated_at        DateTime @updatedAt
  last_refreshed_at DateTime @default(now())
  data_expires_at   DateTime // 3 days from last refresh
  refresh_count     Int      @default(1)

  // Relations
  analysis_requests IpAnalysisRequest[]

  @@index([data_expires_at], name: "idx_ip_registry_data_expires")
  @@index([country_code, last_refreshed_at(sort: Desc)], name: "idx_ip_registry_data_country")
  @@index([continent_code, last_refreshed_at(sort: Desc)], name: "idx_ip_registry_data_continent")
  @@index([is_threat, last_refreshed_at(sort: Desc)], name: "idx_ip_registry_data_threat")
  @@index([is_cloud_provider, last_refreshed_at(sort: Desc)], name: "idx_ip_registry_data_cloud")
  @@map("ip_registry_data")
}

model IpAnalysisRequest {
  id              String  @id @default(cuid())
  ip_address      String
  installation_id String?

  // Enhanced request metadata
  request_source  String // "plugin", "admin", "webhook"
  client_ip       String? // Enhanced: IP of the requesting client
  user_agent      String?
  referer         String?
  request_headers Json? // Enhanced: Store relevant headers

  // Enhanced request/response logging
  request_body  Json? // Enhanced: Original request from plugin
  response_body Json? // Enhanced: Response sent to plugin

  // Analysis results
  analysis_result Json? // Processed analysis data
  risk_score      Float? // Calculated risk score (0-100)
  recommendation  String? // "allow", "block", "review"

  // Enhanced performance tracking
  processing_time_ms Int?
  response_time_ms   Int? // Enhanced: Total response time
  used_cached_data   Boolean @default(false) // Used existing DB data vs fresh API call

  // Timestamps
  requested_at DateTime  @default(now())
  completed_at DateTime?

  // Relations
  installation FreemiusInstallation? @relation(fields: [installation_id], references: [id])
  ip_data      IpRegistryData?       @relation(fields: [ip_address], references: [ip_address])

  @@index([requested_at(sort: Desc), request_source], name: "idx_ip_analysis_request_time_source")
  @@index([ip_address, requested_at(sort: Desc)], name: "idx_ip_analysis_request_ip_time")
  @@index([installation_id, requested_at(sort: Desc)], name: "idx_ip_analysis_request_installation")
  @@index([risk_score, requested_at(sort: Desc)], name: "idx_ip_analysis_request_risk_score")
  @@index([client_ip, requested_at(sort: Desc)], name: "idx_ip_analysis_request_client_ip")
  @@index([response_time_ms, requested_at(sort: Desc)], name: "idx_ip_analysis_request_performance")
  @@map("ip_analysis_requests")
}

enum UserRole {
  SUPER_ADMIN
  DEV
  MARKETING
  SALES
}

enum AdminActionType {
  CREATE_USER
  UPDATE_USER
  DELETE_USER
  ASSIGN_TASK
  COMPLETE_TASK
  UPDATE_SETTINGS
  LOGIN
  LOGOUT
  PASSWORD_RESET
  BULK_OPERATION
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}
