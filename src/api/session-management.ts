import { Request, Response } from 'express';
import { PrismaSessionStore } from '../lib/prisma-session-store';
import { SessionValidator, sessionValidator } from '../lib/session-validator';
import { SecurityLogger, SecurityEventType } from '../lib/security-logger';
import { Logger } from '../lib/logger';
import { ApiResponse } from '../types';

// Helper function to get client IP
function getClientIp(req: Request): string {
    const forwarded = req.headers['x-forwarded-for'];
    const realIp = req.headers['x-real-ip'];
    const cfConnectingIp = req.headers['cf-connecting-ip'];

    if (typeof forwarded === 'string') {
        return forwarded.split(',')[0].trim();
    }

    if (typeof realIp === 'string') {
        return realIp;
    }

    if (typeof cfConnectingIp === 'string') {
        return cfConnectingIp;
    }

    return req.connection?.remoteAddress ||
        req.socket?.remoteAddress ||
        '127.0.0.1';
}

// Helper function to get user agent
function getUserAgent(req: Request): string {
    return req.headers['user-agent'] || 'Unknown';
}

/**
 * Get all active sessions for the current user
 * GET /api/v1/admin/sessions/my-sessions
 */
export async function getMySessionsHandler(req: Request, res: Response): Promise<void> {
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionStore = new PrismaSessionStore();
        const userSessions = await sessionStore.getUserSessions(req.user.id);

        // Enhance session data with security information
        const enhancedSessions = userSessions.map(session => ({
            sessionId: session.userId + '_' + session.createdAt.getTime(), // Safe session identifier
            ipAddress: session.ipAddress,
            userAgent: session.userAgent,
            location: session.location,
            createdAt: session.createdAt,
            lastActivity: session.lastActivity,
            isCurrent: session.ipAddress === clientIp && session.userAgent === userAgent,
            deviceInfo: {
                fingerprint: session.deviceFingerprint?.substring(0, 8) + '...' || 'Unknown',
                platform: session.userAgent ? extractPlatform(session.userAgent) : 'Unknown'
            },
            securityFlags: {
                hasSecurityEvents: session.securityFlags &&
                    (session.securityFlags.ip_change_detected ||
                        session.securityFlags.fingerprint_change_detected),
                creationMethod: session.securityFlags?.created_at ? 'normal' : 'legacy'
            }
        }));

        // Log session access
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.AUTHENTICATION_SUCCESS,
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_sessions',
            action: 'list',
            success: true,
            details: {
                sessionCount: enhancedSessions.length,
                endpoint: '/api/v1/admin/sessions/my-sessions'
            }
        });

        res.status(200).json({
            success: true,
            data: {
                sessions: enhancedSessions,
                currentSession: {
                    ipAddress: clientIp,
                    userAgent: userAgent
                },
                totalSessions: enhancedSessions.length
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Get my sessions handler error', error, {
            userId: req.user?.id
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_sessions',
            action: 'list_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                endpoint: '/api/v1/admin/sessions/my-sessions'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to retrieve sessions'
        } as ApiResponse);
    }
}

/**
 * Revoke a specific session (not the current one)
 * DELETE /api/v1/admin/sessions/revoke/:sessionId
 */
export async function revokeSessionHandler(req: Request, res: Response): Promise<void> {
    const { sessionId } = req.params;
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        if (!sessionId) {
            res.status(400).json({
                success: false,
                error: 'Session ID is required'
            } as ApiResponse);
            return;
        }

        // Prevent users from revoking their current session via this endpoint
        if (req.session?.sessionToken === sessionId) {
            res.status(400).json({
                success: false,
                error: 'Cannot revoke current session. Use logout instead.'
            } as ApiResponse);
            return;
        }

        const sessionStore = new PrismaSessionStore();

        // Verify the session belongs to the current user before revoking
        const userSessions = await sessionStore.getUserSessions(req.user.id);
        const targetSession = userSessions.find(s =>
            (s.userId + '_' + s.createdAt.getTime()) === sessionId
        );

        if (!targetSession) {
            res.status(404).json({
                success: false,
                error: 'Session not found or does not belong to you'
            } as ApiResponse);
            return;
        }

        // Find the actual session token to revoke
        // This is a bit complex because we're using a safe identifier
        // In a real implementation, you might want to store session IDs differently
        const allSessions = await sessionStore.allAsync();
        let sessionTokenToRevoke: string | null = null;

        if (allSessions) {
            for (const [token, sessionData] of Object.entries(allSessions)) {
                if (sessionData.userId === req.user.id &&
                    sessionData.createdAt.getTime() === targetSession.createdAt.getTime()) {
                    sessionTokenToRevoke = token;
                    break;
                }
            }
        }

        if (!sessionTokenToRevoke) {
            res.status(404).json({
                success: false,
                error: 'Session token not found'
            } as ApiResponse);
            return;
        }

        // Revoke the session
        await sessionStore.destroyAsync(sessionTokenToRevoke);

        // Log session revocation
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.LOGOUT,
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_session',
            action: 'revoke',
            success: true,
            details: {
                revokedSessionId: sessionId,
                revokedSessionIp: targetSession.ipAddress,
                revokedSessionUserAgent: targetSession.userAgent,
                revocationReason: 'user_requested',
                endpoint: '/api/v1/admin/sessions/revoke'
            }
        });

        res.status(200).json({
            success: true,
            message: 'Session revoked successfully',
            data: {
                revokedSessionId: sessionId,
                revokedAt: new Date().toISOString()
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Revoke session handler error', error, {
            userId: req.user?.id,
            sessionId
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_session',
            action: 'revoke_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                sessionId,
                endpoint: '/api/v1/admin/sessions/revoke'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to revoke session'
        } as ApiResponse);
    }
}

/**
 * Revoke all other sessions (keep current session active)
 * POST /api/v1/admin/sessions/revoke-all-others
 */
export async function revokeAllOtherSessionsHandler(req: Request, res: Response): Promise<void> {
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!req.user || !req.session) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        const sessionStore = new PrismaSessionStore();
        const revokedCount = await sessionStore.invalidateUserSessions(
            req.user.id,
            req.session.sessionToken
        );

        // Log mass session revocation
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.LOGOUT,
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_sessions',
            action: 'revoke_all_others',
            success: true,
            details: {
                revokedSessionCount: revokedCount,
                currentSessionPreserved: req.session.sessionToken.substring(0, 8) + '...',
                revocationReason: 'user_requested_security_action',
                endpoint: '/api/v1/admin/sessions/revoke-all-others'
            }
        });

        res.status(200).json({
            success: true,
            message: `Successfully revoked ${revokedCount} other sessions`,
            data: {
                revokedSessionCount: revokedCount,
                currentSessionPreserved: true,
                revokedAt: new Date().toISOString()
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Revoke all other sessions handler error', error, {
            userId: req.user?.id
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_sessions',
            action: 'revoke_all_others_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                endpoint: '/api/v1/admin/sessions/revoke-all-others'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to revoke other sessions'
        } as ApiResponse);
    }
}

/**
 * Get session security metrics and statistics
 * GET /api/v1/admin/sessions/security-metrics
 */
export async function getSessionSecurityMetricsHandler(req: Request, res: Response): Promise<void> {
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);
    const { timeframe = 'day' } = req.query;

    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        // Only allow Super Admins to view system-wide metrics
        if (req.user.role !== 'SUPER_ADMIN') {
            res.status(403).json({
                success: false,
                error: 'Insufficient permissions to view security metrics'
            } as ApiResponse);
            return;
        }

        const validTimeframes = ['hour', 'day', 'week'];
        const selectedTimeframe = validTimeframes.includes(timeframe as string)
            ? timeframe as 'hour' | 'day' | 'week'
            : 'day';

        const metrics = await sessionValidator.getValidationStatistics(selectedTimeframe);

        // Get additional session store metrics
        const sessionStore = new PrismaSessionStore();
        const totalActiveSessions = await sessionStore.lengthAsync();

        // Log metrics access
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.AUTHENTICATION_SUCCESS,
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'session_security_metrics',
            action: 'view',
            success: true,
            details: {
                timeframe: selectedTimeframe,
                endpoint: '/api/v1/admin/sessions/security-metrics'
            }
        });

        res.status(200).json({
            success: true,
            data: {
                timeframe: selectedTimeframe,
                sessionMetrics: {
                    totalActiveSessions,
                    ...metrics
                },
                generatedAt: new Date().toISOString()
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Get session security metrics handler error', error, {
            userId: req.user?.id,
            timeframe
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'session_security_metrics',
            action: 'view_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                timeframe,
                endpoint: '/api/v1/admin/sessions/security-metrics'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to retrieve security metrics'
        } as ApiResponse);
    }
}

/**
 * Get all user sessions (Super Admin only)
 * GET /api/v1/admin/sessions/all-sessions
 */
export async function getAllSessionsHandler(req: Request, res: Response): Promise<void> {
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);
    const { page = '1', limit = '50', userId } = req.query;

    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        // Only allow Super Admins to view all sessions
        if (req.user.role !== 'SUPER_ADMIN') {
            res.status(403).json({
                success: false,
                error: 'Insufficient permissions to view all sessions'
            } as ApiResponse);
            return;
        }

        const pageNum = Math.max(1, parseInt(page as string) || 1);
        const limitNum = Math.min(100, Math.max(1, parseInt(limit as string) || 50));

        const sessionStore = new PrismaSessionStore();
        const allSessions = await sessionStore.allAsync();

        if (!allSessions) {
            res.status(200).json({
                success: true,
                data: {
                    sessions: [],
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total: 0,
                        totalPages: 0
                    }
                }
            } as ApiResponse);
            return;
        }

        // Convert to array and filter by userId if specified
        let sessionArray = Object.entries(allSessions).map(([token, data]) => ({
            sessionToken: token.substring(0, 8) + '...',
            userId: data.userId,
            email: data.email,
            role: data.role,
            ipAddress: data.ipAddress,
            userAgent: data.userAgent,
            location: data.location,
            createdAt: data.createdAt,
            lastActivity: data.lastActivity,
            deviceInfo: {
                fingerprint: data.deviceFingerprint?.substring(0, 8) + '...' || 'Unknown'
            },
            securityFlags: data.securityFlags
        }));

        if (userId && typeof userId === 'string') {
            sessionArray = sessionArray.filter(session => session.userId === userId);
        }

        // Sort by last activity (most recent first)
        sessionArray.sort((a, b) => b.lastActivity.getTime() - a.lastActivity.getTime());

        // Paginate results
        const total = sessionArray.length;
        const totalPages = Math.ceil(total / limitNum);
        const startIndex = (pageNum - 1) * limitNum;
        const endIndex = startIndex + limitNum;
        const paginatedSessions = sessionArray.slice(startIndex, endIndex);

        // Log admin session access
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_CREATED, // Using closest available event type
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'all_user_sessions',
            action: 'admin_view',
            success: true,
            details: {
                totalSessions: total,
                filteredUserId: userId,
                page: pageNum,
                limit: limitNum,
                endpoint: '/api/v1/admin/sessions/all-sessions'
            }
        });

        res.status(200).json({
            success: true,
            data: {
                sessions: paginatedSessions,
                pagination: {
                    page: pageNum,
                    limit: limitNum,
                    total,
                    totalPages
                },
                filters: {
                    userId: userId || null
                }
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Get all sessions handler error', error, {
            userId: req.user?.id,
            page,
            limit,
            filterUserId: userId
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'all_user_sessions',
            action: 'admin_view_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                page,
                limit,
                filterUserId: userId,
                endpoint: '/api/v1/admin/sessions/all-sessions'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to retrieve sessions'
        } as ApiResponse);
    }
}

/**
 * Force revoke a user's session (Super Admin only)
 * DELETE /api/v1/admin/sessions/force-revoke/:userId/:sessionId
 */
export async function forceRevokeSessionHandler(req: Request, res: Response): Promise<void> {
    const { userId, sessionId } = req.params;
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        // Only allow Super Admins to force revoke sessions
        if (req.user.role !== 'SUPER_ADMIN') {
            res.status(403).json({
                success: false,
                error: 'Insufficient permissions to force revoke sessions'
            } as ApiResponse);
            return;
        }

        if (!userId || !sessionId) {
            res.status(400).json({
                success: false,
                error: 'User ID and Session ID are required'
            } as ApiResponse);
            return;
        }

        const sessionStore = new PrismaSessionStore();

        // Find the session to revoke
        const allSessions = await sessionStore.allAsync();
        let sessionTokenToRevoke: string | null = null;
        let targetSessionData: any = null;

        if (allSessions) {
            for (const [token, sessionData] of Object.entries(allSessions)) {
                const sessionIdentifier = sessionData.userId + '_' + sessionData.createdAt.getTime();
                if (sessionData.userId === userId && sessionIdentifier === sessionId) {
                    sessionTokenToRevoke = token;
                    targetSessionData = sessionData;
                    break;
                }
            }
        }

        if (!sessionTokenToRevoke || !targetSessionData) {
            res.status(404).json({
                success: false,
                error: 'Session not found'
            } as ApiResponse);
            return;
        }

        // Revoke the session
        await sessionStore.destroyAsync(sessionTokenToRevoke);

        // Log admin session revocation
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_CREATED, // Using closest available event type
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_session',
            action: 'admin_force_revoke',
            success: true,
            details: {
                targetUserId: userId,
                targetUserEmail: targetSessionData.email,
                revokedSessionId: sessionId,
                revokedSessionIp: targetSessionData.ipAddress,
                revokedSessionUserAgent: targetSessionData.userAgent,
                revocationReason: 'admin_forced_revocation',
                adminAction: true,
                endpoint: '/api/v1/admin/sessions/force-revoke'
            }
        });

        res.status(200).json({
            success: true,
            message: 'Session forcefully revoked by administrator',
            data: {
                revokedUserId: userId,
                revokedSessionId: sessionId,
                revokedAt: new Date().toISOString(),
                revokedBy: req.user.email
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Force revoke session handler error', error, {
            adminUserId: req.user?.id,
            targetUserId: userId,
            sessionId
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'user_session',
            action: 'admin_force_revoke_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                targetUserId: userId,
                sessionId,
                endpoint: '/api/v1/admin/sessions/force-revoke'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to revoke session'
        } as ApiResponse);
    }
}

/**
 * Perform manual session cleanup (Super Admin only)
 * POST /api/v1/admin/sessions/cleanup
 */
export async function manualSessionCleanupHandler(req: Request, res: Response): Promise<void> {
    const clientIp = getClientIp(req);
    const userAgent = getUserAgent(req);

    try {
        if (!req.user) {
            res.status(401).json({
                success: false,
                error: 'Authentication required'
            } as ApiResponse);
            return;
        }

        // Only allow Super Admins to perform manual cleanup
        if (req.user.role !== 'SUPER_ADMIN') {
            res.status(403).json({
                success: false,
                error: 'Insufficient permissions to perform session cleanup'
            } as ApiResponse);
            return;
        }

        const sessionStore = new PrismaSessionStore();
        const cleanupResult = await sessionStore.cleanupExpiredSessions();

        // Log manual cleanup action
        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.ADMIN_USER_CREATED, // Using closest available event type
            userId: req.user.id,
            email: req.user.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'session_cleanup',
            action: 'manual_cleanup',
            success: true,
            details: {
                expiredSessions: cleanupResult.expiredSessions,
                idleTimeoutSessions: cleanupResult.idleTimeoutSessions,
                totalCleaned: cleanupResult.totalCleaned,
                cleanupType: 'manual_admin_triggered',
                endpoint: '/api/v1/admin/sessions/cleanup'
            }
        });

        res.status(200).json({
            success: true,
            message: 'Session cleanup completed successfully',
            data: {
                expiredSessions: cleanupResult.expiredSessions,
                idleTimeoutSessions: cleanupResult.idleTimeoutSessions,
                totalCleaned: cleanupResult.totalCleaned,
                cleanupAt: new Date().toISOString(),
                performedBy: req.user.email
            }
        } as ApiResponse);

    } catch (error) {
        Logger.error('Manual session cleanup handler error', error, {
            userId: req.user?.id
        });

        await SecurityLogger.logSecurityEvent({
            eventType: SecurityEventType.SECURITY_ALERT_TRIGGERED,
            userId: req.user?.id,
            email: req.user?.email,
            ipAddress: clientIp,
            userAgent,
            resource: 'session_cleanup',
            action: 'manual_cleanup_error',
            success: false,
            errorMessage: error instanceof Error ? error.message : 'Unknown error',
            details: {
                component: 'session_management',
                endpoint: '/api/v1/admin/sessions/cleanup'
            }
        });

        res.status(500).json({
            success: false,
            error: 'Failed to perform session cleanup'
        } as ApiResponse);
    }
}

/**
 * Helper function to extract platform from user agent
 */
function extractPlatform(userAgent: string): string {
    const ua = userAgent.toLowerCase();

    if (ua.includes('windows')) return 'Windows';
    if (ua.includes('macintosh') || ua.includes('mac os')) return 'macOS';
    if (ua.includes('linux')) return 'Linux';
    if (ua.includes('android')) return 'Android';
    if (ua.includes('iphone') || ua.includes('ipad')) return 'iOS';
    if (ua.includes('chrome os')) return 'Chrome OS';

    return 'Unknown';
}