import { Request, Response } from 'express';
import { FreemiusService } from '../services/freemius-service';
import { IpIntelligenceService } from '../services/ip-intelligence-service';
import { prisma } from '../lib/prisma';
import { Logger } from '../lib/logger';
import { v4 as uuidv4 } from 'uuid';

// Request validation interface
interface AnalyzeRequest {
    ip: string;
    visitor_hash: string;
    plugin_id: number;
    install_id: number;
    url: string;
}

// Response interface
interface AnalyzeResponse {
    success: boolean;
    data?: any; // Complete ipRegistry response
    error?: string;
    request_body: AnalyzeRequest;
    request_id: string;
}

// Validation result interface
interface ValidationResult {
    isValid: boolean;
    errors: string[];
}

/**
 * Validate analyze request parameters
 */
function validateAnalyzeRequest(body: any): ValidationResult {
    const errors: string[] = [];

    // Check required fields
    if (!body.ip || typeof body.ip !== 'string') {
        errors.push('ip is required and must be a string');
    }

    if (!body.visitor_hash || typeof body.visitor_hash !== 'string') {
        errors.push('visitor_hash is required and must be a string');
    }

    if (!body.plugin_id || typeof body.plugin_id !== 'number') {
        errors.push('plugin_id is required and must be a number');
    }

    if (!body.install_id || typeof body.install_id !== 'number') {
        errors.push('install_id is required and must be a number');
    }

    if (!body.url || typeof body.url !== 'string') {
        errors.push('url is required and must be a string');
    }

    // Additional validation
    if (body.ip && typeof body.ip === 'string') {
        // Basic IP format validation
        const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
        if (!ipRegex.test(body.ip.trim())) {
            errors.push('ip must be a valid IPv4 or IPv6 address');
        }
    }

    if (body.url && typeof body.url === 'string') {
        try {
            new URL(body.url);
        } catch {
            errors.push('url must be a valid URL');
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Check for duplicate requests within 24 hours
 */
async function checkDuplicateRequest(ip: string, installId: number): Promise<boolean> {
    try {
        const twentyFourHoursAgo = new Date();
        twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

        const existingRequest = await prisma.ipAnalysisRequest.findFirst({
            where: {
                ip_address: ip,
                installation_id: installId.toString(),
                requested_at: {
                    gte: twentyFourHoursAgo
                }
            }
        });

        return existingRequest !== null;
    } catch (error) {
        Logger.error('Failed to check duplicate request', error, {
            ip,
            installId
        }, {
            component: 'analyze-endpoint',
            operation: 'duplicate_check'
        });
        return false; // Allow request if check fails
    }
}

/**
 * POST /api/v1/analyze endpoint handler
 * Simple proxy to ipRegistry with Freemius validation
 */
export async function analyzeHandler(req: Request, res: Response): Promise<void> {
    const requestId = uuidv4();
    const startTime = Date.now();
    const clientIp = req.ip || req.connection.remoteAddress || 'unknown';

    try {
        Logger.info('Analyze endpoint request received', {
            requestId,
            clientIp,
            userAgent: req.headers['user-agent'],
            body: req.body
        }, {
            component: 'analyze-endpoint',
            operation: 'request_received'
        });

        // Validate request parameters
        const validation = validateAnalyzeRequest(req.body);
        if (!validation.isValid) {
            const response: AnalyzeResponse = {
                success: false,
                error: `Validation failed: ${validation.errors.join(', ')}`,
                request_body: req.body,
                request_id: requestId
            };

            Logger.warn('Analyze request validation failed', {
                requestId,
                errors: validation.errors,
                body: req.body
            }, {
                component: 'analyze-endpoint',
                operation: 'validation_failed'
            });

            res.status(400).json(response);
            return;
        }

        const { ip, visitor_hash, plugin_id, install_id, url } = req.body as AnalyzeRequest;

        // Initialize services
        const freemiusService = new FreemiusService();
        const ipIntelligenceService = new IpIntelligenceService();

        // Check if IP is valid and public
        if (!ipIntelligenceService.isValidPublicIp(ip)) {
            const response: AnalyzeResponse = {
                success: false,
                error: 'Invalid or private IP address',
                request_body: req.body,
                request_id: requestId
            };

            Logger.warn('Invalid or private IP address rejected', {
                requestId,
                ip,
                installId: install_id
            }, {
                component: 'analyze-endpoint',
                operation: 'invalid_ip'
            });

            res.status(400).json(response);
            return;
        }

        // Check for duplicate requests within 24 hours
        const isDuplicate = await checkDuplicateRequest(ip, install_id);
        if (isDuplicate) {
            const response: AnalyzeResponse = {
                success: false,
                error: 'Duplicate request detected. Please wait 24 hours before requesting analysis for the same IP and installation.',
                request_body: req.body,
                request_id: requestId
            };

            Logger.warn('Duplicate request rejected', {
                requestId,
                ip,
                installId: install_id
            }, {
                component: 'analyze-endpoint',
                operation: 'duplicate_request'
            });

            res.status(429).json(response);
            return;
        }

        // Validate Freemius installation
        const installation = await freemiusService.getInstallation(install_id.toString());
        if (!installation) {
            const response: AnalyzeResponse = {
                success: false,
                error: 'Installation not found or inactive',
                request_body: req.body,
                request_id: requestId
            };

            Logger.warn('Installation not found', {
                requestId,
                installId: install_id,
                pluginId: plugin_id
            }, {
                component: 'analyze-endpoint',
                operation: 'installation_not_found'
            });

            res.status(404).json(response);
            return;
        }

        // Validate installation belongs to correct plugin
        if (installation.plugin_id !== plugin_id.toString()) {
            const response: AnalyzeResponse = {
                success: false,
                error: 'Installation does not belong to the specified plugin',
                request_body: req.body,
                request_id: requestId
            };

            Logger.warn('Plugin ID mismatch', {
                requestId,
                installId: install_id,
                expectedPluginId: plugin_id,
                actualPluginId: installation.plugin_id
            }, {
                component: 'analyze-endpoint',
                operation: 'plugin_mismatch'
            });

            res.status(403).json(response);
            return;
        }

        // Validate installation is active
        if (!installation.is_active || installation.is_uninstalled) {
            const response: AnalyzeResponse = {
                success: false,
                error: 'Installation is not active or has been uninstalled',
                request_body: req.body,
                request_id: requestId
            };

            Logger.warn('Installation inactive', {
                requestId,
                installId: install_id,
                isActive: installation.is_active,
                isUninstalled: installation.is_uninstalled
            }, {
                component: 'analyze-endpoint',
                operation: 'installation_inactive'
            });

            res.status(403).json(response);
            return;
        }

        // Validate URL matches installation (optional - basic domain check)
        if (installation.url && url) {
            try {
                const installationDomain = new URL(installation.url).hostname;
                const requestDomain = new URL(url).hostname;

                if (installationDomain !== requestDomain) {
                    Logger.warn('URL domain mismatch', {
                        requestId,
                        installId: install_id,
                        installationUrl: installation.url,
                        requestUrl: url,
                        installationDomain,
                        requestDomain
                    }, {
                        component: 'analyze-endpoint',
                        operation: 'url_mismatch'
                    });
                    // Note: This is a warning, not a blocking error
                }
            } catch (error) {
                Logger.warn('URL validation failed', {
                    requestId,
                    installationUrl: installation.url,
                    requestUrl: url,
                    error: error instanceof Error ? error.message : 'Unknown error'
                }, {
                    component: 'analyze-endpoint',
                    operation: 'url_validation_error'
                });
            }
        }

        // Get IP data from IpIntelligenceService
        const ipDataResult = await ipIntelligenceService.getIpData(ip, {
            installationId: install_id.toString(),
            requestSource: 'plugin',
            userAgent: req.headers['user-agent'],
            referer: req.headers.referer
        });

        const processingTime = Date.now() - startTime;

        // Log successful request/response
        await prisma.ipAnalysisRequest.create({
            data: {
                ip_address: ip,
                installation_id: install_id.toString(),
                request_source: 'plugin',
                user_agent: req.headers['user-agent'] || null,
                referer: req.headers.referer || null,
                client_ip: clientIp,
                request_headers: JSON.stringify({
                    'user-agent': req.headers['user-agent'],
                    'referer': req.headers.referer,
                    'accept': req.headers.accept,
                    'content-type': req.headers['content-type']
                }),
                response_time_ms: processingTime,
                request_body: req.body,
                response_body: ipDataResult.data,
                analysis_result: {
                    success: true,
                    usedCachedData: ipDataResult.metadata.usedStoredData,
                    dataAge: ipDataResult.metadata.dataAge,
                    processingTime: ipDataResult.metadata.processingTime
                },
                processing_time_ms: processingTime,
                used_cached_data: ipDataResult.metadata.usedStoredData,
                requested_at: new Date(),
                completed_at: new Date()
            }
        });

        // Return complete ipRegistry response
        const response: AnalyzeResponse = {
            success: true,
            data: ipDataResult.data, // Complete ipRegistry API response
            request_body: req.body,
            request_id: requestId
        };

        Logger.info('Analyze request completed successfully', {
            requestId,
            ip,
            installId: install_id,
            processingTime,
            usedCachedData: ipDataResult.metadata.usedStoredData
        }, {
            component: 'analyze-endpoint',
            operation: 'request_completed'
        });

        res.status(200).json(response);

    } catch (error) {
        const processingTime = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';

        Logger.error('Analyze endpoint error', error, {
            requestId,
            body: req.body,
            processingTime,
            clientIp
        }, {
            component: 'analyze-endpoint',
            operation: 'request_error'
        });

        // Log failed request
        try {
            await prisma.ipAnalysisRequest.create({
                data: {
                    ip_address: req.body?.ip || 'unknown',
                    installation_id: req.body?.install_id?.toString() || null,
                    request_source: 'plugin',
                    user_agent: req.headers['user-agent'] || null,
                    referer: req.headers.referer || null,
                    client_ip: clientIp,
                    request_headers: JSON.stringify({
                        'user-agent': req.headers['user-agent'],
                        'referer': req.headers.referer
                    }),
                    response_time_ms: processingTime,
                    request_body: req.body,
                    response_body: null,
                    analysis_result: {
                        success: false,
                        error: errorMessage
                    },
                    processing_time_ms: processingTime,
                    used_cached_data: false,
                    requested_at: new Date(),
                    completed_at: new Date()
                }
            });
        } catch (logError) {
            Logger.error('Failed to log failed request', logError, {
                requestId,
                originalError: errorMessage
            }, {
                component: 'analyze-endpoint',
                operation: 'log_error'
            });
        }

        const response: AnalyzeResponse = {
            success: false,
            error: 'Internal server error occurred while processing request',
            request_body: req.body || {},
            request_id: requestId
        };

        res.status(500).json(response);
    }
}