// IMPORTANT: Make sure to import `instrument.ts` at the top of your file.
import '../instrument';

import express from 'express';
import cookieParser from 'cookie-parser';
import cors from 'cors';
import helmet from 'helmet';
import * as Sentry from '@sentry/node';
import apiRoutes from './routes';
import { securityStacks } from '../middleware/security-stack';
import { sessionValidationMiddleware } from '../middleware/auth-middleware';
import { initializePerformanceOptimizations } from '../lib/performance-init';

const app = express();

// Basic middleware
app.use(helmet()); // Security headers
app.use(cors({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

// Apply security middleware stack
const environment = process.env.NODE_ENV || 'development';
const securityStack = environment === 'production' ? securityStacks.production : securityStacks.development;

securityStack.forEach(middleware => {
    app.use(middleware);
});

// Add session validation middleware globally (optional authentication)
app.use(sessionValidationMiddleware());

// API routes
app.use('/api/v1', apiRoutes);

// Health check endpoint
app.get('/health', (_req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// The error handler must be registered before any other error middleware and after all controllers
Sentry.setupExpressErrorHandler(app);

// Optional fallthrough error handler
app.use((error: any, req: express.Request, res: express.Response, _next: express.NextFunction) => {
    // Log to Sentry (already handled by setupExpressErrorHandler, but keeping for additional context)
    Sentry.captureException(error, {
        tags: {
            component: 'express-error-handler'
        },
        extra: {
            url: req.originalUrl,
            method: req.method,
            headers: req.headers,
            body: req.body
        }
    });

    // Don't expose internal errors in production
    const isDevelopment = process.env.NODE_ENV === 'development';

    // The error id is attached to `res.sentry` to be returned
    // and optionally displayed to the user for support.
    res.status(error.status || 500).json({
        success: false,
        error: isDevelopment ? error.message : 'Internal Server Error',
        errorId: (res as any).sentry, // Sentry error ID for support (added by setupExpressErrorHandler)
        ...(isDevelopment && { stack: error.stack })
    });
});

// 404 handler
app.use((_req, res) => {
    res.status(404).json({
        success: false,
        error: 'Endpoint not found'
    });
});

const PORT = process.env.PORT || 3001;

// Start the server
app.listen(PORT, async () => {
    console.log(`API server running on port ${PORT}`);
    console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    console.log(`Sentry DSN configured: ${process.env.SENTRY_DSN ? 'Yes' : 'No'}`);

    // Initialize performance optimizations
    try {
        await initializePerformanceOptimizations();
    } catch (error) {
        console.error('Failed to initialize performance optimizations:', error);
        // Also capture this error in Sentry
        Sentry.captureException(error, {
            tags: {
                component: 'server-startup',
                operation: 'performance-init'
            }
        });
    }
});

export default app;