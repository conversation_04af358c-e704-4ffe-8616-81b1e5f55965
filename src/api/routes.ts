import { Router } from 'express';
import {
    login<PERSON><PERSON><PERSON>,
    logo<PERSON><PERSON><PERSON><PERSON>,
    changePass<PERSON><PERSON><PERSON><PERSON>,
    createAd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    getProfile<PERSON><PERSON><PERSON>,
    requestPasswordR<PERSON>tHandler,
    resetPasswordHandler
} from './auth';
import {
    validateSession<PERSON>and<PERSON>,
    refresh<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    getUserSessionsHandler,
    revokeSessionHand<PERSON>,
    revokeOtherSessionsHandler,
    getSessionSecurityMetricsHandler,
    getSessionAnomaliesHandler
} from './sessions';
import {
    getMySessionsHandler,
    revokeSessionHandler as revokeMySessionHandler,
    revokeAllOtherSessionsHandler,
    getSessionSecurityMetricsHandler as getEnhancedSessionMetricsHandler,
    getAllSessionsHandler,
    forceRevokeSessionHandler,
    manualSessionCleanupHandler
} from './session-management';
import {
    rateLimitMiddleware,
    auth,
    authorize,
    csrf,
    validate,
    Permission,
    UserRole
} from '../middleware';
import { protect, logPermissionDenial } from '../middleware/route-protection';
import { performanceMonitor } from '../lib/performance-monitor';
import { externalApiRateLimiter } from '../lib/external-api-rate-limiter';
import QueryOptimizer from '../lib/query-optimizer';
import { SecurityEventType } from '../lib/security-logger';
import { Logger } from '../lib/logger';
import { analyzeHandler } from './analyze';

const router = Router();

// Helper function for consistent error handling and logging
const handleRouteError = (error: any, operation: string, req: any, res: any) => {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    // Log error with Sentry
    Logger.error(`API Route Error: ${operation}`, error, {
        operation,
        url: req.originalUrl,
        method: req.method,
        userId: req.user?.id,
        userEmail: req.user?.email,
        userAgent: req.headers['user-agent'],
        ip: req.ip
    }, {
        component: 'api-routes',
        operation: operation.toLowerCase().replace(/\s+/g, '_')
    });

    // Return standardized error response
    res.status(500).json({
        success: false,
        error: `Failed to ${operation.toLowerCase()}`,
        message: errorMessage
    });
};

// Apply performance monitoring to all routes
router.use(performanceMonitor.middleware());

// Apply permission denial logging to all routes
router.use(logPermissionDenial());

// Add request tracking middleware
router.use((req, res, next) => {
    // Log API request with Sentry breadcrumb
    Logger.info('API Request', {
        method: req.method,
        url: req.originalUrl,
        userAgent: req.headers['user-agent'],
        ip: req.ip,
        userId: req.user?.id,
        userEmail: req.user?.email
    }, {
        component: 'api-routes',
        operation: 'request'
    });

    next();
});

// WordPress Plugin API Endpoint
// POST /api/v1/analyze - Simple proxy to ipRegistry with Freemius validation
router.post('/v1/analyze',
    rateLimitMiddleware.general,
    analyzeHandler
);

// Authentication Routes
// Enhanced login endpoint with security controls and logging
router.post('/auth/login',
    rateLimitMiddleware.auth,
    validate.login,
    loginHandler
);

// Logout endpoint with proper session cleanup
router.post('/auth/logout',
    rateLimitMiddleware.general,
    auth.optional, // Optional auth to handle cases where session is already invalid
    logoutHandler
);

// Password change endpoint with validation and security
router.post('/auth/change-password',
    rateLimitMiddleware.general,
    auth.required,
    csrf.web,
    validate.changePassword,
    changePasswordHandler
);

// Admin user creation endpoint with authorization (Super Admin only)
router.post('/auth/admin-users',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.superAdmin,
    csrf.admin,
    validate.createAdminUser,
    createAdminUserHandler
);

// Get current user profile
router.get('/auth/profile',
    rateLimitMiddleware.general,
    auth.required,
    getProfileHandler
);

// Request password reset (no auth required)
router.post('/auth/request-password-reset',
    rateLimitMiddleware.passwordReset,
    validate.passwordResetRequest,
    requestPasswordResetHandler
);

// Reset password with token (no auth required)
router.post('/auth/reset-password',
    rateLimitMiddleware.passwordReset,
    validate.passwordReset,
    resetPasswordHandler
);

// Session Management Routes
// Session validation endpoint for frontend use
router.get('/sessions/validate',
    rateLimitMiddleware.general,
    auth.optional, // Optional to allow checking session status
    validateSessionHandler
);

// Session refresh endpoint with security validation
router.post('/sessions/refresh',
    rateLimitMiddleware.general,
    auth.required,
    refreshSessionHandler
);

// User session listing endpoint for security monitoring
router.get('/sessions/list',
    rateLimitMiddleware.general,
    auth.required,
    authorize.sessionManagement,
    getUserSessionsHandler
);

// Revoke single session endpoint
router.delete('/sessions/:sessionId',
    rateLimitMiddleware.general,
    auth.required,
    authorize.sessionManagement,
    csrf.web,
    revokeSessionHandler
);

// Bulk session revocation endpoint (revoke all other sessions)
router.post('/sessions/revoke-others',
    rateLimitMiddleware.general,
    auth.required,
    csrf.web,
    revokeOtherSessionsHandler
);

// Session security metrics endpoint (admin only)
router.get('/sessions/security-metrics',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev,
    getSessionSecurityMetricsHandler
);

// Session anomaly detection endpoint
router.get('/sessions/anomalies',
    rateLimitMiddleware.general,
    auth.required,
    authorize.securityMonitoring,
    getSessionAnomaliesHandler
);

// Enhanced Session Management Routes
// Get current user's sessions with enhanced security information
router.get('/admin/sessions/my-sessions',
    rateLimitMiddleware.general,
    auth.required,
    getMySessionsHandler
);

// Revoke a specific session for current user
router.delete('/admin/sessions/revoke/:sessionId',
    rateLimitMiddleware.general,
    auth.required,
    csrf.web,
    revokeMySessionHandler
);

// Revoke all other sessions for current user (security action)
router.post('/admin/sessions/revoke-all-others',
    rateLimitMiddleware.general,
    auth.required,
    csrf.web,
    revokeAllOtherSessionsHandler
);

// Get enhanced session security metrics (Super Admin only)
router.get('/admin/sessions/security-metrics',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev,
    getEnhancedSessionMetricsHandler
);

// Get all user sessions across the system (Super Admin only)
router.get('/admin/sessions/all-sessions',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev,
    getAllSessionsHandler
);

// Force revoke any user's session (Super Admin only)
router.delete('/admin/sessions/force-revoke/:userId/:sessionId',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev,
    csrf.web,
    forceRevokeSessionHandler
);

// Manual session cleanup (Super Admin only)
router.post('/admin/sessions/cleanup',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev,
    csrf.web,
    manualSessionCleanupHandler
);

// GuardGeo Platform API Routes with Role-Based Access Control

// Dashboard Routes (All authenticated users can view dashboard)
router.get('/admin/dashboard/stats',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            // Get current date ranges
            const now = new Date();
            const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            const monthStart = new Date(now.getFullYear(), now.getMonth(), 1);

            // Aggregate dashboard statistics
            const [
                totalApiRequestsToday,
                totalApiRequestsThisMonth,
                uniqueIpsToday,
                uniqueIpsThisMonth,
                activeInstallations,
                totalProducts,
                recentRequests,
                topCountries,
                threatDetectionsToday
            ] = await Promise.all([
                // API requests today
                prisma.ipAnalysisRequest.count({
                    where: { requested_at: { gte: todayStart } }
                }),
                // API requests this month
                prisma.ipAnalysisRequest.count({
                    where: { requested_at: { gte: monthStart } }
                }),
                // Unique IPs today
                prisma.ipAnalysisRequest.findMany({
                    where: { requested_at: { gte: todayStart } },
                    select: { ip_address: true },
                    distinct: ['ip_address']
                }).then(results => results.length),
                // Unique IPs this month
                prisma.ipAnalysisRequest.findMany({
                    where: { requested_at: { gte: monthStart } },
                    select: { ip_address: true },
                    distinct: ['ip_address']
                }).then(results => results.length),
                // Active installations
                prisma.freemiusInstallation.count({
                    where: { is_active: true }
                }),
                // Total products
                prisma.freemiusProduct.count(),
                // Recent requests
                prisma.ipAnalysisRequest.findMany({
                    take: 10,
                    orderBy: { requested_at: 'desc' },
                    include: {
                        installation: {
                            select: { plugin_id: true }
                        }
                    }
                }),
                // Top countries
                prisma.$queryRaw`
                    SELECT country_name as country, COUNT(*) as count
                    FROM ip_registry_data 
                    WHERE country_name IS NOT NULL 
                    GROUP BY country_name 
                    ORDER BY count DESC 
                    LIMIT 10
                `,
                // Threat detections today
                prisma.ipRegistryData.count({
                    where: {
                        is_threat: true,
                        updated_at: { gte: todayStart }
                    }
                })
            ]);

            // Calculate cache hit rate and average response time
            const performanceStats = await prisma.ipAnalysisRequest.aggregate({
                where: { requested_at: { gte: todayStart } },
                _avg: { processing_time_ms: true },
                _count: { used_cached_data: true }
            });

            const totalRequestsToday = totalApiRequestsToday || 1;
            const cachedRequestsToday = await prisma.ipAnalysisRequest.count({
                where: {
                    requested_at: { gte: todayStart },
                    used_cached_data: true
                }
            });

            const dashboardStats = {
                total_api_requests_today: totalApiRequestsToday,
                total_api_requests_this_month: totalApiRequestsThisMonth,
                unique_ips_today: uniqueIpsToday,
                unique_ips_this_month: uniqueIpsThisMonth,
                active_installations: activeInstallations,
                total_products: totalProducts,
                cache_hit_rate_percentage: (cachedRequestsToday / totalRequestsToday) * 100,
                average_response_time_ms: performanceStats._avg.processing_time_ms || 0,
                threat_detections_today: threatDetectionsToday,
                recent_requests: recentRequests.map(req => ({
                    id: req.id,
                    ip: req.ip_address,
                    plugin_id: req.installation?.plugin_id || 'unknown',
                    install_id: req.installation_id || 'unknown',
                    created_at: req.requested_at,
                    validation_result: req.completed_at ? 'success' : 'pending'
                })),
                top_countries: Array.isArray(topCountries) ? topCountries : []
            };

            Logger.info('Dashboard stats retrieved', {
                userId: req.user?.id,
                statsCount: Object.keys(dashboardStats).length
            });

            res.json({ success: true, data: dashboardStats });
        } catch (error) {
            handleRouteError(error, 'get dashboard stats', req, res);
        }
    }
);

router.get('/admin/system/health',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            // Check database health
            const dbStart = Date.now();
            await prisma.$queryRaw`SELECT 1`;
            const dbResponseTime = Date.now() - dbStart;

            // Get memory usage
            const memUsage = process.memoryUsage();
            const memTotal = memUsage.heapTotal;
            const memUsed = memUsage.heapUsed;

            const healthData = {
                status: 'healthy' as const,
                database: {
                    status: dbResponseTime < 1000 ? 'healthy' : 'warning',
                    responseTime: dbResponseTime
                },
                externalApis: {
                    freemius: {
                        status: 'healthy', // TODO: Implement actual health check
                        lastCheck: new Date().toISOString()
                    },
                    ipRegistry: {
                        status: 'healthy', // TODO: Implement actual health check
                        lastCheck: new Date().toISOString()
                    }
                },
                memory: {
                    used: memUsed,
                    total: memTotal,
                    percentage: (memUsed / memTotal) * 100
                },
                uptime: process.uptime(),
                lastUpdated: new Date().toISOString()
            };

            res.json({ success: true, data: healthData });
        } catch (error) {
            handleRouteError(error, 'get system health', req, res);
        }
    }
);

router.get('/admin/dashboard/recent-activity',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;
            const limit = parseInt(req.query.limit as string) || 10;

            const [requests, logins, errors] = await Promise.all([
                // Recent API requests
                prisma.ipAnalysisRequest.findMany({
                    take: limit,
                    orderBy: { requested_at: 'desc' },
                    include: {
                        installation: {
                            select: { plugin_id: true }
                        }
                    }
                }),
                // Recent logins from security audit log
                prisma.securityAuditLog.findMany({
                    where: {
                        event_type: 'LOGIN_SUCCESS',
                        success: true
                    },
                    take: limit,
                    orderBy: { created_at: 'desc' }
                }),
                // Recent errors from security audit log
                prisma.securityAuditLog.findMany({
                    where: { success: false },
                    take: limit,
                    orderBy: { created_at: 'desc' }
                })
            ]);

            const recentActivity = {
                requests: requests.map(req => ({
                    id: req.id,
                    ip: req.ip_address,
                    plugin_id: req.installation?.plugin_id || 'unknown',
                    install_id: req.installation_id || 'unknown',
                    created_at: req.requested_at,
                    validation_result: req.completed_at ? 'success' : 'pending'
                })),
                logins: logins.map(login => ({
                    id: login.id,
                    email: login.email,
                    ip_address: login.ip_address,
                    created_at: login.created_at
                })),
                errors: errors.map(error => ({
                    id: error.id,
                    event_type: error.event_type,
                    error_message: error.error_message,
                    created_at: error.created_at
                }))
            };

            res.json({ success: true, data: recentActivity });
        } catch (error) {
            handleRouteError(error, 'get recent activity', req, res);
        }
    }
);

// Freemius Management Routes
router.get('/admin/freemius/products',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            const products = await prisma.freemiusProduct.findMany({
                orderBy: { created: 'desc' }
            });

            Logger.info('Freemius products retrieved', {
                userId: req.user?.id,
                productCount: products.length
            });

            res.json({ success: true, data: products });
        } catch (error) {
            handleRouteError(error, 'get Freemius products', req, res);
        }
    }
);

router.get('/admin/freemius/installations',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            // Parse query parameters
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 20;
            const search = req.query.search as string;
            const product_id = req.query.product_id as string;
            const is_active = req.query.is_active === 'true' ? true :
                req.query.is_active === 'false' ? false : undefined;

            const skip = (page - 1) * limit;

            // Build where clause
            const where: any = {};
            if (search) {
                where.OR = [
                    { title: { contains: search } },
                    { url: { contains: search } },
                    { id: { contains: search } }
                ];
            }
            if (product_id) {
                where.plugin_id = product_id;
            }
            if (is_active !== undefined) {
                where.is_active = is_active;
            }

            const [installations, totalCount] = await Promise.all([
                prisma.freemiusInstallation.findMany({
                    where,
                    skip,
                    take: limit,
                    orderBy: { created: 'desc' },
                    include: {
                        product: {
                            select: { title: true }
                        }
                    }
                }),
                prisma.freemiusInstallation.count({ where })
            ]);

            const pagination = {
                current_page: page,
                total_pages: Math.ceil(totalCount / limit),
                total_items: totalCount,
                items_per_page: limit
            };

            Logger.info('Freemius installations retrieved', {
                userId: req.user?.id,
                installationCount: installations.length,
                totalCount,
                filters: { search, product_id, is_active }
            });

            res.json({
                success: true,
                data: {
                    data: installations,
                    pagination
                }
            });
        } catch (error) {
            handleRouteError(error, 'get Freemius installations', req, res);
        }
    }
);

router.post('/admin/freemius/sync',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev, // Dev and Super Admin only
    async (req, res) => {
        try {
            const { freemiusService } = await import('../services/freemius-service');

            // Trigger sync in background
            const syncPromise = freemiusService.syncAllData();

            Logger.info('Freemius sync initiated', {
                userId: req.user?.id,
                userEmail: req.user?.email
            });

            res.json({
                success: true,
                message: 'Freemius data synchronization initiated',
                data: {
                    syncStarted: new Date().toISOString()
                }
            });

            // Continue sync in background
            syncPromise.catch(error => {
                Logger.error('Background Freemius sync failed', error, {
                    userId: req.user?.id
                });
            });
        } catch (error) {
            handleRouteError(error, 'sync Freemius data', req, res);
        }
    }
);

router.get('/admin/freemius/stats',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            const [
                totalProducts,
                totalInstallations,
                activeInstallations,
                lastSyncProduct
            ] = await Promise.all([
                prisma.freemiusProduct.count(),
                prisma.freemiusInstallation.count(),
                prisma.freemiusInstallation.count({
                    where: { is_active: true }
                }),
                prisma.freemiusProduct.findFirst({
                    orderBy: { api_last_synced_at: 'desc' },
                    select: { api_last_synced_at: true }
                })
            ]);

            const stats = {
                totalProducts,
                totalInstallations,
                activeInstallations,
                totalRevenue: 0, // TODO: Calculate from installation data
                lastSyncAt: lastSyncProduct?.api_last_synced_at?.toISOString() || null
            };

            res.json({ success: true, data: stats });
        } catch (error) {
            handleRouteError(error, 'get Freemius stats', req, res);
        }
    }
);

// IP Intelligence Routes
router.post('/admin/ip-intelligence/lookup',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.dev, // Dev and Super Admin only
    async (req, res) => {
        try {
            const { ipIntelligenceService } = await import('../services/ip-intelligence-service');
            const { ip } = req.body;

            if (!ip) {
                return res.status(400).json({
                    success: false,
                    error: 'IP address is required'
                });
            }

            const ipData = await ipIntelligenceService.getIpData(ip, {
                requestSource: 'admin'
            });

            Logger.info('Manual IP lookup performed', {
                userId: req.user?.id,
                ip,
                found: !!ipData
            });

            res.json({ success: true, data: ipData });
        } catch (error) {
            handleRouteError(error, 'perform IP lookup', req, res);
        }
    }
);

router.get('/admin/ip-intelligence/history',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            // Parse query parameters
            const page = parseInt(req.query.page as string) || 1;
            const limit = parseInt(req.query.limit as string) || 50;
            const ip = req.query.ip as string;
            const source = req.query.source as string;
            const recommendation = req.query.recommendation as string;
            const startDate = req.query.startDate as string;
            const endDate = req.query.endDate as string;

            const skip = (page - 1) * limit;

            // Build where clause
            const where: any = {};
            if (ip) {
                where.ip_address = { contains: ip };
            }
            if (source) {
                where.request_source = source;
            }
            if (recommendation) {
                where.recommendation = recommendation;
            }
            if (startDate || endDate) {
                where.requested_at = {};
                if (startDate) {
                    where.requested_at.gte = new Date(startDate);
                }
                if (endDate) {
                    where.requested_at.lte = new Date(endDate);
                }
            }

            const [requests, totalCount] = await Promise.all([
                prisma.ipAnalysisRequest.findMany({
                    where,
                    skip,
                    take: limit,
                    orderBy: { requested_at: 'desc' },
                    include: {
                        ip_data: {
                            select: {
                                country_name: true,
                                city: true,
                                is_threat: true,
                                is_vpn: true,
                                is_proxy: true,
                                is_tor: true
                            }
                        },
                        installation: {
                            select: {
                                id: true,
                                url: true,
                                title: true
                            }
                        }
                    }
                }),
                prisma.ipAnalysisRequest.count({ where })
            ]);

            const pagination = {
                page,
                limit,
                total: totalCount,
                pages: Math.ceil(totalCount / limit)
            };

            res.json({
                success: true,
                data: {
                    requests,
                    pagination
                }
            });
        } catch (error) {
            handleRouteError(error, 'get IP request history', req, res);
        }
    }
);

router.get('/admin/ip-intelligence/stats',
    rateLimitMiddleware.general,
    auth.required,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;
            const timeRange = req.query.timeRange as string || 'day';

            // Calculate time range
            const now = new Date();
            let startDate: Date;

            switch (timeRange) {
                case 'hour':
                    startDate = new Date(now.getTime() - 60 * 60 * 1000);
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default: // day
                    startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            }

            const [
                totalRequests,
                cachedRequests,
                avgProcessingTime,
                threatDetections,
                topCountries,
                requestsBySource,
                recommendationBreakdown
            ] = await Promise.all([
                // Total requests
                prisma.ipAnalysisRequest.count({
                    where: { requested_at: { gte: startDate } }
                }),
                // Cached requests
                prisma.ipAnalysisRequest.count({
                    where: {
                        requested_at: { gte: startDate },
                        used_cached_data: true
                    }
                }),
                // Average processing time
                prisma.ipAnalysisRequest.aggregate({
                    where: { requested_at: { gte: startDate } },
                    _avg: { processing_time_ms: true }
                }),
                // Threat detections
                prisma.ipAnalysisRequest.count({
                    where: {
                        requested_at: { gte: startDate },
                        ip_data: {
                            is_threat: true
                        }
                    }
                }),
                // Top countries
                prisma.$queryRaw`
                    SELECT ird.country_name as country, COUNT(*) as count
                    FROM ip_analysis_requests iar
                    JOIN ip_registry_data ird ON iar.ip_address = ird.ip_address
                    WHERE iar.requested_at >= ${startDate}
                    AND ird.country_name IS NOT NULL
                    GROUP BY ird.country_name
                    ORDER BY count DESC
                    LIMIT 10
                `,
                // Requests by source
                prisma.$queryRaw`
                    SELECT request_source as source, COUNT(*) as count
                    FROM ip_analysis_requests
                    WHERE requested_at >= ${startDate}
                    GROUP BY request_source
                `,
                // Recommendation breakdown
                prisma.$queryRaw`
                    SELECT recommendation, COUNT(*) as count
                    FROM ip_analysis_requests
                    WHERE requested_at >= ${startDate}
                    AND recommendation IS NOT NULL
                    GROUP BY recommendation
                `
            ]);

            const stats = {
                totalRequests,
                cacheHitRate: totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0,
                averageProcessingTime: avgProcessingTime._avg.processing_time_ms || 0,
                threatDetections,
                topCountries: Array.isArray(topCountries) ? topCountries : [],
                requestsBySource: Array.isArray(requestsBySource) ? requestsBySource : [],
                recommendationBreakdown: Array.isArray(recommendationBreakdown) ? recommendationBreakdown : []
            };

            res.json({ success: true, data: stats });
        } catch (error) {
            handleRouteError(error, 'get IP analytics', req, res);
        }
    }
);

// API Logs and Monitoring Routes
router.get('/v1/admin/api-logs',
    ...protect.apiLogs(), // Dev and Super Admin only
    async (req, res) => {
        try {
            // API logs listing logic would go here
            res.json({ success: true, data: { logs: [], pagination: {} } });
        } catch (error) {
            handleRouteError(error, 'get API logs', req, res);
        }
    }
);

router.get('/v1/admin/system-health',
    ...protect.systemHealth(), // Dev and Super Admin only
    async (req, res) => {
        try {
            // System health check logic would go here
            const healthData = {
                status: 'healthy',
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                timestamp: new Date().toISOString()
            };
            res.json({ success: true, data: healthData });
        } catch (error) {
            handleRouteError(error, 'get system health', req, res);
        }
    }
);

// Data Export Routes
router.get('/v1/admin/export',
    ...protect.dataExport(),
    async (req, res) => {
        try {
            // Data export logic would go here
            res.json({ success: true, message: 'Export initiated' });
        } catch (error) {
            handleRouteError(error, 'export data', req, res);
        }
    }
);

router.post('/v1/admin/export',
    ...protect.dataExport(),
    async (req, res) => {
        try {
            // Custom data export logic would go here
            res.json({ success: true, message: 'Custom export initiated' });
        } catch (error) {
            handleRouteError(error, 'create custom export', req, res);
        }
    }
);

// Webhook Routes (Public endpoints with signature validation)
router.post('/v1/webhooks/freemius',
    ...protect.webhook(),
    async (req, res) => {
        try {
            // Freemius webhook processing logic would go here
            res.json({ success: true, message: 'Webhook processed' });
        } catch (error) {
            handleRouteError(error, 'process Freemius webhook', req, res);
        }
    }
);

// User Management Routes (Super Admin only for user creation/deletion, users can manage their own profiles)
router.get('/admin/users',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.superAdmin,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;

            const users = await prisma.user.findMany({
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    role: true,
                    is_active: true,
                    created_at: true,
                    updated_at: true,
                    last_login_ip: true,
                    two_factor_enabled: true,
                    // Don't include password or sensitive fields
                },
                orderBy: { created_at: 'desc' }
            });

            // Add computed fields
            const usersWithExtras = users.map(user => ({
                ...user,
                is_2fa_enabled: user.two_factor_enabled,
                last_login: null // TODO: Get from session or audit log
            }));

            Logger.info('Users list retrieved', {
                userId: req.user?.id,
                userCount: users.length
            });

            res.json({ success: true, data: usersWithExtras });
        } catch (error) {
            handleRouteError(error, 'get users', req, res);
        }
    }
);

router.post('/admin/users',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.superAdmin,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;
            const { hashPassword } = await import('../lib/auth');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');

            const { email, password, role } = req.body;

            // Validate input
            if (!email || !password || !role) {
                return res.status(400).json({
                    success: false,
                    error: 'Email, password, and role are required'
                });
            }

            // Check if user already exists
            const existingUser = await prisma.user.findUnique({
                where: { email }
            });

            if (existingUser) {
                return res.status(400).json({
                    success: false,
                    error: 'User with this email already exists'
                });
            }

            // Hash password and create user
            const hashedPassword = await hashPassword(password);

            const newUser = await prisma.user.create({
                data: {
                    email,
                    password: hashedPassword,
                    first_name: email.split('@')[0], // Default first name
                    last_name: 'User', // Default last name
                    role: role.toUpperCase(),
                    is_active: true,
                    password_changed_at: new Date(),
                    requires_password_change: true // Force password change on first login
                },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    role: true,
                    is_active: true,
                    created_at: true
                }
            });

            // Log user creation
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_CREATED,
                userId: req.user?.id || 'unknown',
                email: req.user?.email,
                ipAddress: req.ip || 'unknown',
                userAgent: req.headers['user-agent'],
                resource: 'user',
                action: 'create',
                success: true,
                details: {
                    targetUserId: newUser.id,
                    targetEmail: newUser.email,
                    newRole: newUser.role
                }
            });

            Logger.info('User created successfully', {
                userId: req.user?.id,
                newUserId: newUser.id,
                newUserEmail: newUser.email,
                newUserRole: newUser.role
            });

            res.json({ success: true, data: newUser });
        } catch (error) {
            handleRouteError(error, 'create user', req, res);
        }
    }
);

router.put('/admin/users/:id',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.superAdmin,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');

            const userId = req.params.id;
            const { email, role } = req.body;

            // Get existing user
            const existingUser = await prisma.user.findUnique({
                where: { id: userId }
            });

            if (!existingUser) {
                return res.status(404).json({
                    success: false,
                    error: 'User not found'
                });
            }

            // Check if email is already taken by another user
            if (email && email !== existingUser.email) {
                const emailTaken = await prisma.user.findFirst({
                    where: {
                        email,
                        id: { not: userId }
                    }
                });

                if (emailTaken) {
                    return res.status(400).json({
                        success: false,
                        error: 'Email is already taken by another user'
                    });
                }
            }

            // Update user
            const updatedUser = await prisma.user.update({
                where: { id: userId },
                data: {
                    ...(email && { email }),
                    ...(role && { role: role.toUpperCase() }),
                    updated_at: new Date()
                },
                select: {
                    id: true,
                    email: true,
                    first_name: true,
                    last_name: true,
                    role: true,
                    is_active: true,
                    created_at: true,
                    updated_at: true
                }
            });

            // Log user update
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_UPDATED,
                userId: req.user?.id || 'unknown',
                email: req.user?.email,
                ipAddress: req.ip || 'unknown',
                userAgent: req.headers['user-agent'],
                resource: 'user',
                action: 'update',
                success: true,
                details: {
                    targetUserId: userId,
                    targetEmail: updatedUser.email,
                    changedFields: Object.keys({ email, role }).filter(key => ({ email, role })[key] !== undefined)
                }
            });

            Logger.info('User updated successfully', {
                userId: req.user?.id,
                targetUserId: userId,
                changes: { email, role }
            });

            res.json({ success: true, data: updatedUser });
        } catch (error) {
            handleRouteError(error, 'update user', req, res);
        }
    }
);

router.delete('/admin/users/:id',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.superAdmin,
    async (req, res) => {
        try {
            const prisma = (await import('../lib/prisma')).default;
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');

            const userId = req.params.id;

            // Get user to delete
            const userToDelete = await prisma.user.findUnique({
                where: { id: userId }
            });

            if (!userToDelete) {
                return res.status(404).json({
                    success: false,
                    error: 'User not found'
                });
            }

            // Prevent deleting super admin
            if (userToDelete.role === 'SUPER_ADMIN') {
                return res.status(400).json({
                    success: false,
                    error: 'Cannot delete super admin user'
                });
            }

            // Prevent self-deletion
            if (userId === req.user?.id) {
                return res.status(400).json({
                    success: false,
                    error: 'Cannot delete your own account'
                });
            }

            // Delete user (cascade will handle related records)
            await prisma.user.delete({
                where: { id: userId }
            });

            // Log user deletion
            await SecurityLogger.logAdminAction({
                eventType: SecurityEventType.ADMIN_USER_DEACTIVATED,
                userId: req.user?.id || 'unknown',
                email: req.user?.email,
                ipAddress: req.ip || 'unknown',
                userAgent: req.headers['user-agent'],
                resource: 'user',
                action: 'delete',
                success: true,
                details: {
                    targetUserId: userId,
                    targetEmail: userToDelete.email,
                    oldRole: userToDelete.role
                }
            });

            Logger.info('User deleted successfully', {
                userId: req.user?.id,
                deletedUserId: userId,
                deletedUserEmail: userToDelete.email
            });

            res.json({ success: true, message: 'User deleted successfully' });
        } catch (error) {
            handleRouteError(error, 'delete user', req, res);
        }
    }
);

// System Settings Routes (Dev and Super Admin only)
router.get('/system/settings',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.systemSettings,
    async (req, res) => {
        try {
            // System settings logic would go here
            res.json({ success: true, data: { settings: {} } });
        } catch (error) {
            handleRouteError(error, 'get system settings', req, res);
        }
    }
);

router.put('/system/settings',
    rateLimitMiddleware.admin,
    auth.required,
    authorize.systemSettings,
    async (req, res) => {
        try {
            // System settings update logic would go here
            res.json({ success: true, message: 'System settings updated successfully' });
        } catch (error) {
            handleRouteError(error, 'update system settings', req, res);
        }
    }
);

// Enhanced Audit Trail and Security Monitoring Routes (Dev and Super Admin only)
// Advanced audit logs endpoint with comprehensive filtering
router.get('/admin/audit-logs',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { AuditSystem } = await import('../lib/audit-system');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');

            const filters = {
                timeRange: req.query.timeRange as 'last_hour' | 'last_24h' | 'last_week' | 'last_month' | 'custom' | undefined,
                eventTypes: req.query.eventTypes ? (req.query.eventTypes as string).split(',') as SecurityEventType[] : undefined,
                userId: req.query.userId as string,
                email: req.query.email as string,
                ipAddress: req.query.ipAddress as string,
                resource: req.query.resource as string,
                action: req.query.action as string,
                success: req.query.success ? req.query.success === 'true' : undefined,
                suspiciousOnly: req.query.suspiciousOnly === 'true',
                searchTerm: req.query.searchTerm as string,
                limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
                offset: req.query.offset ? parseInt(req.query.offset as string) : 0,
                includeStats: req.query.includeStats === 'true',
            };

            const auditReport = await AuditSystem.queryAuditLogs(filters);

            // Log audit log access
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'audit_logs',
                action: 'query',
                success: true,
                details: {
                    filters,
                    resultCount: auditReport.events.length,
                    totalEvents: auditReport.summary.totalEvents,
                }
            });

            res.json({ success: true, data: auditReport });
        } catch (error) {
            handleRouteError(error, 'query audit logs', req, res);
        }
    }
);

// Audit log statistics endpoint
router.get('/admin/audit-logs/stats',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityLogger } = await import('../lib/security-logger');
            const timeframe = (req.query.timeframe as 'hour' | 'day' | 'week' | 'month') || 'day';

            const stats = await SecurityLogger.getAuditLogStats(timeframe);

            res.json({ success: true, data: stats });
        } catch (error) {
            handleRouteError(error, 'get audit log stats', req, res);
        }
    }
);

// Security alerts endpoint
router.get('/admin/security/alerts',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { AuditSystem } = await import('../lib/audit-system');

            const timeRange = (req.query.timeRange as 'last_hour' | 'last_24h' | 'last_week' | 'last_month' | 'custom') || 'last_24h';
            const where = AuditSystem['buildTimeFilter']({ timeRange });

            const alerts = await AuditSystem.detectSecurityAlerts(where);

            res.json({ success: true, data: alerts });
        } catch (error) {
            handleRouteError(error, 'get security alerts', req, res);
        }
    }
);

// Log integrity verification endpoint
router.post('/admin/audit-logs/verify-integrity',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { AuditSystem } = await import('../lib/audit-system');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');

            const integrityCheck = await AuditSystem.verifyLogIntegrity();

            // Log integrity verification
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'audit_logs',
                action: 'verify_integrity',
                success: integrityCheck.isValid,
                details: {
                    totalLogs: integrityCheck.totalLogs,
                    checkedLogs: integrityCheck.checkedLogs,
                    tamperedLogs: integrityCheck.tamperedLogs,
                    issuesFound: integrityCheck.issues.length,
                }
            });

            res.json({ success: true, data: integrityCheck });
        } catch (error) {
            console.error('Failed to verify log integrity:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to verify log integrity',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Compliance report generation endpoint
router.get('/admin/security/compliance-report',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { AuditSystem } = await import('../lib/audit-system');
            const timeRange = req.query.timeRange as 'week' | 'month' | 'quarter' | 'year' || 'month';

            const complianceReport = await AuditSystem.generateComplianceReport(timeRange);

            res.json({ success: true, data: complianceReport });
        } catch (error) {
            console.error('Failed to generate compliance report:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to generate compliance report',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Audit log export endpoint
router.get('/admin/audit-logs/export',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { AuditSystem } = await import('../lib/audit-system');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');

            const filters = {
                timeRange: (req.query.timeRange as 'last_hour' | 'last_24h' | 'last_week' | 'last_month' | 'custom') || 'last_24h',
                eventTypes: req.query.eventTypes ? (req.query.eventTypes as string).split(',') as SecurityEventType[] : undefined,
                userId: req.query.userId as string,
                email: req.query.email as string,
                ipAddress: req.query.ipAddress as string,
                resource: req.query.resource as string,
                success: req.query.success ? req.query.success === 'true' : undefined,
                suspiciousOnly: req.query.suspiciousOnly === 'true',
                searchTerm: req.query.searchTerm as string,
                limit: 10000, // Higher limit for export
            };

            const auditReport = await AuditSystem.queryAuditLogs(filters);

            // Convert to CSV format
            const csvHeaders = [
                'Timestamp',
                'Event Type',
                'User Email',
                'IP Address',
                'Resource',
                'Action',
                'Status',
                'Error Message',
                'Details'
            ];

            const csvRows = auditReport.events.map(log => [
                log.createdAt,
                log.eventType,
                log.email || '',
                log.ipAddress,
                log.resource || '',
                log.action || '',
                log.success ? 'Success' : 'Failed',
                log.errorMessage || '',
                log.details ? JSON.stringify(log.details) : ''
            ]);

            const csvContent = [
                csvHeaders.join(','),
                ...csvRows.map(row => row.map(field => `"${field}"`).join(','))
            ].join('\n');

            // Log audit log export
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.AUDIT_LOG_ACCESSED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'audit_logs',
                action: 'export',
                success: true,
                details: {
                    filters,
                    exportedRecords: auditReport.events.length,
                    format: 'csv',
                }
            });

            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`);
            res.send(csvContent);
        } catch (error) {
            console.error('Failed to export audit logs:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to export audit logs',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Legacy endpoints for backward compatibility
router.get('/security/audit-logs',
    ...protect.securityMonitoring(),
    async (_req, res) => {
        // Redirect to new enhanced endpoint
        res.redirect(301, '/admin/audit-logs');
    }
);

router.get('/security/metrics',
    ...protect.securityMonitoring(),
    async (_req, res) => {
        // Redirect to new enhanced endpoint
        res.redirect(301, '/admin/audit-logs/stats');
    }
);

router.get('/security/failed-logins',
    ...protect.securityMonitoring(),
    async (req, res) => {
        try {
            const { SecurityLogger } = await import('../lib/security-logger');
            const filters = {
                eventTypes: [SecurityEventType.LOGIN_FAILURE],
                timeRange: (req.query.timeRange as 'last_hour' | 'last_24h' | 'last_week' | 'last_month' | 'custom') || 'last_24h',
                limit: req.query.limit ? parseInt(req.query.limit as string) : 100,
            };

            const auditReport = await SecurityLogger.queryAuditLog(filters);
            res.json({ success: true, data: auditReport });
        } catch (error) {
            console.error('Failed to get failed logins:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve failed login attempts',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Enhanced Security Monitoring Routes (Dev and Super Admin only)
router.get('/admin/security/dashboard',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityMonitoring } = await import('../lib/security-monitoring');
            const timeRange = req.query.timeRange as 'hour' | 'day' | 'week' | 'month' || 'day';
            const dashboard = await SecurityMonitoring.getSecurityDashboard(timeRange);
            res.json({ success: true, data: dashboard });
        } catch (error) {
            handleRouteError(error, 'get security dashboard', req, res);
        }
    }
);

router.get('/admin/security/metrics',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityMonitoring } = await import('../lib/security-monitoring');
            const timeRange = req.query.timeRange as 'hour' | 'day' | 'week' | 'month' || 'day';
            const metrics = await SecurityMonitoring.getSecurityMetrics(timeRange);
            res.json({ success: true, data: metrics });
        } catch (error) {
            console.error('Failed to get security metrics:', error);
            res.status(500).json({ success: false, error: 'Failed to retrieve security metrics' });
        }
    }
);

router.get('/admin/security/failed-logins',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityMonitoring } = await import('../lib/security-monitoring');
            const timeRange = req.query.timeRange as 'hour' | 'day' | 'week' || 'day';
            const limit = parseInt(req.query.limit as string) || 100;
            const failedLogins = await SecurityMonitoring.getFailedLoginAttempts(timeRange, limit);
            res.json({ success: true, data: failedLogins });
        } catch (error) {
            console.error('Failed to get failed login attempts:', error);
            res.status(500).json({ success: false, error: 'Failed to retrieve failed login attempts' });
        }
    }
);

router.get('/admin/security/session-anomalies',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityMonitoring } = await import('../lib/security-monitoring');
            const timeRange = req.query.timeRange as 'hour' | 'day' | 'week' || 'day';
            const limit = parseInt(req.query.limit as string) || 50;
            const anomalies = await SecurityMonitoring.detectSessionAnomalies(timeRange, limit);
            res.json({ success: true, data: anomalies });
        } catch (error) {
            console.error('Failed to detect session anomalies:', error);
            res.status(500).json({ success: false, error: 'Failed to detect session anomalies' });
        }
    }
);

router.get('/admin/security/trends',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityMonitoring } = await import('../lib/security-monitoring');
            const timeRange = req.query.timeRange as 'hour' | 'day' | 'week' | 'month' || 'day';
            const dashboard = await SecurityMonitoring.getSecurityDashboard(timeRange);
            res.json({ success: true, data: dashboard.trends });
        } catch (error) {
            console.error('Failed to get security trends:', error);
            res.status(500).json({ success: false, error: 'Failed to retrieve security trends' });
        }
    }
);

// Security Alerting Routes (Dev and Super Admin only)
router.get('/admin/security/alert-rules',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (_req, res) => {
        try {
            const { SecurityAlerting } = await import('../lib/security-alerting');
            const rules = SecurityAlerting.getAlertRules();
            res.json({ success: true, data: rules });
        } catch (error) {
            console.error('Failed to get alert rules:', error);
            res.status(500).json({ success: false, error: 'Failed to retrieve alert rules' });
        }
    }
);

router.put('/admin/security/alert-rules/:ruleId',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (_req, res) => {
        try {
            // Implementation would update alert rule
            res.json({ success: true, message: 'Alert rule updated successfully' });
        } catch (error) {
            console.error('Failed to update alert rule:', error);
            res.status(500).json({ success: false, error: 'Failed to update alert rule' });
        }
    }
);

router.post('/admin/security/alerts/:alertId/resolve',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityAlerting } = await import('../lib/security-alerting');
            const { alertId } = req.params;
            const { reason } = req.body;
            const success = await SecurityAlerting.resolveAlert(alertId, reason);
            res.json({ success, message: success ? 'Alert resolved successfully' : 'Alert not found' });
        } catch (error) {
            console.error('Failed to resolve alert:', error);
            res.status(500).json({ success: false, error: 'Failed to resolve alert' });
        }
    }
);

router.post('/admin/security/alerts/:alertId/suppress',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityAlerting } = await import('../lib/security-alerting');
            const { alertId } = req.params;
            const { duration, reason } = req.body;
            const success = await SecurityAlerting.suppressAlert(alertId, duration, reason);
            res.json({ success, message: success ? 'Alert suppressed successfully' : 'Alert not found' });
        } catch (error) {
            console.error('Failed to suppress alert:', error);
            res.status(500).json({ success: false, error: 'Failed to suppress alert' });
        }
    }
);

router.get('/admin/security/health-check',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (_req, res) => {
        try {
            const { SecurityAlerting } = await import('../lib/security-alerting');
            const healthCheck = await SecurityAlerting.performSecurityHealthCheck();
            res.json({ success: true, data: healthCheck });
        } catch (error) {
            console.error('Failed to perform health check:', error);
            res.status(500).json({ success: false, error: 'Failed to perform health check' });
        }
    }
);

router.post('/admin/security/reports/generate',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const { SecurityAlerting } = await import('../lib/security-alerting');
            const { type } = req.body;

            // Calculate time range based on report type
            const now = new Date();
            let start: Date;

            switch (type) {
                case 'DAILY':
                    start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                    break;
                case 'WEEKLY':
                    start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                    break;
                case 'MONTHLY':
                    start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                    break;
                default:
                    start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            }

            const report = await SecurityAlerting.generateSecurityReport(type, { start, end: now });
            res.json({ success: true, data: report });
        } catch (error) {
            console.error('Failed to generate security report:', error);
            res.status(500).json({ success: false, error: 'Failed to generate security report' });
        }
    }
);

router.post('/admin/security/alerts/evaluate',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (_req, res) => {
        try {
            const { SecurityAlerting } = await import('../lib/security-alerting');
            const alerts = SecurityAlerting.getActiveAlerts();
            res.json({ success: true, data: alerts });
        } catch (error) {
            console.error('Failed to evaluate security alerts:', error);
            res.status(500).json({ success: false, error: 'Failed to evaluate security alerts' });
        }
    }
);

// Performance Monitoring and Health Check Endpoints

// System health check endpoint (Dev and Super Admin only)
router.get('/admin/system/health',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const healthStatus = await performanceMonitor.getHealthCheck();

            // Set appropriate HTTP status based on health
            const statusCode = healthStatus.status === 'healthy' ? 200 :
                healthStatus.status === 'degraded' ? 200 : 503;

            res.status(statusCode).json({
                success: true,
                data: healthStatus
            });
        } catch (error) {
            handleRouteError(error, 'perform health check', req, res);
        }
    }
);

// Performance statistics endpoint (Dev and Super Admin only)
router.get('/admin/system/performance',
    ...protect.devAndAbove(), // Updated to use GuardGeo role hierarchy
    async (req, res) => {
        try {
            const stats = performanceMonitor.getAllStats();
            const slowQueries = performanceMonitor.getSlowQueries(
                parseInt(req.query.threshold as string) || 1000
            );
            const errorStats = performanceMonitor.getErrorStats(
                parseInt(req.query.timeRange as string) || 3600000 // 1 hour default
            );

            res.json({
                success: true,
                data: {
                    endpointStats: stats,
                    slowQueries: slowQueries.slice(0, 20), // Top 20 slowest
                    errorStats,
                    timestamp: new Date().toISOString()
                }
            });
        } catch (error) {
            console.error('Failed to get performance stats:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve performance statistics',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// External API rate limit status endpoint
router.get('/admin/system/external-api-status',
    ...protect.securityMonitoring(),
    async (_req, res) => {
        try {
            const stats = externalApiRateLimiter.getStatistics();

            res.json({
                success: true,
                data: {
                    apiStatus: stats,
                    timestamp: new Date().toISOString()
                }
            });
        } catch (error) {
            console.error('Failed to get external API status:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve external API status',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Database performance and optimization endpoint
router.get('/admin/system/database-stats',
    ...protect.securityMonitoring(),
    async (_req, res) => {
        try {
            const [dbStats, queryAnalysis] = await Promise.all([
                QueryOptimizer.getDatabaseStats(),
                QueryOptimizer.analyzeQueryPerformance()
            ]);

            res.json({
                success: true,
                data: {
                    databaseStats: dbStats,
                    queryAnalysis,
                    timestamp: new Date().toISOString()
                }
            });
        } catch (error) {
            console.error('Failed to get database stats:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve database statistics',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Database optimization endpoint (verifies indexes)
router.post('/admin/system/optimize-database',
    ...protect.superAdminOnly(),
    async (_req, res) => {
        try {
            const verification = await QueryOptimizer.verifyOptimizedIndexes();

            res.json({
                success: true,
                message: 'Database optimization verification completed',
                data: verification
            });
        } catch (error) {
            console.error('Database optimization verification failed:', error);
            res.status(500).json({
                success: false,
                error: 'Database optimization verification failed',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Reset external API rate limits (emergency use)
router.post('/admin/system/reset-rate-limits/:apiName',
    ...protect.superAdminOnly(),
    async (req, res) => {
        try {
            const { apiName } = req.params;
            externalApiRateLimiter.resetRateLimit(apiName);

            res.json({
                success: true,
                message: `Rate limits reset for ${apiName}`
            });
        } catch (error) {
            console.error('Failed to reset rate limits:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to reset rate limits',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// System metrics summary endpoint (for dashboard)
router.get('/admin/system/metrics-summary',
    rateLimitMiddleware.general,
    auth.required,
    authorize.admin,
    async (_req, res) => {
        try {
            const [healthStatus, performanceStats, apiStats] = await Promise.all([
                performanceMonitor.getHealthCheck(),
                performanceMonitor.getAllStats(),
                externalApiRateLimiter.getStatistics()
            ]);

            // Calculate summary metrics
            const totalRequests = performanceStats.reduce((sum, stat) => sum + stat.totalRequests, 0);
            const avgResponseTime = performanceStats.length > 0
                ? performanceStats.reduce((sum, stat) => sum + stat.averageResponseTime, 0) / performanceStats.length
                : 0;
            const errorRate = performanceStats.length > 0
                ? performanceStats.reduce((sum, stat) => sum + stat.errorRate, 0) / performanceStats.length
                : 0;

            const summary = {
                systemHealth: healthStatus.status,
                uptime: healthStatus.uptime,
                totalRequests,
                averageResponseTime: Math.round(avgResponseTime * 100) / 100,
                errorRate: Math.round(errorRate * 100) / 100,
                memoryUsage: healthStatus.checks.memory.percentage,
                databaseStatus: healthStatus.checks.database.status,
                externalApiStatus: {
                    freemius: apiStats.find(api => api.apiName === 'freemius')?.status || 'unknown',
                    ipRegistry: apiStats.find(api => api.apiName === 'ipregistry')?.status || 'unknown'
                },
                timestamp: new Date().toISOString()
            };

            res.json({
                success: true,
                data: summary
            });
        } catch (error) {
            console.error('Failed to get system metrics summary:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve system metrics',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// IP Intelligence Management Endpoints
// Manual IP lookup endpoint for admin interface
router.post('/admin/ip-intelligence/lookup',
    ...protect.securityMonitoring(),
    validate.ipLookup,
    async (req, res) => {
        try {
            const { ipIntelligenceService } = await import('../services/ip-intelligence-service');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');
            const { ip } = req.body;

            const result = await ipIntelligenceService.getIpData(ip, {
                requestSource: 'admin',
                userAgent: req.headers['user-agent'],
                referer: req.headers.referer
            });

            // Log successful IP lookup
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.IP_LOOKUP_PERFORMED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'ip-intelligence',
                action: 'lookup',
                success: true,
                details: {
                    targetIp: ip,
                    riskScore: result.security.riskScore,
                    recommendation: result.security.recommendation,
                    usedCachedData: result.metadata.usedStoredData,
                    processingTime: result.metadata.processingTime
                }
            });

            res.json({ success: true, data: result });
        } catch (error) {
            Logger.error('IP lookup failed', error, {
                ip: req.body.ip,
                userId: req.user?.id,
                userEmail: req.user?.email
            }, {
                component: 'api-routes',
                operation: 'ip_lookup'
            });

            // Log failed IP lookup
            try {
                const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
                const { getClientIp } = await import('../middleware/rate-limit-middleware');
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.IP_LOOKUP_PERFORMED,
                    userId: req.user?.id,
                    email: req.user?.email,
                    ipAddress: getClientIp(req),
                    userAgent: req.headers['user-agent'],
                    resource: 'ip-intelligence',
                    action: 'lookup',
                    success: false,
                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                    details: {
                        targetIp: req.body.ip
                    }
                });
            } catch (logError) {
                Logger.error('Failed to log IP lookup failure', logError, {
                    originalError: error instanceof Error ? error.message : 'Unknown error'
                }, {
                    component: 'api-routes',
                    operation: 'log_failure'
                });
            }

            res.status(500).json({
                success: false,
                error: 'IP lookup failed',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// IP data refresh endpoint with validation
router.post('/admin/ip-intelligence/refresh/:ip',
    ...protect.securityMonitoring(),
    validate.ipRefresh,
    async (req, res) => {
        try {
            const { ipIntelligenceService } = await import('../services/ip-intelligence-service');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');
            const { ip } = req.params;

            const result = await ipIntelligenceService.refreshIpData(ip);

            // Log successful IP data refresh
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.IP_DATA_REFRESHED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'ip-intelligence',
                action: 'refresh',
                success: true,
                details: {
                    targetIp: ip,
                    refreshCount: result.refresh_count,
                    dataExpiresAt: result.data_expires_at
                }
            });

            res.json({
                success: true,
                data: result,
                message: 'IP data refreshed successfully'
            });
        } catch (error) {
            console.error('IP data refresh failed:', error);

            // Log failed IP data refresh
            try {
                const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
                const { getClientIp } = await import('../middleware/rate-limit-middleware');
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.IP_DATA_REFRESHED,
                    userId: req.user?.id,
                    email: req.user?.email,
                    ipAddress: getClientIp(req),
                    userAgent: req.headers['user-agent'],
                    resource: 'ip-intelligence',
                    action: 'refresh',
                    success: false,
                    errorMessage: error instanceof Error ? error.message : 'Unknown error',
                    details: {
                        targetIp: req.params.ip
                    }
                });
            } catch (logError) {
                console.error('Failed to log IP refresh failure:', logError);
            }

            res.status(500).json({
                success: false,
                error: 'IP data refresh failed',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// IP analysis history endpoint with filtering
router.get('/admin/ip-intelligence/history',
    ...protect.securityMonitoring(),
    validate.ipAnalysisHistory,
    async (req, res) => {
        try {
            const { prisma } = await import('../lib/prisma');
            const {
                page = 1,
                limit = 50,
                ip,
                source,
                recommendation,
                startDate,
                endDate
            } = req.query;

            const skip = (Number(page) - 1) * Number(limit);
            const take = Number(limit);

            // Build where clause for filtering
            const where: any = {};

            if (ip) {
                where.ip_address = { contains: ip as string };
            }

            if (source) {
                where.request_source = source as string;
            }

            if (recommendation) {
                where.recommendation = recommendation as string;
            }

            if (startDate || endDate) {
                where.requested_at = {};
                if (startDate) {
                    where.requested_at.gte = new Date(startDate as string);
                }
                if (endDate) {
                    where.requested_at.lte = new Date(endDate as string);
                }
            }

            // Get total count for pagination
            const total = await prisma.ipAnalysisRequest.count({ where });

            // Get paginated results
            const requests = await prisma.ipAnalysisRequest.findMany({
                where,
                skip,
                take,
                orderBy: { requested_at: 'desc' },
                include: {
                    ip_data: {
                        select: {
                            country_name: true,
                            city: true,
                            is_threat: true,
                            is_vpn: true,
                            is_proxy: true,
                            is_tor: true
                        }
                    },
                    installation: {
                        select: {
                            id: true,
                            url: true,
                            title: true
                        }
                    }
                }
            });

            // Log successful access to IP analysis history
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.IP_ANALYSIS_HISTORY_ACCESSED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'ip-intelligence',
                action: 'view_history',
                success: true,
                details: {
                    filters: { ip, source, recommendation, startDate, endDate },
                    resultCount: requests.length,
                    totalRecords: total,
                    page: Number(page),
                    limit: Number(limit)
                }
            });

            res.json({
                success: true,
                data: {
                    requests,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        pages: Math.ceil(total / Number(limit))
                    }
                }
            });
        } catch (error) {
            console.error('Failed to get IP analysis history:', error);

            // Log failed access to IP analysis history
            try {
                const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
                const { getClientIp } = await import('../middleware/rate-limit-middleware');
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.IP_ANALYSIS_HISTORY_ACCESSED,
                    userId: req.user?.id,
                    email: req.user?.email,
                    ipAddress: getClientIp(req),
                    userAgent: req.headers['user-agent'],
                    resource: 'ip-intelligence',
                    action: 'view_history',
                    success: false,
                    errorMessage: error instanceof Error ? error.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log IP history access failure:', logError);
            }

            res.status(500).json({
                success: false,
                error: 'Failed to retrieve IP analysis history',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Get IP analysis statistics
router.get('/admin/ip-intelligence/stats',
    ...protect.securityMonitoring(),
    async (req, res) => {
        try {
            const { ipIntelligenceService } = await import('../services/ip-intelligence-service');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');
            const timeRange = req.query.timeRange as 'hour' | 'day' | 'week' | 'month' || 'day';

            const stats = await ipIntelligenceService.getDataFreshnessStats();

            // Log successful access to IP stats
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.IP_STATS_ACCESSED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'ip-intelligence',
                action: 'view_stats',
                success: true,
                details: {
                    timeRange,
                    totalRequests: stats.totalRequests,
                    cacheHitRate: stats.cacheHitRate
                }
            });

            res.json({ success: true, data: stats });
        } catch (error) {
            console.error('Failed to get IP analysis stats:', error);

            // Log failed access to IP stats
            try {
                const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
                const { getClientIp } = await import('../middleware/rate-limit-middleware');
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.IP_STATS_ACCESSED,
                    userId: req.user?.id,
                    email: req.user?.email,
                    ipAddress: getClientIp(req),
                    userAgent: req.headers['user-agent'],
                    resource: 'ip-intelligence',
                    action: 'view_stats',
                    success: false,
                    errorMessage: error instanceof Error ? error.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log IP stats access failure:', logError);
            }

            res.status(500).json({
                success: false,
                error: 'Failed to retrieve IP analysis statistics',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Super Admin Only Routes
router.get('/admin/system-health',
    ...protect.superAdminOnly(),
    async (_req, res) => {
        // Placeholder for system health endpoint
        res.json({ message: 'System health - super admin only' });
    }
);

router.post('/admin/maintenance-mode',
    ...protect.superAdminOnly(),
    async (_req, res) => {
        // Placeholder for maintenance mode endpoint
        res.json({ message: 'Maintenance mode toggle - super admin only' });
    }
);

// Moderator and Above Routes
router.get('/content',
    ...protect.marketingAndAbove(),
    async (_req, res) => {
        // Placeholder for content listing endpoint
        res.json({ message: 'Content listing - requires moderator permissions or above' });
    }
);

router.post('/content',
    ...protect.marketingAndAbove(),
    async (_req, res) => {
        // Placeholder for content creation endpoint
        res.json({ message: 'Content creation - requires moderator permissions or above' });
    }
);

// Role-specific permission examples
router.get('/admin/user-roles',
    protect.requireRole(UserRole.DEV),
    async (_req, res) => {
        // Placeholder for user roles management
        res.json({ message: 'User roles management - requires admin role' });
    }
);

router.put('/admin/user-roles/:userId',
    protect.requireRole(UserRole.SUPER_ADMIN), // Only super admin can change roles
    async (req, res) => {
        // Placeholder for user role update
        res.json({ message: `Role update for user ${req.params.userId} - super admin only` });
    }
);

// Permission-specific examples
router.get('/reports/security',
    protect.requirePermissions([Permission.VIEW_SECURITY_METRICS, Permission.VIEW_AUDIT_LOGS]),
    async (_req, res) => {
        // Placeholder for security reports
        res.json({ message: 'Security reports - requires specific security permissions' });
    }
);

router.post('/system/backup',
    protect.requireRoleAndPermissions(UserRole.DEV, [Permission.UPDATE_SYSTEM_SETTINGS]),
    async (_req, res) => {
        // Placeholder for system backup
        res.json({ message: 'System backup - requires admin role and system settings permission' });
    }
);

// Freemius Data Management Endpoints
// Product listing and details endpoints
router.get('/admin/freemius/products',
    ...protect.securityMonitoring(),
    validate.freemiusProductList,
    async (req, res) => {
        try {
            const { prisma } = await import('../lib/prisma');
            const { page = 1, limit = 50, status } = req.query;

            const skip = (Number(page) - 1) * Number(limit);
            const take = Number(limit);

            // Build where clause for filtering
            const where: any = {};
            if (status) {
                where.sync_status = status as string;
            }

            // Get total count for pagination
            const total = await prisma.freemiusProduct.count({ where });

            // Get paginated results
            const products = await prisma.freemiusProduct.findMany({
                where,
                skip,
                take,
                orderBy: { last_synced_at: 'desc' },
                include: {
                    _count: {
                        select: {
                            installations: true,
                            events: true
                        }
                    }
                }
            });

            res.json({
                success: true,
                data: {
                    products,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        pages: Math.ceil(total / Number(limit))
                    }
                }
            });
        } catch (error) {
            console.error('Failed to get Freemius products:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve Freemius products',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Get specific product details
router.get('/admin/freemius/products/:productId',
    ...protect.securityMonitoring(),
    async (req, res) => {
        try {
            const { prisma } = await import('../lib/prisma');
            const { productId } = req.params;

            const product = await prisma.freemiusProduct.findUnique({
                where: { id: productId },
                include: {
                    installations: {
                        take: 10,
                        orderBy: { last_synced_at: 'desc' },
                        select: {
                            id: true,
                            url: true,
                            title: true,
                            is_active: true,
                            is_premium: true,
                            country_code: true,
                            last_seen_at: true
                        }
                    },
                    events: {
                        take: 10,
                        orderBy: { created: 'desc' },
                        select: {
                            id: true,
                            type: true,
                            created: true,
                            processing_status: true
                        }
                    },
                    _count: {
                        select: {
                            installations: true,
                            events: true
                        }
                    }
                }
            });

            if (!product) {
                return res.status(404).json({
                    success: false,
                    error: 'Product not found'
                });
            }

            res.json({ success: true, data: product });
        } catch (error) {
            console.error('Failed to get product details:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve product details',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Installation listing with filtering capabilities
router.get('/admin/freemius/installations',
    ...protect.securityMonitoring(),
    validate.freemiusInstallationList,
    async (req, res) => {
        try {
            const { prisma } = await import('../lib/prisma');
            const {
                page = 1,
                limit = 50,
                productId,
                status,
                country
            } = req.query;

            const skip = (Number(page) - 1) * Number(limit);
            const take = Number(limit);

            // Build where clause for filtering
            const where: any = {};

            if (productId) {
                where.plugin_id = productId as string;
            }

            if (status) {
                switch (status) {
                    case 'active':
                        where.is_active = true;
                        where.is_uninstalled = false;
                        break;
                    case 'inactive':
                        where.is_active = false;
                        break;
                    case 'premium':
                        where.is_premium = true;
                        where.is_active = true;
                        break;
                    case 'trial':
                        where.trial_ends = { not: null };
                        where.is_active = true;
                        break;
                }
            }

            if (country) {
                where.country_code = country as string;
            }

            // Get total count for pagination
            const total = await prisma.freemiusInstallation.count({ where });

            // Get paginated results
            const installations = await prisma.freemiusInstallation.findMany({
                where,
                skip,
                take,
                orderBy: { last_synced_at: 'desc' },
                include: {
                    product: {
                        select: {
                            id: true,
                            title: true,
                            slug: true
                        }
                    },
                    _count: {
                        select: {
                            events: true,
                            ip_requests: true
                        }
                    }
                }
            });

            res.json({
                success: true,
                data: {
                    installations,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        pages: Math.ceil(total / Number(limit))
                    }
                }
            });
        } catch (error) {
            console.error('Failed to get Freemius installations:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve Freemius installations',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Get specific installation details
router.get('/admin/freemius/installations/:installId',
    ...protect.securityMonitoring(),
    async (req, res) => {
        try {
            const { prisma } = await import('../lib/prisma');
            const { installId } = req.params;

            const installation = await prisma.freemiusInstallation.findUnique({
                where: { id: installId },
                include: {
                    product: true,
                    events: {
                        take: 20,
                        orderBy: { created: 'desc' }
                    },
                    ip_requests: {
                        take: 10,
                        orderBy: { requested_at: 'desc' },
                        include: {
                            ip_data: {
                                select: {
                                    country_name: true,
                                    city: true,
                                    is_threat: true,
                                    is_vpn: true,
                                    is_proxy: true
                                }
                            }
                        }
                    }
                }
            });

            if (!installation) {
                return res.status(404).json({
                    success: false,
                    error: 'Installation not found'
                });
            }

            res.json({ success: true, data: installation });
        } catch (error) {
            console.error('Failed to get installation details:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve installation details',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Manual Freemius data sync endpoint
router.post('/admin/freemius/sync',
    ...protect.securityMonitoring(),
    async (req, res) => {
        try {
            const { FreemiusService } = await import('../services/freemius-service');
            const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
            const { getClientIp } = await import('../middleware/rate-limit-middleware');
            const freemiusService = new FreemiusService();

            const result = await freemiusService.syncAllData();

            // Log Freemius data sync
            await SecurityLogger.logSecurityEvent({
                eventType: SecurityEventType.FREEMIUS_DATA_SYNCED,
                userId: req.user?.id,
                email: req.user?.email,
                ipAddress: getClientIp(req),
                userAgent: req.headers['user-agent'],
                resource: 'freemius',
                action: 'sync_all',
                success: result.success,
                details: {
                    productsUpdated: result.summary.productsUpdated,
                    installationsUpdated: result.summary.installationsUpdated,
                    eventsProcessed: result.summary.eventsProcessed,
                    errors: result.errors,
                    lastSyncTime: result.lastSyncTime
                }
            });

            res.json({
                success: result.success,
                data: result,
                message: result.success ? 'Freemius data synchronized successfully' : 'Synchronization completed with errors'
            });
        } catch (error) {
            Logger.error('Freemius sync failed', error, {
                userId: req.user?.id,
                userEmail: req.user?.email
            }, {
                component: 'api-routes',
                operation: 'freemius_sync'
            });

            // Log failed Freemius sync
            try {
                const { SecurityLogger, SecurityEventType } = await import('../lib/security-logger');
                const { getClientIp } = await import('../middleware/rate-limit-middleware');
                await SecurityLogger.logSecurityEvent({
                    eventType: SecurityEventType.FREEMIUS_DATA_SYNCED,
                    userId: req.user?.id,
                    email: req.user?.email,
                    ipAddress: getClientIp(req),
                    userAgent: req.headers['user-agent'],
                    resource: 'freemius',
                    action: 'sync_all',
                    success: false,
                    errorMessage: error instanceof Error ? error.message : 'Unknown error'
                });
            } catch (logError) {
                console.error('Failed to log Freemius sync failure:', logError);
            }

            res.status(500).json({
                success: false,
                error: 'Freemius synchronization failed',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Event history endpoint for webhook tracking
router.get('/admin/freemius/events',
    ...protect.securityMonitoring(),
    validate.freemiusEventHistory,
    async (req, res) => {
        try {
            const { prisma } = await import('../lib/prisma');
            const {
                page = 1,
                limit = 50,
                type,
                productId,
                status,
                startDate,
                endDate
            } = req.query;

            const skip = (Number(page) - 1) * Number(limit);
            const take = Number(limit);

            // Build where clause for filtering
            const where: any = {};

            if (type) {
                where.type = { contains: type as string };
            }

            if (productId) {
                where.plugin_id = productId as string;
            }

            if (status) {
                where.processing_status = status as string;
            }

            if (startDate || endDate) {
                where.created = {};
                if (startDate) {
                    where.created.gte = new Date(startDate as string);
                }
                if (endDate) {
                    where.created.lte = new Date(endDate as string);
                }
            }

            // Get total count for pagination
            const total = await prisma.freemiusEvent.count({ where });

            // Get paginated results
            const events = await prisma.freemiusEvent.findMany({
                where,
                skip,
                take,
                orderBy: { created: 'desc' },
                include: {
                    product: {
                        select: {
                            id: true,
                            title: true,
                            slug: true
                        }
                    },
                    installation: {
                        select: {
                            id: true,
                            url: true,
                            title: true
                        }
                    }
                }
            });

            res.json({
                success: true,
                data: {
                    events,
                    pagination: {
                        page: Number(page),
                        limit: Number(limit),
                        total,
                        pages: Math.ceil(total / Number(limit))
                    }
                }
            });
        } catch (error) {
            console.error('Failed to get Freemius events:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve Freemius events',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

// Get Freemius statistics
router.get('/admin/freemius/stats',
    ...protect.securityMonitoring(),
    async (_req, res) => {
        try {
            const { FreemiusService } = await import('../services/freemius-service');
            const freemiusService = new FreemiusService();

            const stats = await freemiusService.getStats();

            res.json({ success: true, data: stats });
        } catch (error) {
            console.error('Failed to get Freemius stats:', error);
            res.status(500).json({
                success: false,
                error: 'Failed to retrieve Freemius statistics',
                message: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }
);

export default router;