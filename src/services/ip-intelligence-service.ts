import axios, { AxiosInstance, AxiosError } from 'axios';
import { prisma } from '../lib/prisma';
import {
    logIpDataRefresh,
    logIpDataCleanup,
} from '../lib/security-logger';
import { externalApiRateLimiter } from '../lib/external-api-rate-limiter';
import { Logger } from '../lib/logger';
import { countryService } from './country-service';


// Configuration interface for ipRegistry API
interface IpIntelligenceConfig {
    apiUrl: string;
    apiKey: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
    dataFreshnessDays: number;
}

// Analysis context for IP requests
interface AnalysisContext {
    installationId?: string;
    requestSource: 'plugin' | 'admin' | 'webhook';
    userAgent?: string;
    referer?: string;
}

// Simple IP data result interface (proxy functionality)
interface IpDataResult {
    ip: string;
    data: IpRegistryApiResponse;
    metadata: {
        usedStoredData: boolean;
        lastUpdated: string;
        processingTime: number;
        dataAge: number; // in hours
    };
}

// ipRegistry API response interface (based on official documentation)
interface IpRegistryApiResponse {
    ip: string;
    type: 'IPv4' | 'IPv6';
    hostname?: string;
    carrier?: {
        name?: string;
        mcc?: string;
        mnc?: string;
    };
    company?: {
        domain: string;
        name: string;
        type: string;
    };
    connection: {
        asn: number;
        domain: string;
        organization: string;
        route: string;
        type: string;
    };
    currency?: {
        code: string;
        name: string;
        name_native: string;
        plural: string;
        plural_native: string;
        symbol: string;
        symbol_native: string;
        format: any;
    };
    location: {
        continent: {
            code: string;
            name: string;
        };
        country: {
            area: number;
            borders: string[];
            calling_code: string;
            capital: string;
            code: string;
            name: string;
            population: number;
            population_density: number;
            flag: any;
            languages: any[];
            tld: string;
        };
        region: {
            code: string;
            name: string;
        };
        city: string;
        postal: string;
        latitude: number;
        longitude: number;
        language: any;
        in_eu: boolean;
    };
    security: {
        is_abuser: boolean;
        is_attacker: boolean;
        is_bogon: boolean;
        is_cloud_provider: boolean;
        is_proxy: boolean;
        is_relay: boolean;
        is_tor: boolean;
        is_tor_exit: boolean;
        is_vpn: boolean;
        is_anonymous: boolean;
        is_threat: boolean;
    };
    time_zone: {
        id: string;
        abbreviation: string;
        current_time: string;
        name: string;
        offset: number;
        in_daylight_saving: boolean;
    };
}

export class IpIntelligenceService {
    private client: AxiosInstance;
    private config: IpIntelligenceConfig;

    constructor() {
        // Load configuration from environment variables
        this.config = {
            apiUrl: process.env.IPREGISTRY_API_URL || 'https://api.ipregistry.co',
            apiKey: process.env.IPREGISTRY_API_KEY || '',
            timeout: parseInt(process.env.IPREGISTRY_TIMEOUT || '10000'),
            retryAttempts: parseInt(process.env.IPREGISTRY_RETRY_ATTEMPTS || '2'),
            retryDelay: parseInt(process.env.IPREGISTRY_RETRY_DELAY || '1000'),
            dataFreshnessDays: parseInt(process.env.IP_DATA_FRESHNESS_DAYS || '3'),
        };

        // Initialize HTTP client
        this.client = axios.create({
            baseURL: this.config.apiUrl,
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'GuardGeo-Backend/1.0',
            },
            params: {
                key: this.config.apiKey,
            },
        });

        // Add interceptors for error handling and logging
        this.client.interceptors.request.use(
            (config) => {
                // Add Sentry breadcrumb for API request
                Logger.info('ipRegistry API Request', {
                    url: config.url,
                    method: config.method?.toUpperCase(),
                    ip: config.url?.split('/').pop()
                }, {
                    service: 'ip-intelligence',
                    operation: 'api_request'
                });
                return config;
            },
            (error) => Promise.reject(error)
        );

        this.client.interceptors.response.use(
            (response) => {
                // Add Sentry breadcrumb for successful API response
                Logger.info('ipRegistry API Response Success', {
                    url: response.config.url,
                    status: response.status,
                    ip: response.config.url?.split('/').pop()
                }, {
                    service: 'ip-intelligence',
                    operation: 'api_response'
                });
                return response;
            },
            (error: AxiosError) => {
                this.logError('ipRegistry API Request Failed', {
                    error,
                    url: error.config?.url,
                    method: error.config?.method,
                    status: error.response?.status,
                    message: error.message,
                    data: error.response?.data,
                });
                return Promise.reject(error);
            }
        );
    }

    /**
     * Get IP data as simple proxy to ipRegistry API
     */
    async getIpData(ip: string, context?: AnalysisContext): Promise<IpDataResult> {
        const startTime = Date.now();

        try {
            this.logInfo(`Starting IP data retrieval: ${ip}`, { context });

            // Validate IP address format and check for private/reserved IPs
            if (!this.isValidPublicIp(ip)) {
                throw new Error(`Invalid or private IP address: ${ip}`);
            }

            // Check if we have fresh data in the database
            const storedData = await this.getStoredData(ip);
            const isFresh = storedData && await this.isDataFresh(ip);

            let ipRegistryData: IpRegistryApiResponse;
            let usedStoredData = false;

            if (isFresh && storedData && storedData.raw_response) {
                // Use stored data - return the complete ipRegistry response
                ipRegistryData = storedData.raw_response as IpRegistryApiResponse;
                usedStoredData = true;
                this.logInfo(`Using cached IP data: ${ip}`);
            } else {
                // Fetch fresh data from API
                const refreshedData = await this.refreshIpData(ip);
                ipRegistryData = refreshedData.raw_response as IpRegistryApiResponse;
                usedStoredData = false;
                this.logInfo(`Fetched fresh IP data: ${ip}`);
            }

            // Log the data request
            await this.logDataRequest(ip, context, {
                processingTime: Date.now() - startTime,
                usedStoredData,
            });

            // Build simple data result
            const result: IpDataResult = {
                ip,
                data: ipRegistryData,
                metadata: {
                    usedStoredData,
                    lastUpdated: storedData?.last_refreshed_at?.toISOString() || new Date().toISOString(),
                    processingTime: Date.now() - startTime,
                    dataAge: storedData ? this.calculateDataAge(storedData.last_refreshed_at || storedData.created_at) : 0,
                },
            };

            this.logInfo(`IP data retrieval completed: ${ip}`, {
                processingTime: result.metadata.processingTime,
                usedStoredData
            });

            return result;
        } catch (error) {
            const errorMsg = `IP data retrieval failed for ${ip}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('IP Data Retrieval Failed', { ip, error: errorMsg, context });

            // Log failed data request
            await this.logDataRequest(ip, context, {
                processingTime: Date.now() - startTime,
                usedStoredData: false,
                error: errorMsg,
            });

            throw new Error(errorMsg);
        }
    }

    /**
     * Refresh IP data from ipRegistry API
     */
    async refreshIpData(ip: string): Promise<any> {
        try {
            this.logInfo(`Refreshing IP data from API: ${ip}`);

            // Fetch from ipRegistry API with retry logic
            const apiResponse = await this.retryApiCall(async () => {
                const response = await this.client.get(`/${ip}?fields=ip,type,hostname,carrier,company,connection,currency,location,security,time_zone`);
                return response.data;
            });

            // Transform API response to our database format
            const ipData = this.transformApiResponse(apiResponse);

            // Store in database
            const storedData = await prisma.ipRegistryData.upsert({
                where: { ip_address: ip },
                update: {
                    ...ipData,
                    last_refreshed_at: new Date(),
                    data_expires_at: new Date(Date.now() + (this.config.dataFreshnessDays * 24 * 60 * 60 * 1000)),
                    refresh_count: {
                        increment: 1,
                    },
                },
                create: {
                    ip_address: ip,
                    ...ipData,
                    last_refreshed_at: new Date(),
                    data_expires_at: new Date(Date.now() + (this.config.dataFreshnessDays * 24 * 60 * 60 * 1000)),
                    refresh_count: 1,
                },
            });

            // Log successful data refresh
            await logIpDataRefresh(
                ip,
                true,
                this.calculateDataAge(storedData.last_refreshed_at)
            );

            this.logInfo(`IP data refreshed and stored: ${ip}`);
            return storedData;
        } catch (error) {
            const errorMsg = `Failed to refresh IP data for ${ip}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('IP Data Refresh Failed', { ip, error: errorMsg });

            // Log failed data refresh
            await logIpDataRefresh(
                ip,
                false,
                undefined,
                undefined,
                undefined,
                undefined,
                undefined,
                errorMsg
            );

            throw new Error(errorMsg);
        }
    }

    /**
     * Get stored IP data from database
     */
    async getStoredData(ip: string): Promise<any | null> {
        try {
            const data = await prisma.ipRegistryData.findUnique({
                where: { ip_address: ip },
            });

            return data;
        } catch (error) {
            this.logError('Get Stored Data Failed', { ip, error: error instanceof Error ? error.message : 'Unknown error' });
            return null;
        }
    }

    /**
     * Check if stored IP data is still fresh based on last_refreshed_at timestamp
     */
    async isDataFresh(ip: string): Promise<boolean> {
        try {
            const data = await this.getStoredData(ip);
            if (!data || !data.last_refreshed_at) {
                return false;
            }

            const now = new Date();
            const lastRefreshed = new Date(data.last_refreshed_at);
            const freshnessThresholdMs = this.config.dataFreshnessDays * 24 * 60 * 60 * 1000;
            const dataAge = now.getTime() - lastRefreshed.getTime();

            const isFresh = dataAge < freshnessThresholdMs;

            this.logInfo(`Data freshness check for ${ip}`, {
                lastRefreshed: lastRefreshed.toISOString(),
                dataAgeHours: Math.floor(dataAge / (60 * 60 * 1000)),
                freshnessThresholdHours: this.config.dataFreshnessDays * 24,
                isFresh
            });

            return isFresh;
        } catch (error) {
            this.logError('Data Freshness Check Failed', { ip, error: error instanceof Error ? error.message : 'Unknown error' });
            return false;
        }
    }

    /**
     * Clean up expired IP data from database based on data_expires_at field
     */
    async cleanupExpiredData(): Promise<number> {
        try {
            this.logInfo('Starting cleanup of expired IP data');

            const now = new Date();

            // Find expired records first for logging
            const expiredRecords = await prisma.ipRegistryData.findMany({
                where: {
                    data_expires_at: {
                        lt: now,
                    },
                },
                select: {
                    ip_address: true,
                    last_refreshed_at: true,
                    data_expires_at: true,
                },
            });

            this.logInfo(`Found ${expiredRecords.length} expired records to clean up`);

            // Delete expired records
            const result = await prisma.ipRegistryData.deleteMany({
                where: {
                    data_expires_at: {
                        lt: now,
                    },
                },
            });

            // Log cleanup operation
            await logIpDataCleanup(result.count);

            this.logInfo(`Cleanup completed: ${result.count} expired records removed`, {
                cleanupTimestamp: now.toISOString(),
                expiredRecordsCount: result.count
            });

            return result.count;
        } catch (error) {
            const errorMsg = `Failed to cleanup expired data: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Data Cleanup Failed', { error: errorMsg });
            return 0;
        }
    }

    /**
     * Get data freshness statistics
     */
    async getDataFreshnessStats(): Promise<{
        totalRecords: number;
        freshRecords: number;
        expiredRecords: number;
        averageDataAge: number; // in hours
    }> {
        try {
            const now = new Date();
            const freshnessThresholdMs = this.config.dataFreshnessDays * 24 * 60 * 60 * 1000;

            const allRecords = await prisma.ipRegistryData.findMany({
                select: {
                    last_refreshed_at: true,
                    data_expires_at: true,
                },
            });

            const totalRecords = allRecords.length;
            let freshRecords = 0;
            let expiredRecords = 0;
            let totalAgeMs = 0;

            allRecords.forEach(record => {
                if (record.last_refreshed_at) {
                    const dataAge = now.getTime() - new Date(record.last_refreshed_at).getTime();
                    totalAgeMs += dataAge;

                    if (dataAge < freshnessThresholdMs) {
                        freshRecords++;
                    } else {
                        expiredRecords++;
                    }
                } else {
                    expiredRecords++;
                }
            });

            const averageDataAge = totalRecords > 0 ? totalAgeMs / totalRecords / (60 * 60 * 1000) : 0;

            return {
                totalRecords,
                freshRecords,
                expiredRecords,
                averageDataAge,
            };
        } catch (error) {
            this.logError('Failed to get data freshness stats', { error: error instanceof Error ? error.message : 'Unknown error' });
            return {
                totalRecords: 0,
                freshRecords: 0,
                expiredRecords: 0,
                averageDataAge: 0,
            };
        }
    }

    /**
     * Transform ipRegistry API response to our database format
     */
    private transformApiResponse(apiResponse: IpRegistryApiResponse): any {
        return {
            // Core geolocation data
            country_code: apiResponse.location?.country?.code || null,
            country_name: apiResponse.location?.country?.name || null,
            continent_code: apiResponse.location?.continent?.code || null, // Enhanced: continent code
            continent_name: apiResponse.location?.continent?.name || null, // Enhanced: continent name
            region_code: apiResponse.location?.region?.code || null,
            region_name: apiResponse.location?.region?.name || null,
            city: apiResponse.location?.city || null,
            postal_code: apiResponse.location?.postal || null,
            latitude: apiResponse.location?.latitude || null,
            longitude: apiResponse.location?.longitude || null,
            timezone: apiResponse.time_zone?.id || null,

            // ISP and network data
            isp: apiResponse.connection?.organization || null,
            organization: apiResponse.connection?.organization || null,
            asn: apiResponse.connection?.asn?.toString() || null,
            asn_name: apiResponse.connection?.organization || null,
            isp_domain: apiResponse.connection?.domain || null, // Enhanced: ISP domain
            route_prefix: apiResponse.connection?.route || null, // Enhanced: route prefix

            // Security and threat data
            is_threat: apiResponse.security?.is_threat || false,
            threat_types: apiResponse.security ? this.extractThreatTypes(apiResponse.security) : null,
            is_bogon: apiResponse.security?.is_bogon || false,
            is_cloud_provider: apiResponse.security?.is_cloud_provider || false,
            is_tor: apiResponse.security?.is_tor || false,
            is_proxy: apiResponse.security?.is_proxy || false,
            is_vpn: apiResponse.security?.is_vpn || false,
            is_malware: apiResponse.security?.is_attacker || false, // Map attacker to malware
            is_abuser: apiResponse.security?.is_abuser || false, // Enhanced: abuser flag
            is_attacker: apiResponse.security?.is_attacker || false, // Enhanced: attacker flag
            is_relay: apiResponse.security?.is_relay || false, // Enhanced: relay flag
            is_tor_exit: apiResponse.security?.is_tor_exit || false, // Enhanced: tor exit flag
            is_anonymous: apiResponse.security?.is_anonymous || false, // Enhanced: anonymous flag

            // Additional metadata
            connection_type: apiResponse.connection?.type || null,
            user_agent: null, // Not provided by ipRegistry
            hostname: apiResponse.hostname || null, // Enhanced: hostname
            ip_type: apiResponse.type || null, // Enhanced: IP type (IPv4/IPv6)
            raw_response: apiResponse, // Enhanced: Complete API response - now required
        };
    }

    /**
     * Extract threat types from security object
     */
    private extractThreatTypes(security: any): string[] | null {
        const threats: string[] = [];

        if (security.is_abuser) threats.push('abuser');
        if (security.is_attacker) threats.push('attacker');
        if (security.is_relay) threats.push('relay');
        if (security.is_tor_exit) threats.push('tor_exit');
        if (security.is_anonymous) threats.push('anonymous');

        return threats.length > 0 ? threats : null;
    }

    /**
     * Calculate data age in hours
     */
    private calculateDataAge(lastRefreshed: string | Date): number {
        const now = new Date();
        const refreshedAt = new Date(lastRefreshed);
        const diffMs = now.getTime() - refreshedAt.getTime();
        return Math.floor(diffMs / (1000 * 60 * 60)); // Convert to hours
    }

    /**
     * Validate if IP is a valid public IP address
     */
    isValidPublicIp(ip: string): boolean {
        if (!ip || typeof ip !== 'string') {
            return false;
        }

        // Sanitize the IP first
        const sanitizedIp = this.sanitizeIp(ip);
        if (!sanitizedIp) {
            return false;
        }

        // First check if it's a valid IP format
        if (!this.isValidIpAddress(sanitizedIp)) {
            return false;
        }

        // Then check if it's not private or reserved
        return !this.isPrivateOrReservedIp(sanitizedIp);
    }

    /**
     * Check if IP is private or reserved
     */
    isPrivateOrReservedIp(ip: string): boolean {
        if (!ip || typeof ip !== 'string') {
            return true;
        }

        const sanitizedIp = this.sanitizeIp(ip);
        if (!sanitizedIp) {
            return true;
        }

        // Private and reserved IPv4 ranges
        const privateRanges = [
            // RFC 1918 - Private networks
            /^10\./,                    // 10.0.0.0/8 - Private
            /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12 - Private
            /^192\.168\./,              // ***********/16 - Private

            // RFC 3927 - Link-local
            /^169\.254\./,              // ***********/16 - Link-local

            // RFC 5735 - Special use addresses
            /^127\./,                   // *********/8 - Loopback
            /^0\./,                     // 0.0.0.0/8 - Current network
            /^224\./,                   // *********/4 - Multicast
            /^240\./,                   // 240.0.0.0/4 - Reserved
            /^255\.255\.255\.255$/,     // Broadcast

            // RFC 6598 - Carrier-grade NAT
            /^100\.(6[4-9]|[7-9][0-9]|1[0-1][0-9]|12[0-7])\./,  // **********/10

            // RFC 3068 - 6to4 relay anycast
            /^192\.88\.99\./,           // ***********/24

            // RFC 2544 - Benchmarking
            /^198\.(1[8-9])\./,         // **********/15

            // IPv6 special addresses
            /^::1$/,                    // IPv6 loopback
            /^::$/,                     // IPv6 unspecified
            /^fc00:/i,                  // IPv6 unique local (RFC 4193)
            /^fd00:/i,                  // IPv6 unique local (RFC 4193)
            /^fe80:/i,                  // IPv6 link-local (RFC 4291)
            /^ff00:/i,                  // IPv6 multicast (RFC 4291)
            /^2001:db8:/i,              // IPv6 documentation (RFC 3849)
            /^2001:10:/i,               // IPv6 ORCHID (RFC 4843)
            /^2002:/i,                  // IPv6 6to4 (RFC 3056)
        ];

        return privateRanges.some(range => range.test(sanitizedIp));
    }

    /**
     * Sanitize and normalize IP address input
     */
    sanitizeIp(ip: any): string | null {
        if (!ip) {
            return null;
        }

        // Convert to string and trim whitespace
        let sanitized = String(ip).trim();

        // Remove common prefixes that might be added by proxies
        sanitized = sanitized.replace(/^::ffff:/i, ''); // IPv4-mapped IPv6

        // Basic length check
        if (sanitized.length === 0 || sanitized.length > 45) { // Max IPv6 length is 39, but allow some buffer
            return null;
        }

        // Remove any non-IP characters (but allow IPv6 colons and dots)
        if (!/^[0-9a-fA-F:.]+$/.test(sanitized)) {
            return null;
        }

        return sanitized;
    }

    /**
     * Validate IPv4 address format specifically
     */
    isValidIpv4(ip: string): boolean {
        if (!ip || typeof ip !== 'string') {
            return false;
        }

        const sanitized = this.sanitizeIp(ip);
        if (!sanitized) {
            return false;
        }

        // IPv4 regex with strict validation
        const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

        if (!ipv4Regex.test(sanitized)) {
            return false;
        }

        // Additional validation: check each octet
        const octets = sanitized.split('.');
        return octets.every(octet => {
            const num = parseInt(octet, 10);
            return num >= 0 && num <= 255 && octet === num.toString();
        });
    }

    /**
     * Validate IPv6 address format specifically
     */
    isValidIpv6(ip: string): boolean {
        if (!ip || typeof ip !== 'string') {
            return false;
        }

        const sanitized = this.sanitizeIp(ip);
        if (!sanitized) {
            return false;
        }

        // IPv6 regex patterns
        const ipv6Patterns = [
            // Full form: 8 groups of 4 hex digits
            /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,

            // Compressed form with ::
            /^(?:[0-9a-fA-F]{1,4}:)*::(?:[0-9a-fA-F]{1,4}:)*[0-9a-fA-F]{1,4}$/,

            // Special cases
            /^::1$/,  // Loopback
            /^::$/,   // Unspecified

            // IPv4-mapped IPv6
            /^::ffff:(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/i,
        ];

        return ipv6Patterns.some(pattern => pattern.test(sanitized));
    }

    /**
     * Validate IP address format (both IPv4 and IPv6)
     */
    private isValidIpAddress(ip: string): boolean {
        if (!ip || typeof ip !== 'string') {
            return false;
        }

        return this.isValidIpv4(ip) || this.isValidIpv6(ip);
    }

    /**
     * Get IP version (4 or 6)
     */
    getIpVersion(ip: string): 4 | 6 | null {
        if (!ip || typeof ip !== 'string') {
            return null;
        }

        const sanitized = this.sanitizeIp(ip);
        if (!sanitized) {
            return null;
        }

        if (this.isValidIpv4(sanitized)) {
            return 4;
        } else if (this.isValidIpv6(sanitized)) {
            return 6;
        }

        return null;
    }

    /**
     * Normalize IP address (expand IPv6, etc.)
     */
    normalizeIp(ip: string): string | null {
        const sanitized = this.sanitizeIp(ip);
        if (!sanitized) {
            return null;
        }

        if (this.isValidIpv4(sanitized)) {
            return sanitized;
        }

        if (this.isValidIpv6(sanitized)) {
            // For IPv6, we could expand compressed notation, but for simplicity
            // we'll just return the sanitized version
            return sanitized.toLowerCase();
        }

        return null;
    }

    /**
     * Log data request for tracking and performance metrics
     */
    private async logDataRequest(
        ip: string,
        context?: AnalysisContext,
        result?: {
            processingTime: number;
            usedStoredData: boolean;
            error?: string;
        }
    ): Promise<void> {
        try {
            await prisma.ipAnalysisRequest.create({
                data: {
                    ip_address: ip,
                    installation_id: context?.installationId || undefined,
                    request_source: context?.requestSource || 'admin',
                    user_agent: context?.userAgent || undefined,
                    referer: context?.referer || undefined,
                    analysis_result: result?.error ? {
                        error: result.error,
                    } : undefined,
                    risk_score: undefined, // No longer calculating risk scores
                    recommendation: undefined, // No longer making recommendations
                    processing_time_ms: result?.processingTime || null,
                    used_cached_data: result?.usedStoredData || false,
                    requested_at: new Date(),
                    completed_at: result ? new Date() : null,
                },
            });
        } catch (error) {
            this.logError('Failed to log data request', {
                ip,
                context,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Retry API calls with exponential backoff and rate limiting
     */
    private async retryApiCall<T>(apiCall: () => Promise<T>): Promise<T> {
        let lastError: Error;

        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                // Check rate limit before making the call
                const rateLimitResult = await externalApiRateLimiter.checkRateLimit('ipregistry');

                if (!rateLimitResult.allowed) {
                    const waitTime = rateLimitResult.retryAfterMs || rateLimitResult.backoffMs || 30000;
                    this.logInfo(`Rate limit exceeded, waiting ${waitTime}ms before retry`);
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    continue; // Don't count this as a retry attempt
                }

                const result = await apiCall();

                // Record successful request
                await externalApiRateLimiter.recordRequest('ipregistry');

                return result;
            } catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');

                // Record failed request for rate limiting
                await externalApiRateLimiter.recordFailure('ipregistry', lastError);

                if (attempt === this.config.retryAttempts) {
                    throw lastError;
                }

                // Calculate exponential backoff delay
                const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
                this.logInfo(`API call failed, retrying in ${delay}ms (attempt ${attempt}/${this.config.retryAttempts})`);

                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        throw lastError!;
    }

    /**
     * Logging methods
     */
    private logInfo(message: string, data?: any): void {
        Logger.info(`[IpIntelligenceService] ${message}`, data, {
            service: 'ip-intelligence',
            component: 'IpIntelligenceService'
        });
    }

    private logError(message: string, data?: any): void {
        const error = data?.error instanceof Error ? data.error : new Error(message);
        Logger.error(`[IpIntelligenceService] ${message}`, error, data, {
            service: 'ip-intelligence',
            component: 'IpIntelligenceService'
        });
    }

    /**
     * Get data request statistics for monitoring
     */
    async getRequestStats(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<{
        totalRequests: number;
        cacheHitRate: number;
        averageProcessingTime: number;
        topCountries: Array<{ country: string; count: number }>;
    }> {
        try {
            const now = new Date();
            let startDate: Date;

            switch (timeRange) {
                case 'hour':
                    startDate = new Date(now.getTime() - (60 * 60 * 1000));
                    break;
                case 'day':
                    startDate = new Date(now.getTime() - (24 * 60 * 60 * 1000));
                    break;
                case 'week':
                    startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
                    break;
                case 'month':
                    startDate = new Date(now.getTime() - (30 * 24 * 60 * 60 * 1000));
                    break;
                default:
                    startDate = new Date(now.getTime() - (24 * 60 * 60 * 1000));
            }

            // Get analysis requests in time range
            const requests = await prisma.ipAnalysisRequest.findMany({
                where: {
                    requested_at: {
                        gte: startDate,
                    },
                    completed_at: {
                        not: null,
                    },
                },
                include: {
                    ip_data: true,
                },
            });

            const totalRequests = requests.length;
            const cachedRequests = requests.filter((r: any) => r.used_cached_data).length;
            const cacheHitRate = totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;

            const processingTimes = requests
                .filter((r: any) => r.processing_time_ms !== null)
                .map((r: any) => r.processing_time_ms!);
            const averageProcessingTime = processingTimes.length > 0
                ? processingTimes.reduce((a: number, b: number) => a + b, 0) / processingTimes.length
                : 0;

            // Top countries (from IP data)
            const countryCount: { [key: string]: number } = {};
            requests.forEach((r: any) => {
                if (r.ip_data?.country_name) {
                    countryCount[r.ip_data.country_name] = (countryCount[r.ip_data.country_name] || 0) + 1;
                }
            });

            const topCountries = Object.entries(countryCount)
                .sort(([, a], [, b]) => b - a)
                .slice(0, 10)
                .map(([country, count]) => ({ country, count }));

            return {
                totalRequests,
                cacheHitRate,
                averageProcessingTime,
                topCountries,
            };
        } catch (error) {
            this.logError('Failed to get request stats', {
                timeRange,
                error: error instanceof Error ? error.message : 'Unknown error'
            });

            return {
                totalRequests: 0,
                cacheHitRate: 0,
                averageProcessingTime: 0,
                topCountries: [],
            };
        }
    }

    /**
     * Enhance IP data with country information using country.is API
     */
    async enhanceWithCountryData(ip: string, ipRegistryData: IpRegistryApiResponse): Promise<IpRegistryApiResponse> {
        try {
            // If ipRegistry already has country data, use it
            if (ipRegistryData.location?.country?.name) {
                this.logInfo(`Using existing country data from ipRegistry for ${ip}`, {
                    country: ipRegistryData.location.country.name
                });
                return ipRegistryData;
            }

            // Try to get country data from country.is API as fallback
            this.logInfo(`Attempting to enhance IP data with country.is for ${ip}`);
            const countryInfo = await countryService.getCountryByIp(ip);

            if (countryInfo && countryInfo.country) {
                // Enhance the ipRegistry data with country information
                const enhancedData = {
                    ...ipRegistryData,
                    location: {
                        ...ipRegistryData.location,
                        country: {
                            ...ipRegistryData.location?.country,
                            name: countryInfo.country,
                            code: countryInfo.country, // country.is returns country code
                        }
                    }
                };

                this.logInfo(`Enhanced IP data with country.is for ${ip}`, {
                    country: countryInfo.country
                });

                return enhancedData;
            }

            this.logInfo(`No country enhancement available for ${ip}`);
            return ipRegistryData;
        } catch (error) {
            this.logError('Failed to enhance IP data with country information', {
                ip,
                error: error instanceof Error ? error.message : 'Unknown error'
            });

            // Return original data on error
            return ipRegistryData;
        }
    }

    /**
     * Get service health status including country service
     */
    async getHealthStatus(): Promise<{
        ipRegistry: { status: 'healthy' | 'unhealthy'; responseTime?: number; error?: string };
        countryService: { status: 'healthy' | 'unhealthy'; responseTime?: number; error?: string };
        database: { status: 'healthy' | 'unhealthy'; error?: string };
    }> {
        const results: {
            ipRegistry: { status: 'healthy' | 'unhealthy'; responseTime?: number; error?: string };
            countryService: { status: 'healthy' | 'unhealthy'; responseTime?: number; error?: string };
            database: { status: 'healthy' | 'unhealthy'; error?: string };
        } = {
            ipRegistry: { status: 'unhealthy', error: 'Not tested' },
            countryService: { status: 'unhealthy', error: 'Not tested' },
            database: { status: 'unhealthy', error: 'Not tested' }
        };

        // Test ipRegistry API
        try {
            const startTime = Date.now();
            await this.client.get('/*******?fields=ip');
            results.ipRegistry = {
                status: 'healthy',
                responseTime: Date.now() - startTime
            };
        } catch (error) {
            results.ipRegistry = {
                status: 'unhealthy',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }

        // Test country service
        try {
            const countryHealth = await countryService.getHealthStatus();
            results.countryService = countryHealth;
        } catch (error) {
            results.countryService = {
                status: 'unhealthy',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }

        // Test database
        try {
            await prisma.ipRegistryData.findFirst();
            results.database.status = 'healthy';
            delete results.database.error;
        } catch (error) {
            results.database.status = 'unhealthy';
            results.database.error = error instanceof Error ? error.message : 'Unknown error';
        }

        return results;
    }
}

// Export singleton instance
export const ipIntelligenceService = new IpIntelligenceService();