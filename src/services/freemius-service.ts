import axios, { AxiosInstance, AxiosError } from 'axios';
import { prisma } from '../lib/prisma';
import { FreemiusProduct, FreemiusInstallation, FreemiusEvent } from '../types';
import crypto from 'crypto';
import {
    logFreemiusApiRequest,
    logFreemiusApiSuccess,
    logFreemiusApiFailed,
    logFreemiusSyncStarted,
    logFreemiusSyncCompleted,
    logFreemiusSyncFailed,
    logFreemiusWebhookReceived,
    logFreemiusWebhookProcessed,
    logFreemiusWebhookFailed,
} from '../lib/security-logger';
import { externalApiRateLimiter } from '../lib/external-api-rate-limiter';
import { Logger } from '../lib/logger';

// Configuration interface for Freemius API
interface FreemiusConfig {
    apiUrl: string;
    apiKey: string;
    productId: string;
    productSecretKey: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
}

// Result interfaces
interface SyncResult {
    success: boolean;
    summary: {
        productsUpdated: number;
        installationsUpdated: number;
        eventsProcessed: number;
    };
    errors: string[];
    lastSyncTime: string;
}

interface ProcessingResult {
    success: boolean;
    eventId: string;
    processed: boolean;
    error?: string;
}

interface WebhookValidationResult {
    isValid: boolean;
    error?: string;
}

interface ValidationResult {
    isValid: boolean;
    errors: string[];
    installation?: FreemiusInstallation;
}

// Complete API response interfaces based on Freemius API documentation and database schema
interface FreemiusApiProduct {
    secret_key?: string;
    public_key?: string;
    id: string;
    created?: string;
    updated?: string;
    parent_plugin_id?: string;
    developer_id?: string;
    store_id?: string;
    slug: string;
    title?: string;
    name?: string;
    environment?: number;
    icon?: string;
    default_plan_id?: string;
    plans?: string;
    features?: string;
    money_back_period?: number;
    refund_policy?: 'flexible' | 'moderate' | 'strict';
    annual_renewals_discount?: number;
    renewals_discount_type?: 'percentage' | 'dollar';
    is_released?: boolean;
    is_sdk_required?: boolean;
    is_pricing_visible?: boolean;
    is_wp_org_compliant?: boolean;
    installs_count?: number;
    active_installs_count?: number;
    free_releases_count?: number;
    premium_releases_count?: number;
    total_purchases?: number;
    total_subscriptions?: number;
    total_renewals?: number;
    total_failed_purchases?: number;
    earnings?: number;
    type?: 'plugin' | 'theme' | 'widget' | 'template';
    is_static?: boolean;
}

interface FreemiusApiInstallation {
    secret_key?: string;
    public_key?: string;
    id: string;
    created?: string;
    updated?: string;
    site_id?: string;
    plugin_id?: string;
    user_id?: string;
    url?: string;
    title?: string;
    name?: string;
    version?: string;
    plan_id?: string;
    license_id?: string;
    trial_plan_id?: string;
    trial_ends?: string;
    subscription_id?: string;
    gross?: number;
    country_code?: string;
    language?: string;
    platform_version?: string;
    sdk_version?: string;
    programming_language_version?: string;
    is_active?: boolean;
    is_disconnected?: boolean;
    is_premium?: boolean;
    is_uninstalled?: boolean;
    is_locked?: boolean;
    source?: number;
    upgraded?: string;
    last_seen_at?: string;
    last_served_update_version?: string;
    is_beta?: boolean;
}

interface FreemiusWebhookEvent {
    id: string;
    created: string;
    updated?: string;
    type: string;
    developer_id?: string;
    plugin_id?: string;
    user_id?: string;
    install_id?: string;
    data?: any;
    event_trigger?: 'system' | 'developer' | 'plugin' | 'user' | 'install';
    process_time?: string;
    objects?: {
        user?: any;
        install?: any;
        license?: any;
        payment?: any;
        subscription?: any;
        plan?: any;
        [key: string]: any;
    };
}

// Statistics and analytics interfaces
interface FreemiusStats {
    totalProducts: number;
    totalInstallations: number;
    activeInstallations: number;
    premiumInstallations: number;
    totalRevenue: number;
    monthlyRevenue: number;
    conversionRate: number;
    churnRate: number;
    averageRevenuePerUser: number;
    topCountries: Array<{
        country: string;
        count: number;
        revenue: number;
    }>;
    recentEvents: FreemiusEvent[];
    installationTrends: Array<{
        date: string;
        installs: number;
        uninstalls: number;
        activations: number;
    }>;
}

interface InstallationFilters {
    filter?: 'all' | 'active' | 'inactive' | 'trial' | 'paying' | 'uninstalled' | 'active_premium' | 'active_free';
    fields?: string[];
    limit?: number;
    offset?: number;
    sort?: 'created' | 'updated' | 'last_seen_at';
    order?: 'asc' | 'desc';
}

export class FreemiusService {
    private client: AxiosInstance;
    private config: FreemiusConfig;

    constructor() {
        // Load configuration from environment variables
        this.config = {
            apiUrl: process.env.FREEMIUS_API_URL || 'https://api.freemius.com/v1',
            apiKey: process.env.FREEMIUS_API_KEY || '',
            productId: process.env.FREEMIUS_PRODUCT_ID || '',
            productSecretKey: process.env.FREEMIUS_PRODUCT_SECRET_KEY || '',
            timeout: parseInt(process.env.FREEMIUS_TIMEOUT || '30000'),
            retryAttempts: parseInt(process.env.FREEMIUS_RETRY_ATTEMPTS || '3'),
            retryDelay: parseInt(process.env.FREEMIUS_RETRY_DELAY || '1000'),
        };

        // Validate required configuration
        if (!this.config.apiKey) {
            throw new Error('FREEMIUS_API_KEY is required');
        }
        if (!this.config.productId) {
            throw new Error('FREEMIUS_PRODUCT_ID is required');
        }
        if (!this.config.productSecretKey) {
            throw new Error('FREEMIUS_PRODUCT_SECRET_KEY is required for webhook validation');
        }

        // Initialize HTTP client
        this.client = axios.create({
            baseURL: this.config.apiUrl,
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'GuardGeo-Backend/1.0',
                'Authorization': `Bearer ${this.config.apiKey}`,
            },
        });

        // Add response interceptor for error handling and logging
        this.client.interceptors.request.use(
            (config) => {
                // Log API request
                logFreemiusApiRequest(
                    config.url || 'unknown',
                    config.method?.toUpperCase() || 'GET',
                    undefined, // userId - would need to be passed in context
                    undefined, // email - would need to be passed in context
                    undefined, // ipAddress - would need to be passed in context
                    undefined, // userAgent - would need to be passed in context
                    this.config.productId
                );

                // Add Sentry breadcrumb for API request
                Logger.info('Freemius API Request', {
                    url: config.url,
                    method: config.method?.toUpperCase(),
                    productId: this.config.productId
                }, {
                    service: 'freemius',
                    operation: 'api_request'
                });

                return config;
            },
            (error) => Promise.reject(error)
        );

        this.client.interceptors.response.use(
            (response) => {
                // Log successful API response
                logFreemiusApiSuccess(
                    response.config.url || 'unknown',
                    response.status,
                    Date.now() - (response.config as any).requestStartTime || 0,
                    undefined, // userId - would need to be passed in context
                    undefined, // email - would need to be passed in context
                    undefined, // ipAddress - would need to be passed in context
                    undefined, // userAgent - would need to be passed in context
                    this.config.productId
                );

                // Add Sentry breadcrumb for successful API response
                Logger.info('Freemius API Response Success', {
                    url: response.config.url,
                    status: response.status,
                    productId: this.config.productId
                }, {
                    service: 'freemius',
                    operation: 'api_response'
                });

                return response;
            },
            (error: AxiosError) => {
                // Log failed API response
                logFreemiusApiFailed(
                    error.config?.url || 'unknown',
                    error.message,
                    error.response?.status,
                    undefined, // userId - would need to be passed in context
                    undefined, // email - would need to be passed in context
                    undefined, // ipAddress - would need to be passed in context
                    undefined, // userAgent - would need to be passed in context
                    this.config.productId,
                    undefined,
                    {
                        code: error.code,
                        message: error.message,
                        responseData: error.response?.data,
                    }
                );

                this.logError('Freemius API Request Failed', {
                    error,
                    url: error.config?.url,
                    method: error.config?.method,
                    status: error.response?.status,
                    message: error.message,
                    data: error.response?.data,
                });
                return Promise.reject(error);
            }
        );

        this.logInfo('FreemiusService initialized', {
            apiUrl: this.config.apiUrl,
            productId: this.config.productId,
            timeout: this.config.timeout,
        });
    }  /**

   * Retrieve product information from Freemius API
   * Implements: GET /products/{productId}.json
   */
    async getProduct(fields?: string[]): Promise<FreemiusProduct | null> {
        const context = `getProduct(${this.config.productId})`;

        try {
            this.logInfo(`Fetching product info for product ID: ${this.config.productId}`, {
                fields: fields?.join(','),
                productId: this.config.productId
            });

            const params = new URLSearchParams();
            if (fields && fields.length > 0) {
                params.append('fields', fields.join(','));
            }

            const response = await this.retryApiCall(async () => {
                const url = `/products/${this.config.productId}.json${params.toString() ? `?${params.toString()}` : ''}`;
                return await this.client.get(url);
            }, context);

            if (!response.data) {
                throw new Error('Empty response from Freemius API');
            }

            const productData = response.data;

            // Validate required product data
            if (!productData.id || !productData.slug) {
                throw new Error('Invalid product data: missing required fields (id, slug)');
            }

            // Store/update product in database
            const product = await this.upsertProduct(productData);

            this.logInfo(`Product info retrieved successfully: ${productData.name || productData.title || productData.slug}`, {
                productId: productData.id,
                productName: productData.name || productData.title,
                productSlug: productData.slug
            });

            return product as FreemiusProduct;
        } catch (error) {
            const enhancedError = this.enhanceError(error, context);
            this.logError('Get Product Failed', {
                productId: this.config.productId,
                fields: fields?.join(','),
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError)
            });

            // Log to Sentry with additional context
            Logger.error('FreemiusService.getProduct failed', enhancedError, {
                productId: this.config.productId,
                fields: fields?.join(','),
                context
            }, {
                service: 'freemius',
                operation: 'getProduct'
            });

            return null;
        }
    }

    /**
     * Retrieve specific installation information
     * Implements: GET /products/{productId}/installs/{installId}.json
     */
    async getInstallation(installId: string, fields?: string[]): Promise<FreemiusInstallation | null> {
        const context = `getInstallation(${installId})`;

        try {
            // Validate input parameters
            if (!installId || typeof installId !== 'string') {
                throw new Error('Invalid installId: must be a non-empty string');
            }

            this.logInfo(`Fetching installation info for install ID: ${installId}`, {
                installId,
                fields: fields?.join(','),
                productId: this.config.productId
            });

            const params = new URLSearchParams();
            if (fields && fields.length > 0) {
                params.append('fields', fields.join(','));
            }

            const response = await this.retryApiCall(async () => {
                const url = `/products/${this.config.productId}/installs/${installId}.json${params.toString() ? `?${params.toString()}` : ''}`;
                return await this.client.get(url);
            }, context);

            if (!response.data) {
                throw new Error('Empty response from Freemius API');
            }

            const installationData = response.data;

            // Validate required installation data
            if (!installationData.id || !installationData.plugin_id) {
                throw new Error('Invalid installation data: missing required fields (id, plugin_id)');
            }

            // Store/update installation in database
            const installation = await this.upsertInstallation(installationData);

            this.logInfo(`Installation info retrieved successfully: ${installId}`, {
                installId: installationData.id,
                pluginId: installationData.plugin_id,
                isActive: installationData.is_active,
                isPremium: installationData.is_premium
            });

            return installation as unknown as FreemiusInstallation;
        } catch (error) {
            const enhancedError = this.enhanceError(error, context);
            this.logError('Get Installation Failed', {
                installId,
                productId: this.config.productId,
                fields: fields?.join(','),
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError)
            });

            // Log to Sentry with additional context
            Logger.error('FreemiusService.getInstallation failed', enhancedError, {
                installId,
                productId: this.config.productId,
                fields: fields?.join(','),
                context
            }, {
                service: 'freemius',
                operation: 'getInstallation'
            });

            return null;
        }
    }

    /**
     * Retrieve all installations for the product with advanced filtering
     * Implements: GET /products/{productId}/installs.json
     */
    async getAllInstallations(options: InstallationFilters = {}): Promise<SyncResult> {
        const startTime = new Date();
        let installationsUpdated = 0;
        const errors: string[] = [];

        try {
            const {
                filter = 'all',
                fields,
                limit,
                offset,
                sort,
                order
            } = options;

            this.logInfo(`Fetching all installations with filter: ${filter}`, options);

            const params = new URLSearchParams();
            params.append('filter', filter);

            if (fields && fields.length > 0) {
                params.append('fields', fields.join(','));
            }
            if (limit) {
                params.append('limit', limit.toString());
            }
            if (offset) {
                params.append('offset', offset.toString());
            }
            if (sort) {
                params.append('sort', sort);
            }
            if (order) {
                params.append('order', order);
            }

            const response = await this.retryApiCall(async () => {
                const url = `/products/${this.config.productId}/installs.json?${params.toString()}`;
                return await this.client.get(url);
            }, `getAllInstallations(filter=${filter})`);

            const installations = Array.isArray(response.data) ? response.data : (response.data?.installs || []);

            // Process each installation
            for (const installationData of installations) {
                try {
                    await this.upsertInstallation(installationData);
                    installationsUpdated++;
                } catch (error) {
                    const errorMsg = `Failed to update installation ${installationData.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                    errors.push(errorMsg);
                    this.logError('Installation Update Failed', { installationId: installationData.id, error: errorMsg });
                }
            }

            this.logInfo(`Installation sync completed: ${installationsUpdated} installations updated, ${errors.length} errors`);

            return {
                success: errors.length === 0,
                summary: {
                    productsUpdated: 0,
                    installationsUpdated,
                    eventsProcessed: 0,
                },
                errors,
                lastSyncTime: startTime.toISOString(),
            };
        } catch (error) {
            const errorMsg = `Failed to sync installations: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Installation Sync Failed', { error: errorMsg });

            return {
                success: false,
                summary: {
                    productsUpdated: 0,
                    installationsUpdated,
                    eventsProcessed: 0,
                },
                errors: [errorMsg],
                lastSyncTime: startTime.toISOString(),
            };
        }
    }

    /**
     * Sync both product and installations data
     */
    async syncAllData(): Promise<SyncResult> {
        const startTime = new Date();
        const context = 'syncAllData';
        let productsUpdated = 0;
        let installationsUpdated = 0;
        const errors: string[] = [];

        try {
            this.logInfo('Starting full data synchronization', {
                productId: this.config.productId,
                startTime: startTime.toISOString()
            });

            // Log sync start
            await logFreemiusSyncStarted('all');

            // Sync product data first with enhanced error handling
            try {
                const product = await this.getProduct();
                if (product) {
                    productsUpdated = 1;
                    this.logInfo('Product sync completed successfully', {
                        productId: product.id,
                        productName: product.title
                    });
                } else {
                    const errorMsg = 'Failed to sync product data: API returned null';
                    errors.push(errorMsg);
                    this.logError('Product sync failed', { error: errorMsg });
                }
            } catch (productError) {
                const errorMsg = `Failed to sync product data: ${productError instanceof Error ? productError.message : 'Unknown error'}`;
                errors.push(errorMsg);
                this.logError('Product sync failed with exception', {
                    error: errorMsg,
                    productId: this.config.productId
                });
            }

            // Sync all installations with enhanced error handling
            try {
                const installationResult = await this.getAllInstallations({ filter: 'all' });
                installationsUpdated = installationResult.summary.installationsUpdated;
                errors.push(...installationResult.errors);

                this.logInfo('Installation sync completed', {
                    installationsUpdated,
                    installationErrors: installationResult.errors.length
                });
            } catch (installationError) {
                const errorMsg = `Failed to sync installations: ${installationError instanceof Error ? installationError.message : 'Unknown error'}`;
                errors.push(errorMsg);
                this.logError('Installation sync failed with exception', {
                    error: errorMsg,
                    productId: this.config.productId
                });
            }

            const syncResults = {
                productsUpdated,
                installationsUpdated,
                eventsProcessed: 0,
            };

            const syncDuration = Date.now() - startTime.getTime();
            const isSuccess = errors.length === 0;

            if (isSuccess) {
                // Log successful sync completion
                await logFreemiusSyncCompleted('all', syncResults);
                this.logInfo(`Full sync completed successfully`, {
                    ...syncResults,
                    durationMs: syncDuration,
                    startTime: startTime.toISOString()
                });
            } else {
                // Log partial sync with errors
                const errorSummary = `Sync completed with ${errors.length} errors: ${errors.slice(0, 3).join(', ')}${errors.length > 3 ? '...' : ''}`;
                await logFreemiusSyncFailed('all', errorSummary);
                this.logError('Full sync completed with errors', {
                    ...syncResults,
                    errorCount: errors.length,
                    errors: errors.slice(0, 5), // Log first 5 errors
                    durationMs: syncDuration
                });
            }

            return {
                success: isSuccess,
                summary: syncResults,
                errors,
                lastSyncTime: startTime.toISOString(),
            };
        } catch (error) {
            const enhancedError = this.enhanceError(error, context);
            const syncDuration = Date.now() - startTime.getTime();

            this.logError('Full Sync Failed', {
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError),
                productsUpdated,
                installationsUpdated,
                durationMs: syncDuration
            });

            // Log sync failure
            await logFreemiusSyncFailed('all', enhancedError.message, undefined, undefined, undefined, undefined, {
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError),
                productsUpdated,
                installationsUpdated,
                durationMs: syncDuration
            });

            // Log to Sentry with full context
            Logger.error('FreemiusService.syncAllData failed', enhancedError, {
                productId: this.config.productId,
                productsUpdated,
                installationsUpdated,
                durationMs: syncDuration,
                context
            }, {
                service: 'freemius',
                operation: 'syncAllData'
            });

            return {
                success: false,
                summary: {
                    productsUpdated,
                    installationsUpdated,
                    eventsProcessed: 0,
                },
                errors: [enhancedError.message],
                lastSyncTime: startTime.toISOString(),
            };
        }
    }

    /**
     * Validate installation against Freemius API
     */
    async validateInstallation(installId: string): Promise<ValidationResult> {
        try {
            this.logInfo(`Validating installation: ${installId}`);

            // Get installation from database
            const localInstallation = await prisma.freemiusInstallation.findUnique({
                where: { id: installId },
            });

            if (!localInstallation) {
                return {
                    isValid: false,
                    errors: ['Installation not found in local database'],
                };
            }

            // Fetch from Freemius API
            const apiInstallation = await this.getInstallation(installId);

            if (!apiInstallation) {
                return {
                    isValid: false,
                    errors: ['Installation not found in Freemius API'],
                };
            }

            // Compare key fields
            const errors: string[] = [];
            if (localInstallation.is_active !== apiInstallation.is_active) {
                errors.push('Active status mismatch');
            }
            if (localInstallation.version !== apiInstallation.version) {
                errors.push('Version mismatch');
            }
            if (localInstallation.is_premium !== apiInstallation.is_premium) {
                errors.push('Premium status mismatch');
            }
            if (localInstallation.is_uninstalled !== apiInstallation.is_uninstalled) {
                errors.push('Uninstalled status mismatch');
            }

            const isValid = errors.length === 0;

            this.logInfo(`Installation validation ${isValid ? 'passed' : 'failed'}: ${installId}`);

            return {
                isValid,
                errors,
                installation: localInstallation as FreemiusInstallation,
            };
        } catch (error) {
            const errorMsg = `Failed to validate installation ${installId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Installation Validation Failed', { installId, error: errorMsg });

            return {
                isValid: false,
                errors: [errorMsg],
            };
        }
    }

    /**
     * Validate webhook signature according to Freemius documentation
     */
    validateWebhookSignature(payload: string, signature: string): WebhookValidationResult {
        try {
            if (!this.config.productSecretKey) {
                return {
                    isValid: false,
                    error: 'Product secret key not configured',
                };
            }

            if (!signature) {
                return {
                    isValid: false,
                    error: 'No signature provided',
                };
            }

            // Calculate expected signature using HMAC SHA256
            const expectedHash = crypto
                .createHmac('sha256', this.config.productSecretKey)
                .update(payload)
                .digest('hex');

            // Use crypto.timingSafeEqual for secure comparison
            const isValid = crypto.timingSafeEqual(
                Buffer.from(expectedHash, 'hex'),
                Buffer.from(signature, 'hex')
            );

            return {
                isValid,
                error: isValid ? undefined : 'Invalid signature',
            };
        } catch (error) {
            return {
                isValid: false,
                error: `Signature validation error: ${error instanceof Error ? error.message : 'Unknown error'}`,
            };
        }
    }

    /**
     * Process a webhook event from Freemius with enhanced immediate processing
     */
    async processWebhookEvent(event: FreemiusWebhookEvent): Promise<ProcessingResult> {
        const startTime = Date.now();

        try {
            this.logInfo(`Processing webhook event: ${event.type} (${event.id})`, {
                eventType: event.type,
                eventId: event.id,
                installId: event.install_id,
                userId: event.user_id,
                pluginId: event.plugin_id
            });

            // Enhanced validation of webhook event data
            if (!event.id || !event.type || !event.created) {
                throw new Error('Invalid webhook event: missing required fields (id, type, created)');
            }

            // Log webhook received
            await logFreemiusWebhookReceived(event.id, event.type, 'webhook');

            // Check if event already exists (idempotency)
            const existingEvent = await prisma.freemiusEvent.findUnique({
                where: { id: event.id },
            });

            if (existingEvent) {
                this.logInfo(`Event already processed: ${event.id}`, {
                    existingStatus: existingEvent.processing_status,
                    processedAt: existingEvent.processed_at
                });
                return {
                    success: true,
                    eventId: event.id,
                    processed: false,
                };
            }

            // Enhanced event data validation and sanitization
            const sanitizedEventData = {
                id: this.validateRequiredString(event.id, 'event.id'),
                created: this.validateDate(event.created) || new Date(),
                updated: this.validateDate(event.updated),
                type: this.validateRequiredString(event.type, 'event.type'),
                developer_id: this.validateRequiredString(event.developer_id, 'event.developer_id'),
                plugin_id: this.validateString(event.plugin_id),
                user_id: this.validateString(event.user_id),
                install_id: this.validateString(event.install_id),
                data: event.data || null,
                event_trigger: this.validateEnum(event.event_trigger, ['system', 'developer', 'plugin', 'user', 'install'], 'system') || 'system',
                process_time: this.validateDate(event.process_time),
                objects: event.objects as any || undefined,
                received_at: new Date(),
                processing_status: 'processing' as const,
                processed_at: null,
            };

            // Store event in database with processing status
            await prisma.freemiusEvent.create({
                data: sanitizedEventData,
            });

            try {
                // Immediate processing of specific event types
                await this.handleSpecificEventTypeEnhanced(event);

                // Update event status to processed
                await prisma.freemiusEvent.update({
                    where: { id: event.id },
                    data: {
                        processing_status: 'processed',
                        processed_at: new Date(),
                    },
                });

                // Log successful webhook processing
                await logFreemiusWebhookProcessed(event.id, event.type, 'webhook');

                const processingTime = Date.now() - startTime;
                this.logInfo(`Webhook event processed successfully: ${event.id}`, {
                    processingTimeMs: processingTime,
                    eventType: event.type
                });

                return {
                    success: true,
                    eventId: event.id,
                    processed: true,
                };

            } catch (processingError) {
                // Update event status to error
                await prisma.freemiusEvent.update({
                    where: { id: event.id },
                    data: {
                        processing_status: 'error',
                        processed_at: new Date(),
                    },
                });

                throw processingError;
            }

        } catch (error) {
            const errorMsg = `Failed to process webhook event ${event.id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            const processingTime = Date.now() - startTime;

            this.logError('Webhook Processing Failed', {
                eventId: event.id,
                eventType: event.type,
                error: errorMsg,
                processingTimeMs: processingTime,
                eventData: event
            });

            // Log webhook processing failure
            await logFreemiusWebhookFailed(event.id, event.type || 'unknown', errorMsg, 'webhook', undefined, {
                error: errorMsg,
                eventData: event,
                processingTimeMs: processingTime
            });

            return {
                success: false,
                eventId: event.id,
                processed: false,
                error: errorMsg,
            };
        }
    }  /**
 
  * Get events with optional filtering
   */
    async getEvents(filters: {
        type?: string;
        plugin_id?: string;
        install_id?: string;
        date_from?: Date;
        date_to?: Date;
        limit?: number;
        offset?: number;
    } = {}): Promise<FreemiusEvent[]> {
        try {
            const where: any = {};

            if (filters.type) where.type = filters.type;
            if (filters.plugin_id) where.plugin_id = filters.plugin_id;
            if (filters.install_id) where.install_id = filters.install_id;
            if (filters.date_from || filters.date_to) {
                where.created = {};
                if (filters.date_from) where.created.gte = filters.date_from;
                if (filters.date_to) where.created.lte = filters.date_to;
            }

            const events = await prisma.freemiusEvent.findMany({
                where,
                orderBy: { created: 'desc' },
                take: filters.limit || 100,
                skip: filters.offset || 0,
            });

            return events as unknown as FreemiusEvent[];
        } catch (error) {
            this.logError('Get Events Failed', { filters, error: error instanceof Error ? error.message : 'Unknown error' });
            return [];
        }
    }

    /**
     * Get comprehensive statistics from local database
     */
    async getStats(): Promise<FreemiusStats> {
        try {
            this.logInfo('Generating Freemius statistics');

            // Get basic counts
            const totalProducts = await prisma.freemiusProduct.count();
            const totalInstallations = await prisma.freemiusInstallation.count();
            const activeInstallations = await prisma.freemiusInstallation.count({
                where: { is_active: true, is_uninstalled: false },
            });
            const premiumInstallations = await prisma.freemiusInstallation.count({
                where: { is_premium: true, is_active: true, is_uninstalled: false },
            });

            // Calculate revenue metrics
            const installations = await prisma.freemiusInstallation.findMany({
                where: { is_active: true, is_uninstalled: false },
                select: { gross: true, created: true, country_code: true },
            });

            const totalRevenue = installations.reduce((sum: number, install: any) => sum + (install.gross || 0), 0);

            // Calculate monthly revenue (last 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            const monthlyInstallations = installations.filter(
                (install: any) => new Date(install.created) >= thirtyDaysAgo
            );
            const monthlyRevenue = monthlyInstallations.reduce((sum: number, install: any) => sum + (install.gross || 0), 0);

            // Calculate conversion rate (premium / total active)
            const conversionRate = activeInstallations > 0 ? (premiumInstallations / activeInstallations) * 100 : 0;

            // Calculate ARPU (Average Revenue Per User)
            const averageRevenuePerUser = premiumInstallations > 0 ? totalRevenue / premiumInstallations : 0;

            // Get top countries
            const countryStats: { [key: string]: { count: number; revenue: number } } = {};
            installations.forEach((install: any) => {
                const country = install.country_code || 'Unknown';
                if (!countryStats[country]) {
                    countryStats[country] = { count: 0, revenue: 0 };
                }
                countryStats[country].count++;
                countryStats[country].revenue += install.gross || 0;
            });

            const topCountries = Object.entries(countryStats)
                .map(([country, stats]) => ({ country, ...stats }))
                .sort((a, b) => b.count - a.count)
                .slice(0, 10);

            // Get recent events
            const recentEvents = await this.getEvents({ limit: 20 });

            // Calculate installation trends (last 30 days)
            const installationTrends: Array<{
                date: string;
                installs: number;
                uninstalls: number;
                activations: number;
            }> = [];

            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                const dayStart = new Date(date);
                dayStart.setHours(0, 0, 0, 0);
                const dayEnd = new Date(date);
                dayEnd.setHours(23, 59, 59, 999);

                const dayEvents = await prisma.freemiusEvent.findMany({
                    where: {
                        created: {
                            gte: dayStart,
                            lte: dayEnd,
                        },
                    },
                    select: { type: true },
                });

                const installs = dayEvents.filter((e: any) => e.type === 'install.installed').length;
                const uninstalls = dayEvents.filter((e: any) => e.type === 'install.uninstalled').length;
                const activations = dayEvents.filter((e: any) => e.type === 'install.activated').length;

                installationTrends.push({
                    date: dateStr,
                    installs,
                    uninstalls,
                    activations,
                });
            }

            // Calculate churn rate (simplified - uninstalls vs total in last 30 days)
            const totalUninstalls = installationTrends.reduce((sum, day) => sum + day.uninstalls, 0);
            const churnRate = activeInstallations > 0 ? (totalUninstalls / activeInstallations) * 100 : 0;

            const stats: FreemiusStats = {
                totalProducts,
                totalInstallations,
                activeInstallations,
                premiumInstallations,
                totalRevenue,
                monthlyRevenue,
                conversionRate,
                churnRate,
                averageRevenuePerUser,
                topCountries,
                recentEvents,
                installationTrends,
            };

            this.logInfo('Statistics generated successfully', {
                totalInstallations,
                activeInstallations,
                premiumInstallations,
                totalRevenue,
            });

            return stats;
        } catch (error) {
            this.logError('Failed to generate statistics', { error: error instanceof Error ? error.message : 'Unknown error' });

            // Return empty stats on error
            return {
                totalProducts: 0,
                totalInstallations: 0,
                activeInstallations: 0,
                premiumInstallations: 0,
                totalRevenue: 0,
                monthlyRevenue: 0,
                conversionRate: 0,
                churnRate: 0,
                averageRevenuePerUser: 0,
                topCountries: [],
                recentEvents: [],
                installationTrends: [],
            };
        }
    }

    /**
     * Get installation by ID from database
     */
    async getStoredInstallation(installId: string): Promise<FreemiusInstallation | null> {
        try {
            const installation = await prisma.freemiusInstallation.findUnique({
                where: { id: installId },
                include: {
                    product: true,
                },
            });

            return installation as FreemiusInstallation | null;
        } catch (error) {
            this.logError('Get Stored Installation Failed', { installId, error: error instanceof Error ? error.message : 'Unknown error' });
            return null;
        }
    }

    /**
     * Get product by ID from database
     */
    async getStoredProduct(): Promise<FreemiusProduct | null> {
        try {
            const product = await prisma.freemiusProduct.findUnique({
                where: { id: this.config.productId },
            });

            return product as FreemiusProduct | null;
        } catch (error) {
            this.logError('Get Stored Product Failed', { error: error instanceof Error ? error.message : 'Unknown error' });
            return null;
        }
    }

    /**
     * Refresh specific installation from API
     */
    async refreshInstallation(installId: string): Promise<FreemiusInstallation | null> {
        try {
            this.logInfo(`Refreshing installation from API: ${installId}`);

            // Force fetch from API (not using cached data)
            const installation = await this.getInstallation(installId);

            if (installation) {
                this.logInfo(`Installation refreshed successfully: ${installId}`);
            } else {
                this.logError('Failed to refresh installation', { installId });
            }

            return installation;
        } catch (error) {
            const errorMsg = `Failed to refresh installation ${installId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Installation Refresh Failed', { installId, error: errorMsg });
            return null;
        }
    }

    /**
     * Refresh product from API
     */
    async refreshProduct(): Promise<FreemiusProduct | null> {
        try {
            this.logInfo('Refreshing product from API');

            // Force fetch from API (not using cached data)
            const product = await this.getProduct();

            if (product) {
                this.logInfo('Product refreshed successfully');
            } else {
                this.logError('Failed to refresh product');
            }

            return product;
        } catch (error) {
            const errorMsg = `Failed to refresh product: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Product Refresh Failed', { error: errorMsg });
            return null;
        }
    }

    /**
     * Clean up old events from database
     */
    async cleanupOldEvents(daysToKeep: number = 90): Promise<number> {
        try {
            this.logInfo(`Cleaning up events older than ${daysToKeep} days`);

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

            const result = await prisma.freemiusEvent.deleteMany({
                where: {
                    created: {
                        lt: cutoffDate,
                    },
                },
            });

            this.logInfo(`Cleanup completed: ${result.count} old events removed`);
            return result.count;
        } catch (error) {
            const errorMsg = `Failed to cleanup old events: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Event Cleanup Failed', { error: errorMsg });
            return 0;
        }
    }

    /**
     * Private helper methods
     */

    private async upsertProduct(apiProduct: FreemiusApiProduct): Promise<any> {
        try {
            // Enhanced null validation and type conversion
            const productData = {
                secret_key: this.validateString(apiProduct.secret_key),
                public_key: this.validateString(apiProduct.public_key),
                updated: this.validateDate(apiProduct.updated),
                parent_plugin_id: this.validateString(apiProduct.parent_plugin_id),
                developer_id: this.validateRequiredString(apiProduct.developer_id, 'developer_id'),
                store_id: this.validateRequiredString(apiProduct.store_id, 'store_id'),
                slug: this.validateRequiredString(apiProduct.slug, 'slug'),
                title: this.validateRequiredString(
                    apiProduct.name || apiProduct.title || apiProduct.slug,
                    'title'
                ),
                environment: this.validateInteger(apiProduct.environment, 0),
                icon: this.validateString(apiProduct.icon),
                default_plan_id: this.validateString(apiProduct.default_plan_id),
                plans: this.validateString(apiProduct.plans),
                features: this.validateString(apiProduct.features),
                money_back_period: this.validateInteger(apiProduct.money_back_period, 0),
                refund_policy: this.validateEnum(apiProduct.refund_policy, ['flexible', 'moderate', 'strict']),
                annual_renewals_discount: this.validateInteger(apiProduct.annual_renewals_discount),
                renewals_discount_type: this.validateEnum(apiProduct.renewals_discount_type, ['percentage', 'dollar']),
                is_released: this.validateBoolean(apiProduct.is_released, true),
                is_sdk_required: this.validateBoolean(apiProduct.is_sdk_required, true),
                is_pricing_visible: this.validateBoolean(apiProduct.is_pricing_visible, true),
                is_wp_org_compliant: this.validateBoolean(apiProduct.is_wp_org_compliant, true),
                installs_count: this.validateInteger(apiProduct.installs_count, 0),
                active_installs_count: this.validateInteger(apiProduct.active_installs_count, 0),
                free_releases_count: this.validateInteger(apiProduct.free_releases_count, 0),
                premium_releases_count: this.validateInteger(apiProduct.premium_releases_count, 0),
                total_purchases: this.validateInteger(apiProduct.total_purchases, 0),
                total_subscriptions: this.validateInteger(apiProduct.total_subscriptions, 0),
                total_renewals: this.validateInteger(apiProduct.total_renewals, 0),
                total_failed_purchases: this.validateInteger(apiProduct.total_failed_purchases, 0),
                earnings: this.validateFloat(apiProduct.earnings, 0),
                type: this.validateEnum(apiProduct.type, ['plugin', 'theme', 'widget', 'template'], 'plugin') || 'plugin',
                is_static: this.validateBoolean(apiProduct.is_static, false),
                api_last_synced_at: new Date(),
                api_sync_errors: undefined,
                last_synced_at: new Date(),
                sync_status: 'active',
            };

            return await prisma.freemiusProduct.upsert({
                where: { id: apiProduct.id },
                update: productData,
                create: {
                    id: this.validateRequiredString(apiProduct.id, 'id'),
                    created: this.validateDate(apiProduct.created) || new Date(),
                    ...productData,
                },
            });
        } catch (error) {
            this.logError('Failed to upsert product', {
                productId: apiProduct.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                apiProduct
            });
            throw error;
        }
    }

    private async upsertInstallation(apiInstallation: FreemiusApiInstallation): Promise<any> {
        try {
            // Enhanced null validation and type conversion
            const installationData = {
                secret_key: this.validateString(apiInstallation.secret_key),
                public_key: this.validateString(apiInstallation.public_key),
                updated: this.validateDate(apiInstallation.updated),
                site_id: this.validateRequiredString(apiInstallation.site_id, 'site_id'),
                user_id: this.validateRequiredString(apiInstallation.user_id, 'user_id'),
                url: this.validateUrl(apiInstallation.url),
                title: this.validateString(apiInstallation.title || apiInstallation.name),
                version: this.validateRequiredString(apiInstallation.version, 'version'),
                plan_id: this.validateString(apiInstallation.plan_id),
                license_id: this.validateString(apiInstallation.license_id),
                trial_plan_id: this.validateString(apiInstallation.trial_plan_id),
                trial_ends: this.validateDate(apiInstallation.trial_ends),
                subscription_id: this.validateString(apiInstallation.subscription_id),
                gross: this.validateFloat(apiInstallation.gross, 0),
                country_code: this.validateCountryCode(apiInstallation.country_code),
                language: this.validateString(apiInstallation.language),
                platform_version: this.validateString(apiInstallation.platform_version),
                sdk_version: this.validateString(apiInstallation.sdk_version),
                programming_language_version: this.validateString(apiInstallation.programming_language_version),
                is_active: this.validateBoolean(apiInstallation.is_active, true),
                is_disconnected: this.validateBoolean(apiInstallation.is_disconnected, false),
                is_premium: this.validateBoolean(apiInstallation.is_premium, false),
                is_uninstalled: this.validateBoolean(apiInstallation.is_uninstalled, false),
                is_locked: this.validateBoolean(apiInstallation.is_locked, false),
                source: this.validateInteger(apiInstallation.source, 0),
                upgraded: this.validateDate(apiInstallation.upgraded),
                last_seen_at: this.validateDate(apiInstallation.last_seen_at),
                last_served_update_version: this.validateString(apiInstallation.last_served_update_version),
                is_beta: this.validateBoolean(apiInstallation.is_beta, false),
                validation_status: 'valid',
                last_validated_at: new Date(),
                validation_errors: undefined,
                api_last_synced_at: new Date(),
                api_sync_errors: undefined,
                last_synced_at: new Date(),
                sync_status: 'active',
            };

            return await prisma.freemiusInstallation.upsert({
                where: { id: apiInstallation.id },
                update: installationData,
                create: {
                    id: this.validateRequiredString(apiInstallation.id, 'id'),
                    created: this.validateDate(apiInstallation.created) || new Date(),
                    plugin_id: this.validateRequiredString(apiInstallation.plugin_id || this.config.productId, 'plugin_id'),
                    ...installationData,
                },
            });
        } catch (error) {
            this.logError('Failed to upsert installation', {
                installationId: apiInstallation.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                apiInstallation
            });
            throw error;
        }
    }
    /**
     * Enhanced event handling that covers all Freemius webhook event types
     */
    private async handleSpecificEventTypeEnhanced(event: FreemiusWebhookEvent): Promise<void> {
        const eventCategory = this.getEventCategory(event.type);

        this.logInfo(`Processing ${eventCategory} event: ${event.type}`, {
            eventId: event.id,
            installId: event.install_id,
            userId: event.user_id,
            pluginId: event.plugin_id
        });

        try {
            switch (eventCategory) {
                case 'installation':
                    await this.handleInstallationEvents(event);
                    break;
                case 'license':
                    await this.handleLicenseEvents(event);
                    break;
                case 'payment':
                    await this.handlePaymentEvents(event);
                    break;
                case 'subscription':
                    await this.handleSubscriptionEvents(event);
                    break;
                case 'user':
                    await this.handleUserEvents(event);
                    break;
                case 'plan':
                    await this.handlePlanEvents(event);
                    break;
                case 'cart':
                    await this.handleCartEvents(event);
                    break;
                case 'affiliate':
                    await this.handleAffiliateEvents(event);
                    break;
                case 'review':
                    await this.handleReviewEvents(event);
                    break;
                case 'webhook':
                    await this.handleWebhookEvents(event);
                    break;
                case 'wordpress':
                    await this.handleWordPressEvents(event);
                    break;
                default:
                    await this.handleGenericEvent(event);
                    break;
            }
        } catch (error) {
            this.logError(`Failed to handle ${eventCategory} event: ${event.type}`, {
                eventId: event.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                eventData: event
            });
            throw error;
        }
    }

    /**
     * Categorize event types for better handling
     */
    private getEventCategory(eventType: string): string {
        if (eventType.startsWith('install.')) return 'installation';
        if (eventType.startsWith('license.')) return 'license';
        if (eventType.startsWith('payment.')) return 'payment';
        if (eventType.startsWith('subscription.')) return 'subscription';
        if (eventType.startsWith('user.')) return 'user';
        if (eventType.startsWith('plan.') || eventType.startsWith('pricing.')) return 'plan';
        if (eventType.startsWith('cart.')) return 'cart';
        if (eventType.startsWith('affiliate.')) return 'affiliate';
        if (eventType.startsWith('review.')) return 'review';
        if (eventType.startsWith('webhook.')) return 'webhook';
        if (eventType.startsWith('addon.') || eventType.startsWith('plugin.')) return 'wordpress';
        return 'other';
    }

    /**
     * Handle installation-related events
     */
    private async handleInstallationEvents(event: FreemiusWebhookEvent): Promise<void> {
        if (!event.install_id) {
            this.logInfo(`Installation event without install_id: ${event.type}`, { eventId: event.id });
            return;
        }

        const installationEvents = [
            'install.activated', 'install.deactivated', 'install.updated', 'install.installed',
            'install.uninstalled', 'install.connected', 'install.disconnected', 'install.deleted',
            'install.premium.activated', 'install.premium.deactivated', 'install.plan.changed',
            'install.plan.downgraded', 'install.version.upgraded', 'install.version.downgrade',
            'install.trial.started', 'install.trial.cancelled', 'install.trial.expired',
            'install.trial.extended', 'install.language.updated', 'install.platform.version.updated',
            'install.programming_language.version.updated', 'install.sdk.version.updated',
            'install.title.updated', 'install.url.updated', 'install.trial.plan.updated'
        ];

        if (installationEvents.includes(event.type)) {
            try {
                await this.getInstallation(event.install_id);
                this.logInfo(`Installation refreshed after ${event.type} event`, {
                    eventId: event.id,
                    installId: event.install_id
                });
            } catch (error) {
                this.logError('Failed to refresh installation after event', {
                    eventId: event.id,
                    installId: event.install_id,
                    eventType: event.type,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    }

    /**
     * Handle license-related events
     */
    private async handleLicenseEvents(event: FreemiusWebhookEvent): Promise<void> {
        const licenseEvents = [
            'license.activated', 'license.deactivated', 'license.expired', 'license.created',
            'license.cancelled', 'license.deleted', 'license.extended', 'license.shortened',
            'license.updated', 'license.ownership.changed', 'license.quota.changed'
        ];

        if (licenseEvents.includes(event.type) && event.install_id) {
            try {
                await this.getInstallation(event.install_id);
                this.logInfo(`Installation refreshed after ${event.type} event`, {
                    eventId: event.id,
                    installId: event.install_id
                });
            } catch (error) {
                this.logError('Failed to refresh installation after license event', {
                    eventId: event.id,
                    installId: event.install_id,
                    eventType: event.type,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    }

    /**
     * Handle payment-related events
     */
    private async handlePaymentEvents(event: FreemiusWebhookEvent): Promise<void> {
        const paymentEvents = [
            'payment.created', 'payment.refund', 'payment.dispute.created',
            'payment.dispute.closed', 'payment.dispute.lost', 'payment.dispute.won'
        ];

        if (paymentEvents.includes(event.type) && event.install_id) {
            try {
                await this.getInstallation(event.install_id);
                this.logInfo(`Installation refreshed after ${event.type} event`, {
                    eventId: event.id,
                    installId: event.install_id
                });
            } catch (error) {
                this.logError('Failed to refresh installation after payment event', {
                    eventId: event.id,
                    installId: event.install_id,
                    eventType: event.type,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    }

    /**
     * Handle subscription-related events
     */
    private async handleSubscriptionEvents(event: FreemiusWebhookEvent): Promise<void> {
        const subscriptionEvents = [
            'subscription.created', 'subscription.cancelled', 'subscription.renewal.failed',
            'subscription.renewal.failed.last', 'subscription.renewal.retry', 'subscription.renewals.discounted'
        ];

        if (subscriptionEvents.includes(event.type) && event.install_id) {
            try {
                await this.getInstallation(event.install_id);
                this.logInfo(`Installation refreshed after ${event.type} event`, {
                    eventId: event.id,
                    installId: event.install_id
                });
            } catch (error) {
                this.logError('Failed to refresh installation after subscription event', {
                    eventId: event.id,
                    installId: event.install_id,
                    eventType: event.type,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    }

    /**
     * Handle user-related events
     */
    private async handleUserEvents(event: FreemiusWebhookEvent): Promise<void> {
        const userEvents = [
            'user.created', 'user.email.changed', 'user.billing.updated', 'user.name.changed',
            'user.trial.started', 'user.beta_program.opted_in', 'user.beta_program.opted_out',
            'user.marketing.opted_in', 'user.marketing.opted_out', 'user.marketing.reset'
        ];

        if (userEvents.includes(event.type)) {
            this.logInfo(`User event processed: ${event.type}`, {
                eventId: event.id,
                userId: event.user_id
            });
        }
    }

    /**
     * Handle plan and pricing events
     */
    private async handlePlanEvents(event: FreemiusWebhookEvent): Promise<void> {
        const planEvents = [
            'plan.created', 'plan.updated', 'plan.deleted', 'plan.lifetime.purchase',
            'pricing.created', 'pricing.updated', 'pricing.deleted'
        ];

        if (planEvents.includes(event.type)) {
            try {
                await this.getProduct();
                this.logInfo(`Product refreshed after ${event.type} event`, {
                    eventId: event.id
                });
            } catch (error) {
                this.logError('Failed to refresh product after plan event', {
                    eventId: event.id,
                    eventType: event.type,
                    error: error instanceof Error ? error.message : 'Unknown error'
                });
            }
        }
    }

    /**
     * Handle cart-related events (mainly for analytics)
     */
    private async handleCartEvents(event: FreemiusWebhookEvent): Promise<void> {
        const cartEvents = [
            'cart.abandoned', 'cart.completed', 'cart.created', 'cart.recovered', 'cart.updated'
        ];

        if (cartEvents.includes(event.type)) {
            this.logInfo(`Cart event processed: ${event.type}`, {
                eventId: event.id
            });
        }
    }

    /**
     * Handle affiliate-related events
     */
    private async handleAffiliateEvents(event: FreemiusWebhookEvent): Promise<void> {
        const affiliateEvents = [
            'affiliate.approved', 'affiliate.blocked', 'affiliate.created', 'affiliate.deleted',
            'affiliate.rejected', 'affiliate.suspended', 'affiliate.updated'
        ];

        if (affiliateEvents.includes(event.type)) {
            this.logInfo(`Affiliate event processed: ${event.type}`, {
                eventId: event.id
            });
        }
    }

    /**
     * Handle review-related events
     */
    private async handleReviewEvents(event: FreemiusWebhookEvent): Promise<void> {
        const reviewEvents = [
            'review.created', 'review.deleted', 'review.requested', 'review.updated'
        ];

        if (reviewEvents.includes(event.type)) {
            this.logInfo(`Review event processed: ${event.type}`, {
                eventId: event.id
            });
        }
    }

    /**
     * Handle webhook-related events
     */
    private async handleWebhookEvents(event: FreemiusWebhookEvent): Promise<void> {
        const webhookEvents = [
            'webhook.created', 'webhook.deleted', 'webhook.updated'
        ];

        if (webhookEvents.includes(event.type)) {
            this.logInfo(`Webhook event processed: ${event.type}`, {
                eventId: event.id
            });
        }
    }

    /**
     * Handle WordPress-specific events
     */
    private async handleWordPressEvents(event: FreemiusWebhookEvent): Promise<void> {
        const wordpressEvents = [
            'plugin.free.downloaded', 'plugin.premium.downloaded', 'plugin.version.deployed',
            'plugin.version.released', 'plugin.version.updated', 'addon.free.downloaded',
            'addon.premium.downloaded'
        ];

        if (wordpressEvents.includes(event.type)) {
            this.logInfo(`WordPress event processed: ${event.type}`, {
                eventId: event.id,
                pluginId: event.plugin_id
            });
        }
    }

    /**
     * Handle generic/unknown events
     */
    private async handleGenericEvent(event: FreemiusWebhookEvent): Promise<void> {
        this.logInfo(`Generic event processed: ${event.type}`, {
            eventId: event.id,
            installId: event.install_id,
            userId: event.user_id,
            pluginId: event.plugin_id
        });
    }

    private async handleSpecificEventType(event: FreemiusWebhookEvent): Promise<void> {
        // Legacy method - redirect to enhanced version
        await this.handleSpecificEventTypeEnhanced(event);
    }

    /**
     * Enhanced retry mechanism with comprehensive error handling and exponential backoff
     */
    private async retryApiCall<T>(apiCall: () => Promise<T>, context?: string): Promise<T> {
        let lastError: Error;
        const startTime = Date.now();

        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                // Check rate limit before making the call
                const rateLimitResult = await externalApiRateLimiter.checkRateLimit('freemius');

                if (!rateLimitResult.allowed) {
                    const waitTime = rateLimitResult.retryAfterMs || rateLimitResult.backoffMs || 60000;
                    this.logInfo(`Rate limit exceeded, waiting ${waitTime}ms before retry`, {
                        context,
                        attempt,
                        waitTimeMs: waitTime
                    });
                    await new Promise(resolve => setTimeout(resolve, waitTime));
                    continue; // Don't count this as a retry attempt
                }

                const result = await apiCall();

                // Record successful request
                await externalApiRateLimiter.recordRequest('freemius');

                // Log successful retry if it wasn't the first attempt
                if (attempt > 1) {
                    const totalTime = Date.now() - startTime;
                    this.logInfo(`API call succeeded after ${attempt} attempts`, {
                        context,
                        totalAttempts: attempt,
                        totalTimeMs: totalTime
                    });
                }

                return result;
            } catch (error) {
                lastError = this.enhanceError(error, context, attempt);

                // Record failed request for rate limiting
                await externalApiRateLimiter.recordFailure('freemius', lastError);

                // Check if this is a non-retryable error
                if (this.isNonRetryableError(lastError)) {
                    this.logError('Non-retryable error encountered', {
                        context,
                        attempt,
                        error: lastError.message,
                        errorType: this.getErrorType(lastError)
                    });
                    throw lastError;
                }

                if (attempt === this.config.retryAttempts) {
                    const totalTime = Date.now() - startTime;
                    this.logError('All retry attempts exhausted', {
                        context,
                        totalAttempts: attempt,
                        totalTimeMs: totalTime,
                        error: lastError.message,
                        errorType: this.getErrorType(lastError)
                    });
                    throw lastError;
                }

                // Calculate exponential backoff delay with jitter
                const baseDelay = this.config.retryDelay * Math.pow(2, attempt - 1);
                const jitter = Math.random() * 0.1 * baseDelay; // Add up to 10% jitter
                const delay = Math.min(baseDelay + jitter, 30000); // Cap at 30 seconds

                this.logInfo(`API call failed, retrying in ${Math.round(delay)}ms`, {
                    context,
                    error: lastError.message,
                    errorType: this.getErrorType(lastError),
                    attempt,
                    maxAttempts: this.config.retryAttempts,
                    delayMs: Math.round(delay)
                });

                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        throw lastError!;
    }

    /**
     * Enhance error with additional context and proper typing
     */
    private enhanceError(error: any, context?: string, attempt?: number): Error {
        let enhancedError: Error;

        if (error instanceof Error) {
            enhancedError = error;
        } else {
            enhancedError = new Error(typeof error === 'string' ? error : 'Unknown error');
        }

        // Add context to error message if provided
        if (context) {
            enhancedError.message = `[${context}] ${enhancedError.message}`;
        }

        // Add attempt information
        if (attempt) {
            (enhancedError as any).attempt = attempt;
        }

        // Add error classification
        (enhancedError as any).errorType = this.getErrorType(enhancedError);
        (enhancedError as any).isRetryable = !this.isNonRetryableError(enhancedError);

        return enhancedError;
    }

    /**
     * Determine error type for better handling
     */
    private getErrorType(error: Error): string {
        const message = error.message.toLowerCase();

        if (message.includes('network') || message.includes('timeout') || message.includes('econnreset')) {
            return 'network';
        }
        if (message.includes('401') || message.includes('unauthorized')) {
            return 'authentication';
        }
        if (message.includes('403') || message.includes('forbidden')) {
            return 'authorization';
        }
        if (message.includes('404') || message.includes('not found')) {
            return 'not_found';
        }
        if (message.includes('429') || message.includes('rate limit')) {
            return 'rate_limit';
        }
        if (message.includes('500') || message.includes('502') || message.includes('503') || message.includes('504')) {
            return 'server_error';
        }
        if (message.includes('400') || message.includes('bad request')) {
            return 'client_error';
        }

        return 'unknown';
    }

    /**
     * Determine if an error should not be retried
     */
    private isNonRetryableError(error: Error): boolean {
        const errorType = this.getErrorType(error);
        const nonRetryableTypes = ['authentication', 'authorization', 'not_found', 'client_error'];

        return nonRetryableTypes.includes(errorType);
    }

    private logInfo(message: string, data?: any): void {
        Logger.info(`[FreemiusService] ${message}`, data, {
            service: 'freemius',
            component: 'FreemiusService'
        });
    }

    private logError(message: string, data?: any): void {
        const error = data?.error instanceof Error ? data.error : new Error(message);
        Logger.error(`[FreemiusService] ${message}`, error, data, {
            service: 'freemius',
            component: 'FreemiusService'
        });
    }

    // Enhanced validation helper methods for proper null validation and type conversion
    private validateString(value: any): string | null {
        if (value === null || value === undefined || value === '') {
            return null;
        }
        return String(value).trim() || null;
    }

    private validateRequiredString(value: any, fieldName: string): string {
        const validated = this.validateString(value);
        if (!validated) {
            throw new Error(`Required field '${fieldName}' is missing or empty`);
        }
        return validated;
    }

    private validateInteger(value: any, defaultValue?: number): number {
        if (value === null || value === undefined || value === '') {
            return defaultValue ?? 0;
        }
        const parsed = parseInt(String(value), 10);
        if (isNaN(parsed)) {
            return defaultValue ?? 0;
        }
        return parsed;
    }

    private validateFloat(value: any, defaultValue?: number): number {
        if (value === null || value === undefined || value === '') {
            return defaultValue ?? 0;
        }
        const parsed = parseFloat(String(value));
        if (isNaN(parsed)) {
            return defaultValue ?? 0;
        }
        return parsed;
    }

    private validateBoolean(value: any, defaultValue?: boolean): boolean {
        if (value === null || value === undefined) {
            return defaultValue ?? false;
        }
        if (typeof value === 'boolean') {
            return value;
        }
        if (typeof value === 'string') {
            return value.toLowerCase() === 'true' || value === '1';
        }
        if (typeof value === 'number') {
            return value !== 0;
        }
        return defaultValue ?? false;
    }

    private validateDate(value: any): Date | null {
        if (!value) {
            return null;
        }
        try {
            const date = new Date(value);
            if (isNaN(date.getTime())) {
                return null;
            }
            return date;
        } catch {
            return null;
        }
    }

    private validateEnum<T extends string>(value: any, allowedValues: T[], defaultValue?: T): T | null {
        const validated = this.validateString(value);
        if (!validated) {
            return defaultValue ?? null;
        }
        if (allowedValues.includes(validated as T)) {
            return validated as T;
        }
        return defaultValue ?? null;
    }

    private validateUrl(value: any): string | null {
        const validated = this.validateString(value);
        if (!validated) {
            return null;
        }
        try {
            new URL(validated);
            return validated;
        } catch {
            // If URL is invalid, still store it but log a warning
            this.logInfo('Invalid URL format detected', { url: validated });
            return validated;
        }
    }

    private validateCountryCode(value: any): string | null {
        const validated = this.validateString(value);
        if (!validated) {
            return null;
        }
        // Validate ISO 3166-1 alpha-2 country code (2 letters)
        if (validated.length === 2 && /^[A-Za-z]{2}$/.test(validated)) {
            return validated.toLowerCase();
        }
        // If invalid format, still store it but log a warning
        this.logInfo('Invalid country code format detected', { countryCode: validated });
        return validated.toLowerCase();
    }

    /**
     * Get service configuration (without sensitive data)
     */
    getConfig(): Partial<FreemiusConfig> {
        return {
            apiUrl: this.config.apiUrl,
            productId: this.config.productId,
            timeout: this.config.timeout,
            retryAttempts: this.config.retryAttempts,
            retryDelay: this.config.retryDelay,
            // Exclude sensitive keys
        };
    }

    /**
     * Test API connectivity
     */
    async testConnection(): Promise<{ success: boolean; error?: string; productInfo?: any }> {
        try {
            this.logInfo('Testing Freemius API connection');

            const product = await this.getProduct(['id', 'name', 'slug']);

            if (product) {
                this.logInfo('API connection test successful');
                return {
                    success: true,
                    productInfo: {
                        id: product.id,
                        title: product.title,
                        slug: product.slug,
                    },
                };
            } else {
                return {
                    success: false,
                    error: 'Failed to retrieve product information',
                };
            }
        } catch (error) {
            const errorMsg = `API connection test failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Connection Test Failed', { error: errorMsg });

            return {
                success: false,
                error: errorMsg,
            };
        }
    }

    /**
     * Manual sync trigger for product data with timestamp-based freshness
     */
    async syncProductData(forceRefresh: boolean = false): Promise<SyncResult> {
        const startTime = new Date();
        const context = 'syncProductData';

        try {
            this.logInfo('Starting product data synchronization', {
                productId: this.config.productId,
                forceRefresh,
                startTime: startTime.toISOString()
            });

            // Check if sync is needed based on timestamp freshness
            if (!forceRefresh) {
                const existingProduct = await prisma.freemiusProduct.findUnique({
                    where: { id: this.config.productId },
                    select: {
                        api_last_synced_at: true,
                        sync_status: true,
                        last_synced_at: true
                    }
                });

                if (existingProduct?.api_last_synced_at) {
                    const timeSinceLastSync = Date.now() - existingProduct.api_last_synced_at.getTime();
                    const syncIntervalMs = 60 * 60 * 1000; // 1 hour default

                    if (timeSinceLastSync < syncIntervalMs && existingProduct.sync_status === 'active') {
                        this.logInfo('Product data is fresh, skipping sync', {
                            lastSyncedAt: existingProduct.api_last_synced_at.toISOString(),
                            timeSinceLastSyncMs: timeSinceLastSync,
                            syncStatus: existingProduct.sync_status
                        });

                        return {
                            success: true,
                            summary: { productsUpdated: 0, installationsUpdated: 0, eventsProcessed: 0 },
                            errors: [],
                            lastSyncTime: existingProduct.api_last_synced_at.toISOString()
                        };
                    }
                }
            }

            // Update sync status to indicate sync in progress
            await this.updateSyncStatus('product', this.config.productId, 'syncing');

            const product = await this.getProduct();
            const syncDuration = Date.now() - startTime.getTime();

            if (product) {
                await this.updateSyncStatus('product', this.config.productId, 'active');

                this.logInfo('Product sync completed successfully', {
                    productId: product.id,
                    productName: product.title,
                    durationMs: syncDuration
                });

                return {
                    success: true,
                    summary: { productsUpdated: 1, installationsUpdated: 0, eventsProcessed: 0 },
                    errors: [],
                    lastSyncTime: startTime.toISOString()
                };
            } else {
                await this.updateSyncStatus('product', this.config.productId, 'error', ['Failed to retrieve product data']);

                return {
                    success: false,
                    summary: { productsUpdated: 0, installationsUpdated: 0, eventsProcessed: 0 },
                    errors: ['Failed to retrieve product data'],
                    lastSyncTime: startTime.toISOString()
                };
            }
        } catch (error) {
            const enhancedError = this.enhanceError(error, context);
            const syncDuration = Date.now() - startTime.getTime();

            await this.updateSyncStatus('product', this.config.productId, 'error', [enhancedError.message]);

            this.logError('Product sync failed', {
                productId: this.config.productId,
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError),
                durationMs: syncDuration
            });

            return {
                success: false,
                summary: { productsUpdated: 0, installationsUpdated: 0, eventsProcessed: 0 },
                errors: [enhancedError.message],
                lastSyncTime: startTime.toISOString()
            };
        }
    }

    /**
     * Manual sync trigger for installation data with timestamp-based freshness
     */
    async syncInstallationData(installId?: string, forceRefresh: boolean = false): Promise<SyncResult> {
        const startTime = new Date();
        const context = `syncInstallationData${installId ? `(${installId})` : ''}`;

        try {
            this.logInfo('Starting installation data synchronization', {
                installId: installId || 'all',
                forceRefresh,
                startTime: startTime.toISOString()
            });

            if (installId) {
                // Sync specific installation
                return await this.syncSingleInstallation(installId, forceRefresh);
            } else {
                // Sync all installations
                return await this.getAllInstallations({ filter: 'all' });
            }
        } catch (error) {
            const enhancedError = this.enhanceError(error, context);
            const syncDuration = Date.now() - startTime.getTime();

            this.logError('Installation sync failed', {
                installId: installId || 'all',
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError),
                durationMs: syncDuration
            });

            return {
                success: false,
                summary: { productsUpdated: 0, installationsUpdated: 0, eventsProcessed: 0 },
                errors: [enhancedError.message],
                lastSyncTime: startTime.toISOString()
            };
        }
    }

    /**
     * Sync a single installation with freshness check
     */
    private async syncSingleInstallation(installId: string, forceRefresh: boolean): Promise<SyncResult> {
        const startTime = new Date();

        try {
            // Check if sync is needed based on timestamp freshness
            if (!forceRefresh) {
                const existingInstallation = await prisma.freemiusInstallation.findUnique({
                    where: { id: installId },
                    select: {
                        api_last_synced_at: true,
                        sync_status: true,
                        last_synced_at: true
                    }
                });

                if (existingInstallation?.api_last_synced_at) {
                    const timeSinceLastSync = Date.now() - existingInstallation.api_last_synced_at.getTime();
                    const syncIntervalMs = 30 * 60 * 1000; // 30 minutes default for installations

                    if (timeSinceLastSync < syncIntervalMs && existingInstallation.sync_status === 'active') {
                        this.logInfo('Installation data is fresh, skipping sync', {
                            installId,
                            lastSyncedAt: existingInstallation.api_last_synced_at.toISOString(),
                            timeSinceLastSyncMs: timeSinceLastSync,
                            syncStatus: existingInstallation.sync_status
                        });

                        return {
                            success: true,
                            summary: { productsUpdated: 0, installationsUpdated: 0, eventsProcessed: 0 },
                            errors: [],
                            lastSyncTime: existingInstallation.api_last_synced_at.toISOString()
                        };
                    }
                }
            }

            // Update sync status to indicate sync in progress
            await this.updateSyncStatus('installation', installId, 'syncing');

            const installation = await this.getInstallation(installId);
            const syncDuration = Date.now() - startTime.getTime();

            if (installation) {
                await this.updateSyncStatus('installation', installId, 'active');

                this.logInfo('Installation sync completed successfully', {
                    installId: installation.id,
                    isActive: installation.is_active,
                    isPremium: installation.is_premium,
                    durationMs: syncDuration
                });

                return {
                    success: true,
                    summary: { productsUpdated: 0, installationsUpdated: 1, eventsProcessed: 0 },
                    errors: [],
                    lastSyncTime: startTime.toISOString()
                };
            } else {
                await this.updateSyncStatus('installation', installId, 'error', ['Failed to retrieve installation data']);

                return {
                    success: false,
                    summary: { productsUpdated: 0, installationsUpdated: 0, eventsProcessed: 0 },
                    errors: ['Failed to retrieve installation data'],
                    lastSyncTime: startTime.toISOString()
                };
            }
        } catch (error) {
            const enhancedError = this.enhanceError(error, `syncSingleInstallation(${installId})`);
            const syncDuration = Date.now() - startTime.getTime();

            await this.updateSyncStatus('installation', installId, 'error', [enhancedError.message]);

            this.logError('Single installation sync failed', {
                installId,
                error: enhancedError.message,
                errorType: this.getErrorType(enhancedError),
                durationMs: syncDuration
            });

            throw enhancedError;
        }
    }

    /**
     * Update sync status and error tracking for products and installations
     */
    private async updateSyncStatus(
        entityType: 'product' | 'installation',
        entityId: string,
        status: 'active' | 'syncing' | 'error' | 'pending',
        errors?: string[]
    ): Promise<void> {
        try {
            const updateData = {
                sync_status: status,
                last_synced_at: new Date(),
                ...(status === 'active' && {
                    api_last_synced_at: new Date(),
                    api_sync_errors: undefined
                }),
                ...(status === 'error' && errors && {
                    api_sync_errors: errors as any
                })
            };

            if (entityType === 'product') {
                await prisma.freemiusProduct.update({
                    where: { id: entityId },
                    data: updateData
                });
            } else {
                await prisma.freemiusInstallation.update({
                    where: { id: entityId },
                    data: updateData
                });
            }

            this.logInfo(`Updated sync status for ${entityType}`, {
                entityId,
                status,
                errors: errors?.length || 0
            });
        } catch (error) {
            this.logError(`Failed to update sync status for ${entityType}`, {
                entityId,
                status,
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        }
    }

    /**
     * Get sync status and statistics
     */
    async getSyncStatus(): Promise<{
        product: {
            id: string;
            status: string;
            lastSyncedAt?: string;
            apiLastSyncedAt?: string;
            errors?: string[];
        } | null;
        installations: {
            total: number;
            active: number;
            syncing: number;
            error: number;
            lastSyncedAt?: string;
        };
        overallHealth: 'healthy' | 'degraded' | 'unhealthy';
    }> {
        try {
            // Get product sync status
            const product = await prisma.freemiusProduct.findUnique({
                where: { id: this.config.productId },
                select: {
                    id: true,
                    sync_status: true,
                    last_synced_at: true,
                    api_last_synced_at: true,
                    api_sync_errors: true
                }
            });

            // Get installation sync statistics
            const installationStats = await prisma.freemiusInstallation.groupBy({
                by: ['sync_status'],
                _count: { sync_status: true }
            });

            const lastInstallationSync = await prisma.freemiusInstallation.findFirst({
                orderBy: { api_last_synced_at: 'desc' },
                select: { api_last_synced_at: true }
            });

            const installations = {
                total: installationStats.reduce((sum, stat) => sum + stat._count.sync_status, 0),
                active: installationStats.find(s => s.sync_status === 'active')?._count.sync_status || 0,
                syncing: installationStats.find(s => s.sync_status === 'syncing')?._count.sync_status || 0,
                error: installationStats.find(s => s.sync_status === 'error')?._count.sync_status || 0,
                lastSyncedAt: lastInstallationSync?.api_last_synced_at?.toISOString()
            };

            // Determine overall health
            let overallHealth: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
            if (!product || product.sync_status === 'error' || installations.error > installations.total * 0.5) {
                overallHealth = 'unhealthy';
            } else if (product.sync_status !== 'active' || installations.error > 0) {
                overallHealth = 'degraded';
            }

            return {
                product: product ? {
                    id: product.id,
                    status: product.sync_status,
                    lastSyncedAt: product.last_synced_at?.toISOString(),
                    apiLastSyncedAt: product.api_last_synced_at?.toISOString(),
                    errors: product.api_sync_errors as string[] || undefined
                } : null,
                installations,
                overallHealth
            };
        } catch (error) {
            this.logError('Failed to get sync status', {
                error: error instanceof Error ? error.message : 'Unknown error'
            });

            return {
                product: null,
                installations: { total: 0, active: 0, syncing: 0, error: 0 },
                overallHealth: 'unhealthy'
            };
        }
    }

    /**
     * Get service health status
     */
    async getHealthStatus(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        checks: {
            apiConnection: boolean;
            databaseConnection: boolean;
            configurationValid: boolean;
        };
        lastSync?: string;
        errors: string[];
    }> {
        const errors: string[] = [];
        const checks = {
            apiConnection: false,
            databaseConnection: false,
            configurationValid: false,
        };

        // Check configuration
        try {
            if (this.config.apiKey && this.config.productId && this.config.productSecretKey) {
                checks.configurationValid = true;
            } else {
                errors.push('Missing required configuration');
            }
        } catch (error) {
            errors.push('Configuration validation failed');
        }

        // Check database connection
        try {
            await prisma.freemiusProduct.findFirst();
            checks.databaseConnection = true;
        } catch (error) {
            errors.push('Database connection failed');
        }

        // Check API connection
        try {
            const connectionTest = await this.testConnection();
            checks.apiConnection = connectionTest.success;
            if (!connectionTest.success && connectionTest.error) {
                errors.push(`API connection failed: ${connectionTest.error}`);
            }
        } catch (error) {
            errors.push('API connection test failed');
        }

        // Get last sync time
        let lastSync: string | undefined;
        try {
            const product = await prisma.freemiusProduct.findFirst({
                orderBy: { last_synced_at: 'desc' },
                select: { last_synced_at: true },
            });
            if (product?.last_synced_at) {
                lastSync = product.last_synced_at.toISOString();
            }
        } catch (error) {
            errors.push('Failed to get last sync time');
        }

        // Determine overall status
        let status: 'healthy' | 'degraded' | 'unhealthy';
        if (checks.configurationValid && checks.databaseConnection && checks.apiConnection) {
            status = 'healthy';
        } else if (checks.configurationValid && checks.databaseConnection) {
            status = 'degraded';
        } else {
            status = 'unhealthy';
        }

        return {
            status,
            checks,
            lastSync,
            errors,
        };
    }
}

// Export singleton instance
export const freemiusService = new FreemiusService();