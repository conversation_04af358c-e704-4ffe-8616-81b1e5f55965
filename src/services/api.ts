import axios, { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';

// Extend AxiosRequestConfig to include metadata
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    metadata?: {
      requestId: number;
      startTime: number;
    };
  }
}
import {
  ApiResponse,
  LoginCredentials,
  TwoFactorCredentials,
  User,
  AnalyzeIpRequest,
  AnalyzeIpResponse,
  ApiRequestLog,
  ApiLogFilters,
  PaginatedResponse,
  IpData,
  DashboardStats,
  ManualTask,
  CreateUserRequest,
  UpdateUserRequest,
  ChangePasswordRequest,
  CreateAdminRequest,
  UpdateAdminRequest,
  AdminUser,
  AdminCreationResult,
  AdminListResponse,
  AdminListFilters,
  FreemiusProduct,
  FreemiusInstallation,
  IpRegistryData,
} from '../types';

class ApiClient {
  private client: AxiosInstance;
  private requestId = 0;

  constructor() {
    this.client = axios.create({
      baseURL: '/api/v1',
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 30000, // 30 second timeout
    });

    // Request interceptor for auth and logging
    this.client.interceptors.request.use(
      (config) => {
        // Add request ID for tracking
        const currentRequestId = ++this.requestId;
        config.metadata = { requestId: currentRequestId, startTime: Date.now() };

        // Add auth token
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Add request ID header
        config.headers['X-Request-ID'] = currentRequestId.toString();

        // Log request in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API Request ${currentRequestId}]`, {
            method: config.method?.toUpperCase(),
            url: config.url,
            data: config.data,
          });
        }

        return config;
      },
      (error) => {
        console.error('[API Request Error]', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for auth errors and logging
    this.client.interceptors.response.use(
      (response) => {
        const { requestId, startTime } = response.config.metadata || {};
        const duration = Date.now() - (startTime || 0);

        // Log response in development
        if (process.env.NODE_ENV === 'development') {
          console.log(`[API Response ${requestId}]`, {
            status: response.status,
            duration: `${duration}ms`,
            data: response.data,
          });
        }

        return response;
      },
      (error: AxiosError) => {
        const { requestId, startTime } = error.config?.metadata || {};
        const duration = Date.now() - (startTime || 0);

        // Log error in development
        if (process.env.NODE_ENV === 'development') {
          console.error(`[API Error ${requestId}]`, {
            status: error.response?.status,
            duration: `${duration}ms`,
            message: error.message,
            data: error.response?.data,
          });
        }

        // Handle auth errors
        if (error.response?.status === 401) {
          localStorage.removeItem('auth_token');
          // Only redirect if not already on login page
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private async request<T>(
    method: 'get' | 'post' | 'put' | 'delete',
    url: string,
    data?: any,
    retries: number = 0
  ): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.client[method](url, data);
      return response.data;
    } catch (error: any) {
      // Retry logic for network errors (not auth errors)
      if (retries < 2 && this.shouldRetry(error)) {
        await this.delay(Math.pow(2, retries) * 1000); // Exponential backoff
        return this.request(method, url, data, retries + 1);
      }

      // Return structured error response
      if (error.response?.data) {
        return error.response.data;
      }

      return {
        success: false,
        error: this.getErrorMessage(error),
      };
    }
  }

  private shouldRetry(error: AxiosError): boolean {
    // Retry on network errors or 5xx server errors, but not auth errors
    return (
      !error.response ||
      (error.response.status >= 500 && error.response.status !== 401)
    );
  }

  private getErrorMessage(error: AxiosError): string {
    if (error.code === 'ECONNABORTED') {
      return 'Request timeout - please try again';
    }
    if (error.code === 'ERR_NETWORK') {
      return 'Network error - please check your connection';
    }
    if (error.response?.status === 403) {
      return 'Access denied - insufficient permissions';
    }
    if (error.response?.status === 404) {
      return 'Resource not found';
    }
    if (error.response?.status >= 500) {
      return 'Server error - please try again later';
    }
    return error.message || 'An unexpected error occurred';
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Enhanced authentication endpoints
  async login(credentials: LoginCredentials): Promise<ApiResponse<{ user?: User; token?: string; sessionToken?: string; requires2FA?: boolean; requiresPasswordChange?: boolean; lockoutInfo?: any }>> {
    return this.request('post', '/auth/login', credentials);
  }

  async logout(): Promise<ApiResponse<void>> {
    return this.request('post', '/auth/logout');
  }

  async verify2FA(credentials: TwoFactorCredentials): Promise<ApiResponse<{ user: User; token: string }>> {
    return this.request('post', '/auth/verify-2fa', credentials);
  }

  async getProfile(): Promise<ApiResponse<{ user: User }>> {
    return this.request('get', '/auth/profile');
  }

  async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {
    return this.request('post', '/auth/change-password', data);
  }

  async enable2FA(): Promise<ApiResponse<{ qr_code: string; secret: string }>> {
    return this.request('post', '/auth/enable-2fa');
  }

  async disable2FA(token: string): Promise<ApiResponse<void>> {
    return this.request('post', '/auth/disable-2fa', { token });
  }

  async updateProfile(data: any): Promise<ApiResponse<any>> {
    return this.request('put', '/auth/profile', data);
  }



  // Enhanced session management endpoints
  async validateSession(): Promise<ApiResponse<{ user: User; requiresRefresh?: boolean; session?: any }>> {
    return this.request('get', '/sessions/validate');
  }

  async refreshSession(): Promise<ApiResponse<{ sessionToken: string; expiresAt: string; user: User }>> {
    return this.request('post', '/sessions/refresh');
  }

  async getSessionList(): Promise<ApiResponse<{ sessions: any[]; totalSessions: number; currentSessionId: string }>> {
    return this.request('get', '/sessions/list');
  }

  async revokeSession(sessionId: string): Promise<ApiResponse<void>> {
    return this.request('delete', `/sessions/${sessionId}`);
  }

  async revokeOtherSessions(): Promise<ApiResponse<{ revokedCount: number }>> {
    return this.request('post', '/sessions/revoke-others');
  }

  async getSessionSecurityMetrics(): Promise<ApiResponse<any>> {
    return this.request('get', '/sessions/security-metrics');
  }

  // Password reset endpoints
  async requestPasswordReset(email: string): Promise<ApiResponse<void>> {
    return this.request('post', '/auth/request-password-reset', { email });
  }

  async resetPassword(token: string, newPassword: string): Promise<ApiResponse<void>> {
    return this.request('post', '/auth/reset-password', { token, newPassword });
  }

  // Core API endpoints
  async analyzeIp(data: AnalyzeIpRequest): Promise<ApiResponse<AnalyzeIpResponse>> {
    return this.request('post', '/analyze', data);
  }

  // Admin endpoints
  async getDashboardStats(): Promise<ApiResponse<DashboardStats>> {
    return this.request('get', '/admin/dashboard/stats');
  }

  async getSystemHealth(): Promise<ApiResponse<{
    status: 'healthy' | 'warning' | 'critical';
    database: { status: string; responseTime: number };
    externalApis: {
      freemius: { status: string; lastCheck: string };
      ipRegistry: { status: string; lastCheck: string };
    };
    memory: { used: number; total: number; percentage: number };
    uptime: number;
    lastUpdated: string;
  }>> {
    return this.request('get', '/admin/system/health');
  }

  async getRecentActivity(limit: number = 10): Promise<ApiResponse<{
    requests: ApiRequestLog[];
    logins: any[];
    errors: any[];
  }>> {
    return this.request('get', `/admin/dashboard/recent-activity?limit=${limit}`);
  }

  async getApiLogs(filters: ApiLogFilters): Promise<ApiResponse<PaginatedResponse<ApiRequestLog>>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    return this.request('get', `/admin/logs/api?${params.toString()}`);
  }

  async getApiLogById(id: string): Promise<ApiResponse<ApiRequestLog>> {
    return this.request('get', `/admin/logs/api/${id}`);
  }

  async searchIpData(ip: string): Promise<ApiResponse<IpData | null>> {
    return this.request('get', `/admin/ip-data/search?ip=${encodeURIComponent(ip)}`);
  }

  async refreshIpData(ip: string): Promise<ApiResponse<IpData>> {
    return this.request('post', '/admin/ip-data/refresh', { ip });
  }

  // IP Intelligence endpoints
  async lookupIp(ip: string): Promise<ApiResponse<any>> {
    return this.request('post', '/admin/ip-intelligence/lookup', { ip });
  }

  async refreshIpIntelligence(ip: string): Promise<ApiResponse<any>> {
    return this.request('post', `/admin/ip-intelligence/refresh/${encodeURIComponent(ip)}`);
  }

  async getIpAnalysisHistory(filters: any): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        params.append(key, value.toString());
      }
    });
    return this.request('get', `/admin/ip-intelligence/history?${params.toString()}`);
  }

  async getIpAnalysisStats(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/ip-intelligence/stats?timeRange=${timeRange}`);
  }

  async getManualTasks(): Promise<ApiResponse<ManualTask[]>> {
    return this.request('get', '/admin/tasks');
  }

  async createManualTask(type: ManualTask['type']): Promise<ApiResponse<ManualTask>> {
    return this.request('post', '/admin/tasks', { type });
  }

  async getTaskStatus(id: string): Promise<ApiResponse<ManualTask>> {
    return this.request('get', `/admin/tasks/${id}`);
  }

  // User management endpoints (Super Admin only)
  async getUsers(): Promise<ApiResponse<User[]>> {
    return this.request('get', '/admin/users');
  }

  async createUser(data: CreateUserRequest): Promise<ApiResponse<User>> {
    return this.request('post', '/admin/users', data);
  }

  async updateUser(id: string, data: UpdateUserRequest): Promise<ApiResponse<User>> {
    return this.request('put', `/admin/users/${id}`, data);
  }

  async deleteUser(id: string): Promise<ApiResponse<void>> {
    return this.request('delete', `/admin/users/${id}`);
  }

  // Admin management endpoints (Super Admin only)
  async getAdminUsers(filters?: AdminListFilters): Promise<ApiResponse<AdminListResponse>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return this.request('get', `/admin/admin-users?${params.toString()}`);
  }

  async createAdminUser(data: CreateAdminRequest): Promise<ApiResponse<AdminCreationResult>> {
    return this.request('post', '/admin/admin-users', data);
  }

  async updateAdminUser(id: string, data: UpdateAdminRequest): Promise<ApiResponse<AdminCreationResult>> {
    return this.request('put', `/admin/admin-users/${id}`, data);
  }

  async deactivateAdminUser(id: string): Promise<ApiResponse<AdminCreationResult>> {
    return this.request('post', `/admin/admin-users/${id}/deactivate`);
  }

  async getAdminUserById(id: string): Promise<ApiResponse<AdminUser>> {
    return this.request('get', `/admin/admin-users/${id}`);
  }

  async validateAdminEmail(email: string): Promise<ApiResponse<{ isAvailable: boolean }>> {
    return this.request('get', `/admin/admin-users/validate-email?email=${encodeURIComponent(email)}`);
  }

  // Freemius data endpoints
  async getProducts(): Promise<ApiResponse<FreemiusProduct[]>> {
    return this.request('get', '/admin/freemius/products');
  }

  async getInstallations(filters?: {
    product_id?: string;
    page?: number;
    limit?: number;
    search?: string;
    is_active?: boolean;
  }): Promise<ApiResponse<PaginatedResponse<FreemiusInstallation>>> {
    const params = new URLSearchParams();
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }
    return this.request('get', `/admin/freemius/installations?${params.toString()}`);
  }

  async getInstallationById(id: string): Promise<ApiResponse<FreemiusInstallation>> {
    return this.request('get', `/admin/freemius/installations/${id}`);
  }

  async syncFreemiusData(): Promise<ApiResponse<ManualTask>> {
    return this.request('post', '/admin/freemius/sync');
  }

  async getFreemiusStats(): Promise<ApiResponse<{
    totalProducts: number;
    totalInstallations: number;
    activeInstallations: number;
    totalRevenue: number;
    lastSyncAt: string | null;
  }>> {
    return this.request('get', '/admin/freemius/stats');
  }

  // System Settings endpoints (Super Admin only)
  async getSystemSettings(): Promise<ApiResponse<any[]>> {
    return this.request('get', '/admin/system/settings');
  }

  async updateSystemSetting(key: string, value: any, reason?: string): Promise<ApiResponse<void>> {
    return this.request('put', `/admin/system/settings/${key}`, { value, reason });
  }

  async getSettingsChangeLog(settingKey?: string): Promise<ApiResponse<any[]>> {
    const url = settingKey
      ? `/admin/system/settings/changelog?key=${encodeURIComponent(settingKey)}`
      : '/admin/system/settings/changelog';
    return this.request('get', url);
  }

  // Enhanced Audit Trail and Security Monitoring endpoints
  async getAuditLogs(filters: any): Promise<ApiResponse<any>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          params.append(key, value.join(','));
        } else {
          params.append(key, value.toString());
        }
      }
    });
    return this.request('get', `/admin/audit-logs?${params.toString()}`);
  }

  async getAuditStats(timeframe: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/audit-logs/stats?timeframe=${timeframe}`);
  }

  async getSecurityAlerts(timeRange?: string): Promise<ApiResponse<any>> {
    const params = timeRange ? `?timeRange=${timeRange}` : '';
    return this.request('get', `/admin/security/alerts${params}`);
  }

  async verifyLogIntegrity(): Promise<ApiResponse<any>> {
    return this.request('post', '/admin/audit-logs/verify-integrity');
  }

  async getSecurityReport(timeRange: 'week' | 'month' | 'quarter' | 'year' = 'month'): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/security/compliance-report?timeRange=${timeRange}`);
  }

  async exportAuditLogs(filters: any): Promise<ApiResponse<string>> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          params.append(key, value.join(','));
        } else {
          params.append(key, value.toString());
        }
      }
    });

    try {
      const response = await this.client.get(`/admin/audit-logs/export?${params.toString()}`, {
        responseType: 'text',
      });
      return { success: true, data: response.data };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to export audit logs',
      };
    }
  }



  async executeRetentionPolicy(policyId: string): Promise<ApiResponse<any>> {
    return this.request('post', `/admin/security/retention/${policyId}/execute`);
  }

  // Security Monitoring endpoints
  async getSecurityDashboard(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/security/dashboard?timeRange=${timeRange}`);
  }

  async getSecurityMetrics(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/security/metrics?timeRange=${timeRange}`);
  }

  async getFailedLoginAttempts(timeRange: 'hour' | 'day' | 'week' = 'day', limit: number = 100): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/security/failed-logins?timeRange=${timeRange}&limit=${limit}`);
  }

  async getSessionAnomalies(timeRange: 'hour' | 'day' | 'week' = 'day', limit: number = 50): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/security/session-anomalies?timeRange=${timeRange}&limit=${limit}`);
  }

  async getSecurityTrends(timeRange: 'hour' | 'day' | 'week' | 'month' = 'day'): Promise<ApiResponse<any>> {
    return this.request('get', `/admin/security/trends?timeRange=${timeRange}`);
  }

  // Security Alerting endpoints
  async getAlertRules(): Promise<ApiResponse<any>> {
    return this.request('get', '/admin/security/alert-rules');
  }

  async updateAlertRule(ruleId: string, updates: any): Promise<ApiResponse<any>> {
    return this.request('put', `/admin/security/alert-rules/${ruleId}`, updates);
  }

  async resolveSecurityAlert(alertId: string, reason?: string): Promise<ApiResponse<any>> {
    return this.request('post', `/admin/security/alerts/${alertId}/resolve`, { reason });
  }

  async suppressSecurityAlert(alertId: string, duration: number, reason: string): Promise<ApiResponse<any>> {
    return this.request('post', `/admin/security/alerts/${alertId}/suppress`, { duration, reason });
  }

  async getSecurityHealthCheck(): Promise<ApiResponse<any>> {
    return this.request('get', '/admin/security/health-check');
  }

  async generateSecurityReport(type: 'DAILY' | 'WEEKLY' | 'MONTHLY'): Promise<ApiResponse<any>> {
    return this.request('post', '/admin/security/reports/generate', { type });
  }

  async evaluateSecurityAlerts(): Promise<ApiResponse<any>> {
    return this.request('post', '/admin/security/alerts/evaluate');
  }
}

export const api = new ApiClient();

// Separate auth API for cleaner imports
export const authApi = {
  login: api.login.bind(api),
  verify2FA: api.verify2FA.bind(api),
  getProfile: api.getProfile.bind(api),
  changePassword: api.changePassword.bind(api),
  enable2FA: api.enable2FA.bind(api),
  disable2FA: api.disable2FA.bind(api),
};