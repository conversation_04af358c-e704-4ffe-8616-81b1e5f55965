import axios, { AxiosInstance, AxiosError } from 'axios';
import { Logger } from '../lib/logger';

// Country information interface
interface CountryInfo {
    country: string | null;
    ip: string;
}



// Configuration interface for country.is API
interface CountryServiceConfig {
    apiUrl: string;
    timeout: number;
    retryAttempts: number;
    retryDelay: number;
}

export class CountryService {
    private client: AxiosInstance;
    private config: CountryServiceConfig;

    constructor() {
        // Load configuration from environment variables
        this.config = {
            apiUrl: process.env.COUNTRY_API_URL || 'https://api.country.is',
            timeout: parseInt(process.env.COUNTRY_API_TIMEOUT || '5000'),
            retryAttempts: parseInt(process.env.COUNTRY_API_RETRY_ATTEMPTS || '2'),
            retryDelay: parseInt(process.env.COUNTRY_API_RETRY_DELAY || '1000'),
        };

        // Initialize HTTP client
        this.client = axios.create({
            baseURL: this.config.apiUrl,
            timeout: this.config.timeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'GuardGeo-Backend/1.0',
            },
        });

        // Add interceptors for error handling and logging
        this.client.interceptors.request.use(
            (config) => {
                Logger.info('Country API Request', {
                    url: config.url,
                    method: config.method?.toUpperCase(),
                    ip: config.url?.split('/').pop()
                }, {
                    service: 'country-detection',
                    operation: 'api_request'
                });
                return config;
            },
            (error) => Promise.reject(error)
        );

        this.client.interceptors.response.use(
            (response) => {
                Logger.info('Country API Response Success', {
                    url: response.config.url,
                    status: response.status,
                    ip: response.config.url?.split('/').pop(),
                    country: response.data?.country
                }, {
                    service: 'country-detection',
                    operation: 'api_response'
                });
                return response;
            },
            (error: AxiosError) => {
                this.logError('Country API Request Failed', {
                    error,
                    url: error.config?.url,
                    method: error.config?.method,
                    status: error.response?.status,
                    message: error.message,
                    data: error.response?.data,
                });
                return Promise.reject(error);
            }
        );
    }

    /**
     * Get country information by IP address
     */
    async getCountryByIp(ip: string): Promise<CountryInfo | null> {
        try {
            this.logInfo(`Getting country for IP: ${ip}`);

            // Validate IP address
            if (!ip || typeof ip !== 'string') {
                throw new Error('Invalid IP address provided');
            }

            // Check if it's a private IP (country.is doesn't work with private IPs)
            if (this.isPrivateIp(ip)) {
                this.logInfo(`Private IP detected, skipping country lookup: ${ip}`);
                return {
                    country: null,
                    ip: ip
                };
            }

            // Make API call with retry logic
            const response = await this.retryApiCall(async () => {
                const result = await this.client.get(`/${ip}`);
                return result.data;
            });

            const countryInfo: CountryInfo = {
                country: response.country || null,
                ip: response.ip || ip
            };

            this.logInfo(`Country lookup successful for ${ip}`, {
                country: countryInfo.country
            });

            return countryInfo;
        } catch (error) {
            const errorMsg = `Country lookup failed for ${ip}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.logError('Country Lookup Failed', { ip, error: errorMsg });

            // Return null on error but don't throw - this is a fallback service
            return {
                country: null,
                ip: ip
            };
        }
    }

    /**
     * Check if IP is private or reserved (country.is doesn't work with these)
     */
    private isPrivateIp(ip: string): boolean {
        if (!ip || typeof ip !== 'string') {
            return true;
        }

        // Private and reserved IPv4 ranges
        const privateRanges = [
            /^10\./,                    // 10.0.0.0/8 - Private
            /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12 - Private
            /^192\.168\./,              // ***********/16 - Private
            /^127\./,                   // *********/8 - Loopback
            /^169\.254\./,              // ***********/16 - Link-local
            /^0\./,                     // 0.0.0.0/8 - Current network
            /^224\./,                   // *********/4 - Multicast
            /^240\./,                   // 240.0.0.0/4 - Reserved
            /^255\.255\.255\.255$/,     // Broadcast
            /^::1$/,                    // IPv6 loopback
            /^fc00:/i,                  // IPv6 unique local
            /^fe80:/i,                  // IPv6 link-local
            /^ff00:/i,                  // IPv6 multicast
            /^::$/,                     // IPv6 unspecified
        ];

        return privateRanges.some(range => range.test(ip));
    }

    /**
     * Retry API calls with exponential backoff
     */
    private async retryApiCall<T>(apiCall: () => Promise<T>): Promise<T> {
        let lastError: Error;

        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                const result = await apiCall();
                return result;
            } catch (error) {
                lastError = error instanceof Error ? error : new Error('Unknown error');

                if (attempt === this.config.retryAttempts) {
                    throw lastError;
                }

                // Calculate exponential backoff delay
                const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
                this.logInfo(`Country API call failed, retrying in ${delay}ms (attempt ${attempt}/${this.config.retryAttempts})`);

                await new Promise(resolve => setTimeout(resolve, delay));
            }
        }

        throw lastError!;
    }

    /**
     * Logging methods
     */
    private logInfo(message: string, data?: any): void {
        Logger.info(`[CountryService] ${message}`, data, {
            service: 'country-detection',
            component: 'CountryService'
        });
    }

    private logError(message: string, data?: any): void {
        const error = data?.error instanceof Error ? data.error : new Error(message);
        Logger.error(`[CountryService] ${message}`, error, data, {
            service: 'country-detection',
            component: 'CountryService'
        });
    }

    /**
     * Get service health status
     */
    async getHealthStatus(): Promise<{
        status: 'healthy' | 'unhealthy';
        responseTime?: number;
        error?: string;
    }> {
        try {
            const startTime = Date.now();

            // Test with a known public IP (Google DNS)
            const testIp = '*******';
            await this.getCountryByIp(testIp);

            const responseTime = Date.now() - startTime;

            return {
                status: 'healthy',
                responseTime
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }
}

// Export singleton instance
export const countryService = new CountryService();