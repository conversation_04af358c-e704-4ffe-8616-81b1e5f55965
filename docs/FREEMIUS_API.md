
# API docs and endpoints for this project

> The Freemius API Documentation!
> You can use our API to access Freemius API endpoints, which can get information on various aspects of Freemius.
> - Manage products.
> - Manage license verifications.
> - Create custom integration with your SaaS.

## Auth:
Freemius API supports Bearer Token authentication for product-specific operations.
```
Authorization: Bearer {api_token}
````

## Endoints needed for this project
### Retrieve a product info (Plugin info from Freemius platform)

```javascript
const query = new URLSearchParams({fields: 'id,name,slug'}).toString();
const productId = '1234';
const resp = await fetch(
'https://api.freemius.com/v1/products/${productId}.json?${query}',
{
	method: 'GET',
	headers: {
		Authorization: 'Bearer <api_token>'
	}
}
);
const data = await resp.text();
```
- `{productId}` ( _(int64)_ _>= 1_) : is an integer obtained from the Freemius developer dashboard. It must be set in `.env`
- `{query}` : Every field of the API response can be used as filter (URL encoded). *Example*: `title=My Awesome Plugin`
- `{fields}`: Comma separated list of fields to return in the response. If not specified, all fields are returned.  *Example*: `fields=id,name,slug`

### Retrieve an install info
An install is a website that has installed the plugin, not the fact that the plugin is installed.
```javascript
const query = new URLSearchParams({fields: 'id,name,slug'}).toString(); const productId = '1234'; const installId = '1234'; const resp = await fetch( `https://api.freemius.com/v1/products/${productId}/installs/${installId}.json?${query}`, { method: 'GET', headers: { Authorization: 'Bearer <api_token>' } } ); const data = await resp.text(); console.log(data);
```

The `installId` is generated automatically every time a client (website) install a plugin/theme that use Freemius WP SDK for licensing. The id is tied to that unique website. Its is used in webhook details sent by the WP plugin.

## Retrieve ALL websites (installations)

```javascript
const query = new URLSearchParams({ filter: 'all' }).toString(); const productId = '1234'; const resp = await fetch( `https://api.freemius.com/v1/products/${productId}/installs.json?${query}`, { method: 'GET', headers: { Authorization: 'Bearer <api_token>' } } ); const data = await resp.text(); console.log(data);
```
`filter`can take  `Enum"all""active""inactive""trial""paying""uninstalled""active_premium""active_free"`

Yes these are ALL the relevent endpoints to US. Nothing ELSE.