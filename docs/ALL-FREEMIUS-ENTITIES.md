# Official Freemius API entities & and their data + fields + types

## Products

API response:
```json
{
  "secret_key": "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "public_key": "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "parent_plugin_id": "123456",
  "developer_id": "123456",
  "store_id": "123456",
  "slug": "my-freemius-plugin",
  "title": "My Freemius Plugin",
  "environment": 0,
  "icon": "https://img.freemius.com/plugin-icon-blue.png",
  "default_plan_id": "string",
  "plans": "123456,123457,123458",
  "features": "123456,123457,123458",
  "money_back_period": 0,
  "refund_policy": "flexible",
  "annual_renewals_discount": 0,
  "renewals_discount_type": "percentage",
  "is_released": true,
  "is_sdk_required": true,
  "is_pricing_visible": true,
  "is_wp_org_compliant": true,
  "installs_count": 0,
  "active_installs_count": 0,
  "free_releases_count": 0,
  "premium_releases_count": 0,
  "total_purchases": 0,
  "total_subscriptions": 0,
  "total_renewals": 0,
  "total_failed_purchases": "1234",
  "earnings": "1234.56",
  "type": "plugin",
  "is_static": true
}
```

* `Body`: application/json

- `secret_key` *string*
The secret key associated with the entity for authorization.
_Example_: "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `public_key` *string*
The public key associated with the entity for authorization.
_Example_: "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `parent_plugin_id` *string or null* *(int64)* *>= 1*
If the product is an add-on then this is the ID of the parent product.
_Example_: "123456"

- `developer_id` *string* *(int64)* *>= 1*
The ID of the developer that owns the product.
_Example_: "123456"

- `store_id` *string* *(int64)* *>= 1*
The ID of the store that the product is being sold on.
_Example_: "123456"

- `slug` *string*
The `slug` of the product. If your plugin is listed on `WordPress.org` repository, use the exact slug.
_Example_: "my-freemius-plugin"

- `title` *string*
The title of the product.
_Example_: "My Freemius Plugin"

- `environment` *number*
The environment the entity belongs to. 0 means it belongs to the production environment, 1 means it belongs to the sandbox environment.
Enum "1" "0"
_Example_: 0

- `icon` *string or null*
Product's icon (profile picture).
_Example_: "https://img.freemius.com/plugin-icon-blue.png"

- `default_plan_id` *string* *(int64)*
Default plan ID of the product.

- `plans` *string*
Comma separated, ordered plans collection.
_Example_: "123456,123457,123458"

- `features` *string*
Comma separated, ordered features collection.
_Example_: "123456,123457,123458"

- `money_back_period` *integer*
Money-back guarantee in days.

- `refund_policy` *string*
Enum "flexible" "moderate" "strict"

- `annual_renewals_discount` *integer or null*
Renewals discount that will be applied to the chosen plan.

- `renewals_discount_type` *string*
The type of renewals discount, percentage or dollar.
Enum "percentage" "dollar"

- `is_released` *boolean*
A flag that controls the visibility of add-ons in the in-dashboard add-ons marketplace. Defaults to true. Only applicable if the product is an add-on.

- `is_sdk_required` *boolean*
A flag that controls whether the SDK should be required or not during deployment of a version. It defaults to `true`.

- `is_pricing_visible` *boolean*
Determines if the pricing should be visible in the in-SDK pricing page. Defaults to true. Turn this off during the development of a new product.

- `is_wp_org_compliant` *boolean*
Set to true if the free version of the module is hosted on WordPress.org. Defaults to true.

- `installs_count` *integer*
Total number of opted in sites which were logged with the SDK.

- `active_installs_count` *integer*
Total number of active sites where the SDK is active.

- `free_releases_count` *integer*
The number of "free" version of the product that were deployed from Freemius.

- `premium_releases_count` *integer*
The number of "premium" version of the product that were deployed from Freemius.

- `total_purchases` *integer*
Total number of payments recorded for the product.

- `total_subscriptions` *integer*
Total number of subscriptions recorded for the product.

- `total_renewals` *integer*
Total number of renewals recorded for the product.

- `total_failed_purchases` *integer*
Total number of failed payments recorded for the product.
_Example_: "1234"

- `earnings` *number* *(float)*
Total gross revenues.
_Example_: "1234.56"

- `type` *string*
Enum "plugin" "theme" "widget" "template"

- `is_static` *boolean*
Determines whether the product is categorized as a static product (for example, a widget or a template).

## Installation

API response:

```json
{
  "secret_key": "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "public_key": "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6",
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "site_id": 1234,
  "plugin_id": "123456",
  "user_id": "123456",
  "url": "https://example.com",
  "title": "Catwalk Designs",
  "version": "1.0.0",
  "plan_id": "1234",
  "license_id": "123456",
  "trial_plan_id": "1234",
  "trial_ends": "2025-01-01 00:00:00",
  "subscription_id": "123456",
  "gross": 100,
  "country_code": "us",
  "language": "en-GB",
  "platform_version": "1.0.0",
  "sdk_version": "1.2.2",
  "programming_language_version": "5.6",
  "is_active": true,
  "is_disconnected": true,
  "is_premium": true,
  "is_uninstalled": true,
  "is_locked": true,
  "source": 0,
  "upgraded": "2025-01-01 00:00:00",
  "last_seen_at": "2025-01-01 00:00:00",
  "last_served_update_version": "1.2.2",
  "is_beta": true
}
```

* `Body`: application/json

- `secret_key` *string*
The secret key associated with the entity for authorization.
_Example_: "sk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `public_key` *string*
The public key associated with the entity for authorization.
_Example_: "pk_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6"

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `site_id` *string* *(int64)*
The ID of the site.
_Example_: "1234"

- `plugin_id` *string* *(int64)* *>= 1*
The ID of the product the entity belongs to.
_Example_: "123456"

- `user_id` *string* *(int64)* *>= 1*
The ID of the user the entity belongs to.
_Example_: "123456"

- `url` *string or null*
The site URL.
_Example_: "https://example.com"

- `title` *string or null*
The site title.
_Example_: "Catwalk Designs"

- `version` *string*
The Product version.
_Example_: "1.0.0"

- `plan_id` *string* *(int64)* *>= 1*
The ID of the plan associated with the product that the install has a license activation. If `null` it means the install is using the free plan.
_Example_: "1234"

- `license_id` *string or null* *(int64)* *>= 1*
The ID of the license associated with the entity.
_Example_: "123456"

- `trial_plan_id` *string* *(int64)* *>= 1*
The ID of the trial license associated to the product. If this is not a trial, this will be `null`.
_Example_: "1234"

- `trial_ends` *string or null* *(date-time)*
The product trial license expiry date. If this is not a trial, this will be null.
_Example_: "2025-01-01 00:00:00"

- `subscription_id` *string or null* *(int64)* *>= 1*
The ID of the subscription associated with the entity.
_Example_: "123456"

- `gross` *number* *(float)*
The gross amount the install has spent on the product. This includes one time purchase, or subscriptions and renewals.
_Example_: 100

- `country_code` *string or null*
The ISO 3166-1 alpha 2 two-letter country code associated with the entity.
_Example_: "us"

- `language` *string or null*
The language specified for the product install.
_Example_: "en-GB"

- `platform_version` *string or null*
The platform version (e.g WordPress version).
_Example_: "1.0.0"

- `sdk_version` *string or null*
The Freemius SDK version. Only relevant for WordPress products using the Freemius WP SDK.
_Example_: "1.2.2"

- `programming_language_version` *string or null*
The programming language version (e.g PHP version).
_Example_: "5.6"

- `is_active` *boolean*
If the product is actively installed on the site.

- `is_disconnected` *boolean*
If the product is disconnected on the site.

- `is_premium` *boolean*
If the install using the premium code. Relevant only for WP Products.

- `is_uninstalled` *boolean*
If the product is uninstalled on the site.

- `is_locked` *boolean*
If the product is locked on the site.

- `source` *number*
The source of the migration data. To get support migrating from other platform please see our documentation.
Enum "0" "1" "2" "3" "4" "5" "6" "7" "8" "9" "10" "11"

- `upgraded` *string or null* *(date-time)*
Time when the product was upgraded to the current version. If never upgraded since the initial installation, this will be `null`.
_Example_: "2025-01-01 00:00:00"

- `last_seen_at` *string or null* *(date-time)*
The last time the product was used on the site.
_Example_: "2025-01-01 00:00:00"

- `last_served_update_version` *string or null*
The last product version update used on the site. If not updated, this will be null.
_Example_: "1.2.2"

- `is_beta` *boolean*
Whether the install is participating in the beta program.

## Event

API response:
```json
{
  "id": "123456",
  "created": "2025-01-01 00:00:00",
  "updated": "2025-01-01 00:00:00",
  "type": "license.activated",
  "developer_id": "1234",
  "plugin_id": "123456",
  "user_id": "123456",
  "install_id": "123456",
  "data": null,
  "event_trigger": "system",
  "process_time": "2025-01-01 12:00:00",
  "objects": {
    "user": {
      "id": "1234567",
      "email": "<EMAIL>",
      "first": "Joe",
      "last": "Doe",
      "is_verified": true
    },
    "install": {
      "id": "1234567",
      "plugin_id": "12345",
      "user_id": "1234567",
      "url": "https://example.com",
      "plan_id": "12345"
    },
    "license": {
      "plugin_id": "12345",
      "user_id": "12345",
      "plan_id": "12345",
      "pricing_id": "12345",
      "quota": 10,
      "expiration": "2025-10-01 10:11:46",
      "id": "1234567",
      "created": "2025-01-01 01:11:46",
      "updated": "2025-01-01 01:11:46"
    },
    "payment": {
      "subscription_id": "12345",
      "gross": 14.99,
      "gateway_fee": 0.41,
      "vat": 1.52,
      "is_renewal": true,
      "type": "payment",
      "user_id": "12345",
      "install_id": "12345",
      "plan_id": "12345",
      "pricing_id": "12345",
      "license_id": "12345"
    }
  }
}
```

* `Body`: application/json

- `id` *string* *(int64)* *>= 1*
The unique identifier of the entity.
_Example_: "123456"

- `created` *string* *(date-time)*
The date and time the entity was created, under UTC timezone.
_Example_: "2025-01-01 00:00:00"

- `updated` *string or null* *(date-time)*
The date and time the entity was updated, under UTC timezone. If `null` then the entity was never updated since its creation.
_Example_: "2025-01-01 00:00:00"

- `type` *string*
The type of event. See our documented list of the available event types.
_Example_: "license.activated"

- `developer_id` *string* *(int64)* *>= 1*
The ID of the developer.
_Example_: "1234"

- `plugin_id` *string or null* *(int64)* *>= 1*
The ID of the product the entity belongs to. Null means it has not been associated with a product yet.
_Example_: "123456"

- `user_id` *string or null* *(int64)* *>= 1*
The ID of the user the entity belongs to. If NULL then still not associated to any user.
_Example_: "123456"

- `install_id` *string or null* *(int64)* *>= 1*
The ID of the installation or site the entity is associated with, a `null` value means it has not been associated with an installation yet.
_Example_: "123456"

- `data` *any or null*
The details of the triggered event. This can be a `string` showing ID of the associated entity, an `object` with additional information of the event, or array of objects.

- `event_trigger` *string*
The type of trigger for the event.
Enum "system" "developer" "plugin" "user" "install"
_Example_: "system"

- `process_time` *string or null* *(date-time)*
The time the event was processed. If it is `null`, the event is pending.
_Example_: "2025-01-01 12:00:00"

- `objects` *object*
A map of related objects keyed by their type or ID, such as Payment, User, Install, or License.
_Example_: 
```json
{
  "user": {
    "id": "1234567",
    "email": "<EMAIL>",
    "first": "Joe",
    "last": "Doe",
    "is_verified": true
  },
  "install": {
    "id": "1234567",
    "plugin_id": "12345",
    "user_id": "1234567",
    "url": "https://example.com",
    "plan_id": "12345"
  },
  "license": {
    "plugin_id": "12345",
    "user_id": "12345",
    "plan_id": "12345",
    "pricing_id": "12345",
    "quota": 10,
    "expiration": "2025-10-01 10:11:46",
    "id": "1234567",
    "created": "2025-01-01 01:11:46",
    "updated": "2025-01-01 01:11:46"
  },
  "payment": {
    "subscription_id": "12345",
    "gross": 14.99,
    "gateway_fee": 0.41,
    "vat": 1.52,
    "is_renewal": true,
    "type": "payment",
    "user_id": "12345",
    "install_id": "12345",
    "plan_id": "12345",
    "pricing_id": "12345",
    "license_id": "12345"
  }
}
```