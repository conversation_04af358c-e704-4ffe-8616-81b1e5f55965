# GuardGeo API Platform

GuardGeo API is a private, proprietary backend system designed exclusively for the GuardGeo WordPress plugin and authorized administrators. It serves as an IP intelligence platform tightly integrated with Freemius (licensing/subscription management platform) and ipRegistry (IP geolocation data). No registration, super admin account data must be seeded.

## Core Purpose

- Validate incoming plugin requests via Freemius API
- Fetch and store IP intelligence data from ipRegistry  
- Return structured IP analysis results to authorized WordPress installations
- Provide admin interface for monitoring, analytics, and manual operations

## Key Characteristics

- **Private/Internal Only**: No public registration or consumer access
- **Admin-Focused**: All users are admin-level (Super Admin, Dev, Marketing, Sales)
- **Production-Grade**: Commercial-ready implementation following world-class best practices
- **Freemius-Centric**: All requests must be validated against active Freemius subscriptions
- **Comprehensive Logging**: Every action (API, web, Webhook event and data, All Admin actions and operations) must be logged in detail

## Business Rules

- Only Super Admin can create other admin accounts
- All installations must have active paid Freemius subscriptions
- IP data is cached for 3 days before refreshing from ipRegistry
- Authentication restricted to configured email domains only
- Minimum 12-character passwords required

## Routing Convention

- **Admin Routes**: `/admin/*`
- **API Routes**: `/api/v1/*`
- **Webhooks**: `/api/v1/webhooks/*`