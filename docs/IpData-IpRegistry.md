```json
{
  "ip": "**************",
  "type": "IPv4",
  "hostname": null,
  "carrier": {
    "name": null,
    "mcc": null,
    "mnc": null
  },
  "company": {
    "domain": "gva.africa",
    "name": "Gva Togo",
    "type": "isp"
  },
  "connection": {
    "asn": 36924,
    "domain": "gva.africa",
    "organization": "Gva Cote D'ivoire SAS",
    "route": "************/17",
    "type": "isp"
  },
  "currency": {
    "code": "XOF",
    "name": "West African CFA Franc",
    "name_native": "franc CFA (BCEAO)",
    "plural": "West African CFA francs",
    "plural_native": "francs CFA (BCEAO)",
    "symbol": "F CFA",
    "symbol_native": "F CFA",
    "format": {
      "decimal_separator": ",",
      "group_separator": " ",
      "negative": {
        "prefix": "-",
        "suffix": " F CFA"
      },
      "positive": {
        "prefix": "",
        "suffix": " F CFA"
      }
    }
  },
  "location": {
    "continent": {
      "code": "AF",
      "name": "Africa"
    },
    "country": {
      "area": 56785,
      "borders": [
        "BF",
        "BJ",
        "GH"
      ],
      "calling_code": "228",
      "capital": "Lomé",
      "code": "TG",
      "name": "Togo",
      "population": 9515236,
      "population_density": 167.57,
      "flag": {
        "emoji": "🇹🇬",
        "emoji_unicode": "U+1F1F9 U+1F1EC",
        "emojitwo": "https://cdn.ipregistry.co/flags/emojitwo/tg.svg",
        "noto": "https://cdn.ipregistry.co/flags/noto/tg.png",
        "twemoji": "https://cdn.ipregistry.co/flags/twemoji/tg.svg",
        "wikimedia": "https://cdn.ipregistry.co/flags/wikimedia/tg.svg"
      },
      "languages": [
        {
          "code": "fr",
          "name": "French",
          "native": "français"
        },
        {
          "code": "ee",
          "name": "Ewe",
          "native": "eʋegbe"
        },
        {
          "code": "ha",
          "name": "Hausa",
          "native": "Hausa"
        }
      ],
      "tld": ".tg"
    },
    "region": {
      "code": "TG-M",
      "name": "Maritime"
    },
    "city": "Lomé",
    "postal": "BP1172",
    "latitude": 6.1316,
    "longitude": 1.224,
    "language": {
      "code": "fr",
      "name": "French",
      "native": "français"
    },
    "in_eu": false
  },
  "security": {
    "is_abuser": false,
    "is_attacker": false,
    "is_bogon": false,
    "is_cloud_provider": false,
    "is_proxy": false,
    "is_relay": false,
    "is_tor": false,
    "is_tor_exit": false,
    "is_vpn": false,
    "is_anonymous": false,
    "is_threat": false
  },
  "time_zone": {
    "id": "Africa/Lome",
    "abbreviation": "GMT",
    "current_time": "2025-09-02T15:31:43Z",
    "name": "Greenwich Mean Time",
    "offset": 0,
    "in_daylight_saving": false
  }
}
```

----

```json
{
  "ip": "*************",
  "type": "IPv4",
  "hostname": "ec2-54-85-132-205.compute-1.amazonaws.com",
  "carrier": {
    "name": null,
    "mcc": null,
    "mnc": null
  },
  "company": {
    "domain": "amazon.com",
    "name": "Amazon Technologies Inc.",
    "type": "hosting"
  },
  "connection": {
    "asn": 14618,
    "domain": "aws.com",
    "organization": "Amazon.com, Inc.",
    "route": "*********/15",
    "type": "hosting"
  },
  "currency": {
    "code": "USD",
    "name": "US Dollar",
    "name_native": "US Dollar",
    "plural": "US dollars",
    "plural_native": "US dollars",
    "symbol": "$",
    "symbol_native": "$",
    "format": {
      "decimal_separator": ".",
      "group_separator": ",",
      "negative": {
        "prefix": "-$",
        "suffix": ""
      },
      "positive": {
        "prefix": "$",
        "suffix": ""
      }
    }
  },
  "location": {
    "continent": {
      "code": "NA",
      "name": "North America"
    },
    "country": {
      "area": 9629091,
      "borders": [
        "CA",
        "MX"
      ],
      "calling_code": "1",
      "capital": "Washington D.C.",
      "code": "US",
      "name": "United States",
      "population": 340110988,
      "population_density": 35.32,
      "flag": {
        "emoji": "🇺🇸",
        "emoji_unicode": "U+1F1FA U+1F1F8",
        "emojitwo": "https://cdn.ipregistry.co/flags/emojitwo/us.svg",
        "noto": "https://cdn.ipregistry.co/flags/noto/us.png",
        "twemoji": "https://cdn.ipregistry.co/flags/twemoji/us.svg",
        "wikimedia": "https://cdn.ipregistry.co/flags/wikimedia/us.svg"
      },
      "languages": [
        {
          "code": "en",
          "name": "English",
          "native": "English"
        },
        {
          "code": "es",
          "name": "Spanish",
          "native": "español"
        },
        {
          "code": "fr",
          "name": "French",
          "native": "français"
        }
      ],
      "tld": ".us"
    },
    "region": {
      "code": "US-VA",
      "name": "Virginia"
    },
    "city": "Ashburn",
    "postal": "20147",
    "latitude": 39.04372,
    "longitude": -77.48748,
    "language": {
      "code": "en",
      "name": "English",
      "native": "English"
    },
    "in_eu": false
  },
  "security": {
    "is_abuser": false,
    "is_attacker": false,
    "is_bogon": false,
    "is_cloud_provider": true,
    "is_proxy": false,
    "is_relay": false,
    "is_tor": false,
    "is_tor_exit": false,
    "is_vpn": false,
    "is_anonymous": false,
    "is_threat": false
  },
  "time_zone": {
    "id": "America/New_York",
    "abbreviation": "EST",
    "current_time": "2025-09-02T11:33:59-04:00",
    "name": "Eastern Standard Time",
    "offset": -14400,
    "in_daylight_saving": true
  }
}
```

----


```json
{
  "ip": "*************",
  "type": "IPv4",
  "hostname": null,
  "carrier": {
    "name": null,
    "mcc": null,
    "mnc": null
  },
  "company": {
    "domain": "onebroadband.in",
    "name": "ONEOTT INTERTAINMENT LIMITED",
    "type": "isp"
  },
  "connection": {
    "asn": 17665,
    "domain": "onebroadband.in",
    "organization": "ONEOTT INTERTAINMENT LIMITED",
    "route": "************/22",
    "type": "isp"
  },
  "currency": {
    "code": "INR",
    "name": "Indian Rupee",
    "name_native": "Indian Rupee",
    "plural": "Indian rupees",
    "plural_native": "Indian rupees",
    "symbol": "₹",
    "symbol_native": "₹",
    "format": {
      "decimal_separator": ".",
      "group_separator": ",",
      "negative": {
        "prefix": "-₹",
        "suffix": ""
      },
      "positive": {
        "prefix": "₹",
        "suffix": ""
      }
    }
  },
  "location": {
    "continent": {
      "code": "AS",
      "name": "Asia"
    },
    "country": {
      "area": 3287590,
      "borders": [
        "BD",
        "BT",
        "CN",
        "MM",
        "NP",
        "PK"
      ],
      "calling_code": "91",
      "capital": "New Delhi",
      "code": "IN",
      "name": "India",
      "population": 1450935791,
      "population_density": 441.34,
      "flag": {
        "emoji": "🇮🇳",
        "emoji_unicode": "U+1F1EE U+1F1F3",
        "emojitwo": "https://cdn.ipregistry.co/flags/emojitwo/in.svg",
        "noto": "https://cdn.ipregistry.co/flags/noto/in.png",
        "twemoji": "https://cdn.ipregistry.co/flags/twemoji/in.svg",
        "wikimedia": "https://cdn.ipregistry.co/flags/wikimedia/in.svg"
      },
      "languages": [
        {
          "code": "en",
          "name": "English",
          "native": "English"
        },
        {
          "code": "hi",
          "name": "Hindi",
          "native": "हिन्दी"
        },
        {
          "code": "bn",
          "name": "Bangla",
          "native": "বাংলা"
        },
        {
          "code": "te",
          "name": "Telugu",
          "native": "తెలుగు"
        },
        {
          "code": "mr",
          "name": "Marathi",
          "native": "मराठी"
        },
        {
          "code": "ta",
          "name": "Tamil",
          "native": "தமிழ்"
        },
        {
          "code": "ur",
          "name": "Urdu",
          "native": "اردو"
        },
        {
          "code": "gu",
          "name": "Gujarati",
          "native": "ગુજરાતી"
        },
        {
          "code": "kn",
          "name": "Kannada",
          "native": "ಕನ್ನಡ"
        },
        {
          "code": "ml",
          "name": "Malayalam",
          "native": "മലയാളം"
        },
        {
          "code": "or",
          "name": "Odia",
          "native": "ଓଡ଼ିଆ"
        },
        {
          "code": "pa",
          "name": "Punjabi",
          "native": "ਪੰਜਾਬੀ"
        },
        {
          "code": "as",
          "name": "Assamese",
          "native": "অসমীয়া"
        },
        {
          "code": "bh",
          "name": "Bhojpuri",
          "native": "Bhojpuri"
        },
        {
          "code": "ks",
          "name": "Kashmiri",
          "native": "کٲشُر"
        },
        {
          "code": "ne",
          "name": "Nepali",
          "native": "नेपाली"
        },
        {
          "code": "sd",
          "name": "Sindhi",
          "native": "سنڌي"
        },
        {
          "code": "sa",
          "name": "Sanskrit",
          "native": "संस्कृत भाषा"
        },
        {
          "code": "fr",
          "name": "French",
          "native": "français"
        }
      ],
      "tld": ".in"
    },
    "region": {
      "code": "IN-DL",
      "name": "Delhi"
    },
    "city": "Delhi",
    "postal": "110006",
    "latitude": 28.65194,
    "longitude": 77.23156,
    "language": {
      "code": "en",
      "name": "English",
      "native": "English"
    },
    "in_eu": false
  },
  "security": {
    "is_abuser": true,
    "is_attacker": false,
    "is_bogon": false,
    "is_cloud_provider": false,
    "is_proxy": false,
    "is_relay": false,
    "is_tor": false,
    "is_tor_exit": false,
    "is_vpn": false,
    "is_anonymous": false,
    "is_threat": true
  },
  "time_zone": {
    "id": "Asia/Kolkata",
    "abbreviation": "IST",
    "current_time": "2025-09-02T21:04:39+05:30",
    "name": "India Standard Time",
    "offset": 19800,
    "in_daylight_saving": false
  }
}
```

