__Dta scrapped on September 2nd, 2025 from the official doc ()__

# Events & Webhooks


At Freemius, we offer a robust **webhooks mechanism** that allows Makers to stay in sync with important platform activities. Webhooks enable real-time communication between Freemius and your backend systems whenever key actions occur—such as a new sale, a license deactivation, or a refund.

This system empowers you to automate workflows, sync data, trigger notifications, and more.

## What is an Event?

An **event** is a specific, predefined action or change that takes place within the Freemius platform.

Events reflect meaningful occurrences in your product’s lifecycle—like a user completing a purchase, a subscription being canceled, or a license being activated. Each event is uniquely named (e.g., `subscription.canceled`, `license.activated`) and carries structured metadata relevant to that action.

Events are the **triggers** that initiate webhook callouts. Whenever a monitored event occurs, <PERSON>mi<PERSON> generates a corresponding callout to your configured endpoint.

## What is a Webhook?

A **webhook** is a URL endpoint that you configure to receive HTTP `POST` requests from <PERSON>mius when certain events occur.  
webhoo  
When an event is triggered, <PERSON><PERSON>us performs a **webhook callout**—sending a signed payload with all relevant event data to your webhook endpoint. This enables your system to programmatically react to the event, such as updating a database, triggering an internal workflow, or notifying your team.

In short:

- The **webhook** is the URL endpoint you provide.
- The **webhook callout** is the actual HTTP request Freemius makes to that endpoint.

## How to create a Webhook?

1. Go to the ***Integrations*** section then choose **Custom Webhooks**
2. Click the **Add Webhook** button.
3. A popup with a form will appear. Add your custom URL that will receive the event callback.
4. Select whether to receive all event types or specific ones on the Callback URL.
5. Choose to immediately make the webhook active or deactivate for a future time.

<video id="hover-video" autoplay="" muted="" loop="" playsinline="" width="100%" style="display: block;"><source src="/wp-content/uploads/2025/05/freemius-create-events-webhook.mp4" type="video/mp4">Your browser does not support the video tag.</video>  

> The Webhook can be edited later to change the url and other options. See all the [available webhooks](https://freemius.com/help/documentation/saas/events-webhooks/#event_types)

## How to Test Event’s Payload?

If you’d like to understand the payload of a specified `event.type` follow these steps:  
1. Head to the **Events Log** section in the dashboard.  
2. Filter the events based on that `event.type`.  
3. Copy the ID of the 1st event shown in the filtered view.  
4. Leverage the API to [fetch the event’s data](https://docs.freemius.com/api/events).  
5. The result’s schema from the API is identical to the payload’s schema you’ll get by the webhook once `event.type` will be processed.

## Types of Events

This is a list of all the types of events we currently send. We may add more at any time, so you shouldn’t rely on only these types existing in your code.

You’ll notice that these events follow a pattern: `resource.event`. Our goal is to design a consistent system that makes things easier to anticipate and code against.

**NOTE:** Events that occur on “sub” resources like `payment.dispute.created` do not trigger the parent’s `update` event.

<table><thead><tr><th>Event</th><th></th></tr></thead><tbody><tr><td colspan="2"><h3>Affiliate Program</h3></td></tr><tr><td><code>affiliate.approved</code></td><td>Occurs whenever an affiliate is approved.</td></tr><tr><td><code>affiliate.blocked</code></td><td>Occurs whenever an affiliate is blocked.</td></tr><tr><td><code>affiliate.created</code></td><td>Occurs whenever an affiliate is created.</td></tr><tr><td><code>affiliate.deleted</code></td><td>Occurs whenever an affiliate is deleted.</td></tr><tr><td><code>affiliate.payout.pending</code></td><td>Occurs once per month when our system calculates affiliate payouts. Triggered only for affiliates eligible for a payout after crossing the $100 threshold.</td></tr><tr><td><code>affiliate.paypal.updated</code></td><td>Occurs whenever there’s an update of an affiliates PayPal details.</td></tr><tr><td><code>affiliate.rejected</code></td><td>Occurs whenever an affiliate is rejected.</td></tr><tr><td><code>affiliate.suspended</code></td><td>Occurs whenever an affiliate is suspended.</td></tr><tr><td><code>affiliate.unapproved</code></td><td>Occurs whenever an affiliate state is changed from approved to pending.</td></tr><tr><td><code>affiliate.updated</code></td><td>Occurs whenever there’s an update of an affiliate details.</td></tr><tr><td colspan="2"><h3>Card</h3></td></tr><tr><td><code>card.created</code></td><td>Occurs whenever a new credit or debit card is added to a user’s account.</td></tr><tr><td><code>card.updated</code></td><td>Occurs whenever a user’s credit or debit card details have been updated.</td></tr><tr><td colspan="2"><h3>Cart</h3></td></tr><tr><td><code>cart.abandoned</code></td><td>Occurs whenever a cart session has been abandoned.</td></tr><tr><td><code>cart.completed</code></td><td>Occurs whenever a ‘sale’ has been completed, but the payment hasn’t necessarily been completed yet (e.g. for trials, 100% coupon codes, or for PayPal subscription which takes 24 to process).</td></tr><tr><td><code>cart.created</code></td><td>Occurs whenever a cart session has been initiated.</td></tr><tr><td><code>cart.recovered</code></td><td>Occurs whenever an abandoned cart session has been recovered and converted into a ‘sale’ (i.e. triggers the cart.completed event).</td></tr><tr><td><code>cart.recovery.deactivated</code></td><td>Occurs when the cart recovery feature has been deactivated in the Developer Dashboard.</td></tr><tr><td><code>cart.recovery.email_1_sent</code></td><td>Occurs when the first cart recovery email has been sent.</td></tr><tr><td><code>cart.recovery.email_2_sent</code></td><td>Occurs when the second cart recovery email has been sent.</td></tr><tr><td><code>cart.recovery.email_3_sent</code></td><td>Occurs when the third (and final) cart recovery email has been sent.</td></tr><tr><td><code>cart.recovery.reactivated</code></td><td>Occurs when the cart recovery feature has been reactivated in the Developer Dashboard.</td></tr><tr><td><code>cart.recovery.subscribed</code></td><td>Occurs when the prospect re-subscribes to the cart abandonment recovery campaign through a link in the cart recovery emails. Or, if a developer of the product reactivates a recovery campaign through the Developer Dashboard.</td></tr><tr><td><code>cart.recovery.unsubscribed</code></td><td>Occurs when the prospect unsubscribes from the cart abandonment recovery campaign through a link in the cart recovery emails. Or, if a developer of the product stops a recovery campaign through the Developer Dashboard.</td></tr><tr><td><code>cart.updated</code></td><td>Occurs when the cart contents have been updated.</td></tr><tr><td colspan="2"><h3>Coupons &amp; Discounts</h3></td></tr><tr><td><code>coupon.created</code></td><td>Occurs whenever a coupon is created.</td></tr><tr><td><code>coupon.deleted</code></td><td>Occurs whenever a coupon is deleted.</td></tr><tr><td><code>coupon.updated</code></td><td>Occurs whenever a coupon is updated.</td></tr><tr><td colspan="2"><h3>Buyer Email Tracking</h3></td></tr><tr><td><code>email.clicked</code></td><td>Occurs when a link is clicked by a user. Email link click tracking is only included in cart abandonment recovery and trial-related emails.</td></tr><tr><td><code>email.opened</code></td><td>Occurs when an email has been opened by a user. Email open tracking is only included in cart abandonment recovery, trial-related, and subscription renewal reminder emails.</td></tr><tr><td colspan="2"><h3>Installation (Downloadable Software)</h3></td></tr><tr><td colspan="2"><p style="padding-left: 10px; margin: 0;">In the context of downloadable software (like plugins or desktop apps), an installation represents a specific copy of your product that has been installed and is running on a particular device or site. The following webhook events let you track changes to each installation—covering its state, version, plan, platform details, and more.</p></td></tr><tr><td><code>install.activated</code></td><td>Occurs whenever a user is opted-in, and after reactivation of a product.</td></tr><tr><td><code>install.deactivated</code></td><td>Occurs whenever a product’s installation is being deactivated.</td></tr><tr><td><code>install.deleted</code></td><td>Occurs whenever a product is being uninstalled.</td></tr><tr><td><code>install.connected</code></td><td>Occurs whenever a user opts in for sharing basic device/website info after previously opting out from it.</td></tr><tr><td><code>install.disconnected</code></td><td>Occurs whenever a user opts out from sharing basic device/website info after previously sharing it.</td></tr><tr><td><code>install.installed</code></td><td>Occurs whenever a product is being installed (triggered before <code>install.activated</code>).</td></tr><tr><td><code>install.language.updated</code></td><td>Occurs whenever an installation language is updated.</td></tr><tr><td><code>install.plan.changed</code></td><td>Occurs whenever an installation plan is changed.</td></tr><tr><td><code>install.plan.downgraded</code></td><td>Occurs whenever an installation plan is downgraded to the default plan (if the default plan is free, then when downgraded back to the free version).</td></tr><tr><td><code>install.platform.version.updated</code></td><td>Occurs whenever an installation platform version is updated (e.g., WP version; iOS version).</td></tr><tr><td><code>install.premium.activated</code></td><td>Occurs whenever the paid product version is activated.</td></tr><tr><td><code>install.premium.deactivated</code></td><td>Occurs whenever the paid product version is deactivated.</td></tr><tr><td><code>install.programming_language.version.updated</code></td><td>Occurs whenever an installation PHP version is updated.</td></tr><tr><td><code>install.sdk.version.updated</code></td><td>Occurs whenever a product installation is updated with a new Freemius SDK version.</td></tr><tr><td><code>install.title.updated</code></td><td>Occurs whenever an installation’s device/site title is updated.</td></tr><tr><td><code>install.trial.cancelled</code></td><td>Occurs when a trial is cancelled.</td></tr><tr><td><code>install.trial.expired</code></td><td>Occurs when a trial is expired.</td></tr><tr><td><code>install.trial_expiring_notice.sent</code></td><td>Occurs when an email has been sent to the user notifying them the trial period is about to end.</td></tr><tr><td><code>install.trial.extended</code></td><td>Occurs whenever a trial is manually extended.</td></tr><tr><td><code>install.trial.plan.updated</code></td><td>Occurs whenever a trial plan is updated.</td></tr><tr><td><code>install.trial.started</code></td><td>Occurs when a free trial is started on an existing product install.</td></tr><tr><td><code>install.uninstalled</code></td><td>Occurs whenever the product is uninstalled.</td></tr><tr><td><code>install.updated</code></td><td>Occurs whenever an installation data is updated (in addition to specific install update events).</td></tr><tr><td><code>install.url.updated</code></td><td>Occurs whenever an installation’s home URL is updated.</td></tr><tr><td><code>install.version.downgrade</code></td><td>Occurs whenever the product is downgraded to a lower version (not related to the plan).</td></tr><tr><td><code>install.version.upgraded</code></td><td>Occurs whenever the product is upgrade to a newer version (not related to the plan).</td></tr><tr><td colspan="2"><h3>Licensing</h3></td></tr><tr><td><code>license.activated</code></td><td>Occurs whenever a license is activated (also triggered automatically after a successful plan upgrade).</td></tr><tr><td><code>license.cancelled</code></td><td>Occurs when a license is cancelled.</td></tr><tr><td><code>license.created</code></td><td>Occurs whenever a license is created.</td></tr><tr><td><code>license.deactivated</code></td><td>Occurs whenever a license is deactivated.</td></tr><tr><td><code>license.deleted</code></td><td>Occurs when a license has been deleted in the Developer Dashboard.</td></tr><tr><td><code>license.expired</code></td><td>Occurs when a license expires.</td></tr><tr><td><code>license.expired_notice.sent</code></td><td>Occurs when a license expired email notice has been sent to the user.</td></tr><tr><td><code>license.extended</code></td><td>Occurs whenever a license is extended by the developer from the dashboard.</td></tr><tr><td><code>license.ownership.changed</code></td><td>Occurs whenever a license ownership is changed by the developer from the dashboard.</td></tr><tr><td><code>license.quota.changed</code></td><td>Occurs whenever a license quota is changed by the developer from the dashboard.</td></tr><tr><td><code>license.renewal_reminder.sent</code></td><td>Occurs when a license renewal reminder email has been sent to the user.</td></tr><tr><td><code>license.shortened</code></td><td>Occurs when a license has been shortened in the User/Developer Dashboard.</td></tr><tr><td><code>license.trial_expiring_notice.sent</code></td><td>Occurs when a courtesy trial expiration reminder email has been sent to the user. For 7-day trials, the email is sent 2 days prior to the trial expiration. For longer trials, the email is sent 7 days prior to the trial expiration.</td></tr><tr><td><code>license.updated</code></td><td>Occurs whenever a license is updated (in addition to specific license update events).</td></tr><tr><td colspan="2"><h3>Payment</h3></td></tr><tr><td><code>payment.created</code></td><td>Occurs whenever a successful payment is created. There’s no dedicated event for subscription renewals. To trigger an action only for renewals, your webhook needs to check the is_renewal flag of the event’s payment object provided with the event’s metadata.</td></tr><tr><td><code>payment.refund</code></td><td>Occurs whenever a payment refund is processed.</td></tr><tr><td><code>payment.dispute.created</code></td><td>Occurs whenever we notified about a payment disputed by a customer.</td></tr><tr><td><code>payment.dispute.closed</code></td><td>Occurs whenever a payment dispute is closed by refunding the disputed payment.</td></tr><tr><td><code>payment.dispute.lost</code></td><td>Occurs whenever a payment dispute is closed in favor of the customer.</td></tr><tr><td><code>payment.dispute.won</code></td><td>Occurs whenever a payment dispute is closed in your/seller’s favor.</td></tr><tr><td colspan="2"><h3>Product Team</h3></td></tr><tr><td><code>member.created</code></td><td>Occurs whenever a developer is added as a team member.</td></tr><tr><td><code>member.deleted</code></td><td>Occurs whenever a member is removed from a team.</td></tr><tr><td><code>member.updated</code></td><td>Occurs whenever a team member role is updated.</td></tr><tr><td colspan="2"><h3>Plans, Pricing and Features</h3></td></tr><tr><td><code>plan.created</code></td><td>Occurs whenever a plan is created.</td></tr><tr><td><code>plan.deleted</code></td><td>Occurs whenever a plan is deleted.</td></tr><tr><td><code>plan.lifetime.purchase</code></td><td>Occurs whenever a user purchases a lifetime package (doesn’t matter which plan).</td></tr><tr><td><code>plan.updated</code></td><td>Occurs whenever a plan details are updated.</td></tr><tr><td><code>pricing.created</code></td><td>Occurs whenever a new pricing is added to a plan.</td></tr><tr><td><code>pricing.deleted</code></td><td>Occurs whenever one of the plan pricing is deleted.</td></tr><tr><td><code>pricing.updated</code></td><td>Occurs whenever a plan pricing is updated.</td></tr><tr><td colspan="2"><h3>Reviews</h3></td></tr><tr><td><code>review.created</code></td><td>Occurs when a new review has been created.</td></tr><tr><td><code>review.deleted</code></td><td>Occurs when a review has been deleted in the Developer Dashboard.</td></tr><tr><td><code>review.requested</code></td><td>Occurs when a new review request email has been sent to the user. This is sent 7 days after purchase to users that haven’t canceled the license yet, were not refunded, and are using the product on at least one website.</td></tr><tr><td><code>review.updated</code></td><td>Occurs when a new review has been updated in the Developer Dashboard.</td></tr><tr><td colspan="2"><h3>Subscription</h3></td></tr><tr><td><code>subscription.cancelled</code></td><td>Occurs whenever a subscription is cancelled.</td></tr><tr><td><code>subscription.created</code></td><td>Occurs whenever a subscription is created.</td></tr><tr><td><code>subscription.renewal_reminder.sent</code></td><td>Occurs whenever an annual renewal reminder email is sent (30 days before the automatic renewal).</td></tr><tr><td><code>subscription.renewal_reminder.opened</code></td><td>Occurs whenever an annual renewal reminder email is opened.</td></tr><tr><td><code>subscription.renewal.failed</code></td><td>Occurs whenever a renewal payment processing is failed .</td></tr><tr><td><code>subscription.renewal.failed.last</code></td><td>Occurs when the latest subscription renewal attempt has failed.</td></tr><tr><td><code>subscription.renewal.failed_email.sent</code></td><td>Occurs whenever a failure renewal processing email is sent to the buyer.</td></tr><tr><td><code>subscription.renewal.retry</code></td><td>Occurs whenever a renewal payment retry is processed.</td></tr><tr><td><code>subscription.renewals.discounted</code></td><td>Occurs whenever a special subscription cancellation promo is applied.</td></tr><tr><td colspan="2"><h3>Store</h3></td></tr><tr><td><code>store.created</code></td><td>Occurs when a new store has been created (e.g. when a new Freemius account has been created).</td></tr><tr><td><code>store.dashboard_url.updated</code></td><td>Occurs when the store’s dashboard URL is updated.</td></tr><tr><td><code>store.plugin.added</code></td><td>Occurs when a product is added to a store.</td></tr><tr><td><code>store.plugin.removed</code></td><td>Occurs when a product is removed from a store.</td></tr><tr><td><code>store.url.updated</code></td><td>Occurs when a store’s URL is updated</td></tr><tr><td colspan="2"><h3>User</h3></td></tr><tr><td><code>user.beta_program.opted_in</code></td><td>Occurs when a user has opted into a product’s beta program.</td></tr><tr><td><code>user.beta_program.opted_out</code></td><td>Occurs when a user has opted out of a product’s beta program.</td></tr><tr><td><code>user.billing.updated</code></td><td>Occurs whenever a customer billing information is updated (e.g. address, VAT ID, company name).</td></tr><tr><td><code>user.billing.tax_id.updated</code></td><td>Occurs whenever the tax ID associated with the user’s billing is changed.</td></tr><tr><td><code>user.card.created</code></td><td>Occurs whenever a new card is added to a user’s account. This is similar to card.created but also logs the connection between a user and a credit card. Different users in the system can be associated with the same credit card (e.g. if a company with multiple people is using the same credit card for all their purchases).</td></tr><tr><td><code>user.created</code></td><td>Occurs whenever a new user is created.</td></tr><tr><td><code>user.email.changed</code></td><td>Occurs whenever a user update their email address.</td></tr><tr><td><code>user.email.verified</code></td><td>Occurs when a user email is verified (usually via email confirmation). If you need to use this event, please contact us <NAME_EMAIL>.</td></tr><tr><td><code>user.email_status.bounced</code></td><td>Occurs when a transactional email sent to a user bounces, which also changes the user’s email_status property to 'bounced'.</td></tr><tr><td><code>user.email_status.delivered</code></td><td>Occurs when a transactional email sent to a user is successfully delivered and only if the user’s previous deliverability state (aka the email_status property) was not empty before. I.e., this event is only triggered after a previous email has bounced or dropped.</td></tr><tr><td><code>user.email_status.dropped</code></td><td>Occurs when a transactional email sent to a user is dropped, which also changes the user’s email_status property to 'dropped'.</td></tr><tr><td><code>user.marketing.opted_in</code></td><td>Occurs when a user has opted to receive marketing material (emails). This event only occurs after a change in the marketing opt-in state and is not triggered when a user is created.</td></tr><tr><td><code>user.marketing.opted_out</code></td><td>Occurs when a user has opted out of a receiving marketing material (emails). This event only occurs after a change in the marketing opt-in state and is not triggered when a user is created.</td></tr><tr><td><code>user.marketing.reset</code></td><td>Occurs when a user’s marketing status has been reset.</td></tr><tr><td><code>user.name.changed</code></td><td>Occurs whenever a user update their name.</td></tr><tr><td><code>user.support.contacted</code></td><td>Occurs when a user submits a support ticket via the contact form in the Customer Portal.</td></tr><tr><td><code>user.trial.started</code></td><td>Occurs when a user registers for a trial.</td></tr><tr><td colspan="2"><h3>Webhook</h3></td></tr><tr><td><code>webhook.created</code></td><td>Occurs whenever a webhook is created.</td></tr><tr><td><code>webhook.deleted</code></td><td>Occurs whenever a webhook is deleted.</td></tr><tr><td><code>webhook.updated</code></td><td>Occurs whenever a webhook is updated.</td></tr><tr><td colspan="2"><h3>WordPress</h3></td></tr><tr><td><code>addon.free.downloaded</code></td><td>Occurs whenever a free version of an add-on is downloaded.</td></tr><tr><td><code>addon.premium.downloaded</code></td><td>Occurs whenever a premium version of an add-on is downloaded.</td></tr><tr><td><code>install.extensions.opt_in</code></td><td>Occurs whenever a user opts in for sharing a website’s plugins &amp; themes list after previously opting out from it.</td></tr><tr><td><code>install.extensions.opt_out</code></td><td>Occurs whenever a user opt-out from sharing a website’s plugins &amp; themes list after previously sharing it.</td></tr><tr><td><code>install.ownership.candidate.confirmed</code></td><td>Occurs whenever an account ownership transfer candidate confirms the transfer.</td></tr><tr><td><code>install.ownership.completed</code></td><td>Occurs whenever an account ownership transfer is complete.</td></tr><tr><td><code>install.ownership.initiated</code></td><td>Occurs whenever an account ownership transfer is initiated.</td></tr><tr><td><code>install.ownership.owner.confirmed</code></td><td>Occurs whenever an account ownership transfer is confirmed by the current installation account owner.</td></tr><tr><td><code>install.site.opt_in</code></td><td>Occurs whenever a user opts in for sharing basic website after previously opting out from it.</td></tr><tr><td><code>install.site.opt_out</code></td><td>Occurs whenever a user opts out from sharing basic website after previously sharing it.</td></tr><tr><td><code>install.user.opt_in</code></td><td>Occurs whenever a user opts in for sharing their basic profile info after previously opting out from it.</td></tr><tr><td><code>install.user.opt_out</code></td><td>Occurs whenever a user opts out from sharing their basic profile info after previously sharing it.</td></tr><tr><td><code>license.site.blacklisted</code></td><td>Occurs when a site has been blacklisted in the Customer Portal.</td></tr><tr><td><code>license.blacklisted_site.deleted</code></td><td>Occurs when a blacklisted site has been removed in the Customer Portal.</td></tr><tr><td><code>license.site.whitelisted</code></td><td>Occurs when a site has been whitelisted in the Customer Portal.</td></tr><tr><td><code>license.whitelisted_site.deleted</code></td><td>Occurs when a whitelisted site has been removed in the Customer Portal.</td></tr><tr><td><code>plugin.free.downloaded</code></td><td>Occurs whenever a free product version is downloaded.</td></tr><tr><td><code>plugin.premium.downloaded</code></td><td>Occurs whenever a paid product version is downloaded.</td></tr><tr><td><code>plugin.version.deleted</code></td><td>Occurs whenever a deployed version is deleted.</td></tr><tr><td><code>plugin.version.deployed</code></td><td>Occurs whenever a new product version is deployed to Freemius.</td></tr><tr><td><code>plugin.version.released</code></td><td>Occurs whenever a version is set as released.</td></tr><tr><td><code>plugin.version.beta.released</code></td><td>Occurs whenever a version is released as beta.</td></tr><tr><td><code>plugin.version.release.suspended</code></td><td>Occurs whenever a deployment release is suspended.</td></tr><tr><td><code>plugin.version.updated</code></td><td>Occurs whenever an existing version is re-deployed to Freemius.</td></tr><tr><td><code>pricing.visit</code></td><td>Occurs when a user has visited the pricing table via the WordPress admin or popup checkout modal.</td></tr></tbody></table>

Last updated on August 3, 2025
