Here is the official sample of Webhook listener:

__scrapped on September 2nd, 2025__

https://freemius.com/help/documentation/saas/saas-integration/

```php
<?php
    // Retrieve the request's body payload.
    $input     = @file_get_contents("php://input");
    $hash      = hash_hmac('sha256', $input, '<PRODUCT_SECRET_KEY>');
    $signature = $_SERVER['HTTP_X_SIGNATURE'] ?? '';
 
    if ( ! hash_equals($hash, $signature))
    {
        // Invalid signature, don't expose any data to attackers.
        http_response_code(200);
        exit;
    }
 
    $event_json = json_decode($input);
 
    function loadUser($fsUser)
    {
        $user = loadUserByExternalId($fsUser->id);
 
        if (is_object($user))
        {
            // A matching user already exists in the DB.
            return $user;
        }
        else
        {
            /**
             * There's no user matching the Freemius user ID, so we need to link locate the local user by email and link it to the corresponding Freemius user ID.
             */
            $user = loadUserByEmail($fsUser->email); // You'll need to implement `loadUserByEmail($email)`
 
            // Link local user with Freemius's user ID.
            $user->external_id = $fsUser->id;
            $user->update();
        }
 
        return $user;
    }
 
    function getPlanID($fsPlanId)
    {
        $plansMap = [
            // Freemius plan ID => Your plan ID
            '678' => '1',
            '789' => '2',
        ];
 
        return $plansMap[$fsPlanId];
    }
 
    function createLicense($user, $fsLicense)
    {
        return storeLicense([
            'user_id'      => $user->id,
            'plan_id'      => getPlanID($fsLicense->plan_id),
                        // Using the license id you'll be able to generate checkout links for plan changes and payment method updates.
            'external_id'  => $fsLicense->id,
            'expiration'   => $fsLicense->expiration,
        ]);
    }
 
    function setUserPlan($fsUser, $fsLicense)
    {
        $user = loadUser($fsUser);
        return createLicense($user, $fsLicense);
    }
 
    function updateLicenseExpiration($fsUser, $fsLicense)
    {
        $license = loadLicenseByExternalId($fsLicense->id);
 
        if ( ! is_object($license))
        {
            // Likely the 'license.created' event failed processing.
            setUserPlan($fsUser, $fsLicense);
        }
        else
        {
            $license->expiration = $fsLicense->expiration;
            $license->update();
        }
 
        return $license;
    }
 
    function updateUserPlan($fsUser, $fsLicense)
    {
        if ( ! is_object($license))
        {
            // Likely the 'license.created' event failed processing.
            setUserPlan($fsUser, $fsLicense);
        }
        else
        {
            $license->plan_id = getPlanID($fsLicense->plan_id);
            $license->update();
        }
 
        return $license;
    }
 
    function downgradeUserPlan($fsUser, $fsLicense)
    {
        return updateLicenseExpiration($fsUser, $fsLicense);
    }
 
    switch ($fs_event->type)
    {
        case 'license.created':
 
            setUserPlan(
                $fs_event->objects->user,
                $fs_event->objects->license
            );
            break;
 
        case 'license.plan.changed':
            updateUserPlan(
                $fs_event->objects->user,
                $fs_event->objects->license
            );
            break;
 
        case 'license.extended':
        case 'license.shortened':
            updateLicenseExpiration(
                $fs_event->objects->user,
                $fs_event->objects->license
            );
            break;
 
        case 'license.expired':
            downgradeLicenseByExternalId($fs_event->objects->license->id);
            break;
        case 'license.deleted':
        case 'license.cancelled':
            cancelLicneseByExternalId($fs_event->objects->license->id);
            break;
    }
 
    http_response_code(200);
```