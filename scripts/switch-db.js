#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const provider = process.argv[2];

if (!provider || !['sqlite', 'postgresql', 'mysql'].includes(provider)) {
    console.error('Usage: node scripts/switch-db.js <sqlite|postgresql|mysql>');
    process.exit(1);
}

const schemaPath = path.join(__dirname, '..', 'prisma', 'schema.prisma');
const sourceSchemaPath = provider === 'sqlite'
    ? schemaPath
    : path.join(__dirname, '..', 'prisma', `schema.${provider}.prisma`);

if (provider !== 'sqlite') {
    if (!fs.existsSync(sourceSchemaPath)) {
        console.error(`Schema file for ${provider} not found: ${sourceSchemaPath}`);
        process.exit(1);
    }

    // Copy the provider-specific schema to schema.prisma
    fs.copyFileSync(sourceSchemaPath, schemaPath);
}

console.log(`✅ Switched to ${provider} database provider`);
console.log('📝 Don\'t forget to update your DATABASE_URL in .env file');
console.log('🔄 Run "npm run db:generate" to regenerate the Prisma client');