# Requirements Document

## Introduction

This specification addresses the critical realignment of the GuardGeo API platform to properly separate backend and frontend concerns, connect features to the backend, and complete missing functionality. The platform serves as a private, proprietary backend system for the GuardGeo WordPress plugin and authorized administrators, providing IP intelligence services integrated with Freemius and ipRegistry.

The current implementation has existing Node.js/Express backend services and React/TypeScript frontend components, but many features are not properly connected and security validations need to be moved entirely to the backend. This realignment will ensure proper separation of concerns while maintaining the existing technology stack and ensuring all logging uses Sentry.io instead of console methods.

## Requirements

### Requirement 1: Backend Architecture Realignment

**User Story:** As a system architect, I want the platform to properly separate backend and frontend concerns using the existing Node.js/Express backend, so that all business logic and security validations occur server-side while maintaining the current technology stack.

#### Acceptance Criteria

1. WHEN the platform is deployed THEN the backend SHALL continue using Node.js/Express with TypeScript
2. WHEN frontend components are accessed THEN they SHALL properly connect to backend APIs for all data operations
3. WHEN API endpoints are called THEN they SHALL follow the documented routing conventions (/admin/*, /api/v1/*, /api/v1/webhooks/*)
4. WHEN authentication occurs THEN it SHALL use the existing session management system with proper backend validation
5. WHEN frontend features are used THEN they SHALL be properly connected to corresponding backend services

### Requirement 2: Backend Security Validation

**User Story:** As a security administrator, I want all security validations to occur on the backend, so that the platform maintains proper security boundaries and cannot be bypassed by client-side manipulation.

#### Acceptance Criteria

1. WHEN a user attempts authentication THEN all validation SHALL occur on the Express backend using existing middleware
2. WHEN API requests are made THEN rate limiting SHALL be enforced server-side using the existing rate limiting middleware
3. WHEN user permissions are checked THEN authorization SHALL be validated on the backend before any data access
4. WHEN CSRF protection is needed THEN the existing CSRF middleware SHALL be properly implemented
5. WHEN session management occurs THEN it SHALL use the existing Express session handling with proper security headers

### Requirement 3: Freemius Integration Backend Services

**User Story:** As a platform administrator, I want complete Freemius integration with proper data mapping, so that all product and installation data is accurately stored and synchronized.

#### Acceptance Criteria

1. WHEN Freemius API is called THEN the system SHALL use the existing FreemiusService with proper error handling and Sentry logging
2. WHEN product data is retrieved THEN it SHALL be mapped to the FreemiusProduct Prisma model with all documented fields from the API specification
3. WHEN installation data is synced THEN it SHALL be stored in FreemiusInstallation model with complete field mapping including all status flags
4. WHEN webhook events are received THEN they SHALL be processed and stored in FreemiusEvent model with proper validation
5. WHEN data synchronization occurs THEN it SHALL update database records based on timestamp freshness without external caching systems

### Requirement 4: IP Intelligence Backend Services

**User Story:** As a security analyst, I want comprehensive IP intelligence analysis with database-based freshness tracking, so that IP threats are accurately identified and performance is optimized.

#### Acceptance Criteria

1. WHEN IP analysis is requested THEN the system SHALL check the IpRegistryData table for data less than 2 days old based on last_refreshed_at timestamp
2. WHEN fresh data is not available THEN the system SHALL fetch from ipRegistry API using the existing IpIntelligenceService and store complete response
3. WHEN risk scoring occurs THEN it SHALL calculate based on documented threat indicators and security flags using the existing calculateRiskScore method
4. WHEN analysis results are returned THEN they SHALL include location, network, security, and metadata information as defined in IpAnalysisResult interface
5. WHEN IP data cleanup is needed THEN the system SHALL remove records where data_expires_at is past current timestamp

### Requirement 5: Admin Web Interface Backend Integration

**User Story:** As an administrator, I want the existing React frontend components to be properly connected to backend services, so that all admin features work correctly with proper data flow and security.

#### Acceptance Criteria

1. WHEN accessing admin routes THEN the React components SHALL properly fetch data from backend API endpoints
2. WHEN viewing dashboards THEN they SHALL display real-time API usage statistics, system logs, and status information from backend services
3. WHEN searching logs THEN the interface SHALL use backend filtering and provide export capabilities through API endpoints
4. WHEN managing IP data THEN administrators SHALL be able to trigger manual refreshes and view analysis results via backend services
5. WHEN performing user management THEN Super Admins SHALL be able to create and manage other admin accounts through proper backend validation

### Requirement 6: Comprehensive Logging with Sentry Integration

**User Story:** As a system administrator, I want all application events logged to Sentry.io instead of console, so that I have centralized monitoring and alerting for production issues.

#### Acceptance Criteria

1. WHEN any error occurs THEN it SHALL be logged to Sentry using the configured DSN
2. WHEN API requests are made THEN they SHALL be logged with request/response details to Sentry
3. WHEN security events occur THEN they SHALL be captured in Sentry with appropriate severity levels
4. WHEN performance issues are detected THEN they SHALL trigger Sentry alerts
5. WHEN console.log statements exist THEN they SHALL be replaced with appropriate Sentry logging calls

### Requirement 7: Database Schema Completion

**User Story:** As a data architect, I want the database schema to completely map all Freemius and ipRegistry entities, so that no data is lost and all relationships are properly maintained.

#### Acceptance Criteria

1. WHEN Freemius entities are stored THEN all fields from the API documentation SHALL be mapped to Prisma model columns in the existing schema
2. WHEN ipRegistry data is stored THEN the complete API response SHALL be preserved in the raw_response JSON field and mapped fields updated
3. WHEN relationships exist THEN they SHALL be properly defined using Prisma relationship syntax
4. WHEN schema changes are needed THEN they SHALL be implemented using Prisma migrations
5. WHEN schema updates occur THEN they SHALL maintain data integrity and include proper indexes as defined in the current schema

### Requirement 8: API Endpoint Implementation

**User Story:** As a WordPress plugin developer, I want the /api/v1/analyze endpoint to work correctly with proper validation, so that the GuardGeo plugin can successfully analyze visitor IPs.

#### Acceptance Criteria

1. WHEN the analyze endpoint receives a request THEN it SHALL validate all required parameters (ip, visitor_hash, plugin_id, install_id, url)
2. WHEN validation passes THEN it SHALL verify the installation against Freemius API or cached data
3. WHEN installation is valid THEN it SHALL perform IP analysis and return structured results
4. WHEN private/reserved IPs are submitted THEN the request SHALL be rejected with appropriate error message
5. WHEN duplicate requests occur within 24 hours THEN they SHALL be rejected to prevent abuse

### Requirement 9: Webhook Processing System

**User Story:** As a system integrator, I want Freemius webhooks to be processed reliably, so that real-time updates are handled efficiently and stored in the database.

#### Acceptance Criteria

1. WHEN webhook requests are received THEN they SHALL validate HMAC signature using the existing validateWebhookSignature method
2. WHEN signature validation passes THEN the event SHALL be processed immediately and stored in the FreemiusEvent table
3. WHEN events are processed THEN they SHALL update relevant database records based on event type and data
4. WHEN processing fails THEN the system SHALL log failures to Sentry and return appropriate error responses
5. WHEN webhook processing completes THEN it SHALL return appropriate HTTP status codes to Freemius and log success to Sentry

### Requirement 10: Role-Based Access Control

**User Story:** As a security administrator, I want proper role-based access control using the existing middleware system, so that users can only access features appropriate to their role level.

#### Acceptance Criteria

1. WHEN users authenticate THEN their role SHALL determine accessible routes and features using existing authorization middleware
2. WHEN Super Admin users log in THEN they SHALL have access to all platform features including user management through existing protect.superAdminOnly() middleware
3. WHEN Dev users access the system THEN they SHALL have access to technical and development-related features as defined in existing role permissions
4. WHEN Marketing/Sales users log in THEN they SHALL have access only to analytics and reporting features based on their UserRole enum value
5. WHEN unauthorized access is attempted THEN the system SHALL return 403 responses and log the attempt using existing security logging