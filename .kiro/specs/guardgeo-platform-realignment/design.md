# Design Document

## Overview

This design document outlines the architectural approach for realigning the GuardGeo API platform to properly separate backend and frontend concerns while maintaining the existing Node.js/Express backend and React/TypeScript frontend. The design ensures all security validations occur on the backend, implements comprehensive Sentry.io logging, and completes the integration between existing services and frontend components.

The platform serves as a simple proxy between ipRegistry and the GuardGeo WordPress plugin, with an admin interface for monitoring and management. The platform does NOT make decisions about IP threats - it simply returns the complete ipRegistry data to the WordPress plugin, which makes all security decisions.

## Architecture

### High-Level Architecture

```mermaid
---
config:
  layout: elk
---
flowchart TB
 subgraph subGraph0["Frontend Layer"]
        A["React/TypeScript Components"]
        B["Admin Dashboard"]
        C["User Management UI"]
        D["IP Analysis UI"]
        E["System Monitoring UI"]
  end
 subgraph subGraph1["Backend Layer"]
        F["Express.js Server"]
        G["Authentication Middleware"]
        H["Authorization Middleware"]
        I["Rate Limiting Middleware"]
        J["CSRF Protection"]
        K["Security Logging"]
  end
 subgraph subGraph2["Service Layer"]
        L["FreemiusService"]
        M["IpIntelligenceService"]
        N["UserService"]
        O["AuditService"]
        P["SecurityService"]
  end
 subgraph subGraph3["Data Layer"]
        Q["Prisma ORM"]
        R["SQLite/PostgreSQL/MySQL"]
  end
 subgraph subGraph4["External APIs"]
        S["Freemius API"]
        T["ipRegistry API"]
        U["Sentry.io"]
  end
 subgraph subGraph5["WordPress Plugin"]
        V["GuardGeo Plugin"]
  end
    A --> F
    B --> F
    C --> F
    D --> F
    E --> F
    F --> G & H & I & J & K
    G --> L
    H --> M
    I --> N
    J --> O
    K --> P & U
    L --> Q & S
    M --> Q & T
    N --> Q
    O --> Q
    P --> Q
    Q --> R
    V --> F

```

### Backend Architecture Details

The backend follows a layered architecture pattern with clear separation of concerns:

1. **API Layer**: Express.js routes handling HTTP requests/responses
2. **Middleware Layer**: Authentication, authorization, rate limiting, CSRF protection
3. **Service Layer**: Business logic encapsulated in service classes
4. **Data Access Layer**: Prisma ORM for database operations
5. **External Integration Layer**: API clients for Freemius and ipRegistry

### Frontend Architecture Details

The frontend maintains the existing React/TypeScript structure but ensures proper backend integration:

1. **Component Layer**: React components for UI rendering
2. **Service Layer**: API client services for backend communication
3. **State Management**: React Context for authentication and global state
4. **Routing Layer**: React Router for navigation and route protection

## Components and Interfaces

### Core Backend Services

#### FreemiusService Enhancement
```typescript
interface FreemiusServiceInterface {
  // Product Management
  getProduct(fields?: string[]): Promise<FreemiusProduct | null>
  syncProductData(): Promise<SyncResult>
  
  // Installation Management
  getInstallation(installId: string, fields?: string[]): Promise<FreemiusInstallation | null>
  getAllInstallations(filters?: InstallationFilters): Promise<SyncResult>
  validateInstallation(installId: string): Promise<ValidationResult>
  
  // Webhook Processing
  validateWebhookSignature(payload: string, signature: string): WebhookValidationResult
  processWebhookEvent(event: FreemiusWebhookEvent): Promise<ProcessingResult>
  
  // Analytics and Reporting
  getStats(): Promise<FreemiusStats>
  getEvents(filters?: EventFilters): Promise<FreemiusEvent[]>
}
```

#### IpIntelligenceService Enhancement
```typescript
interface IpIntelligenceServiceInterface {
  // IP Data Retrieval (Proxy functionality)
  getIpData(ip: string, context?: RequestContext): Promise<IpRegistryData>
  
  // Data Management
  refreshIpData(ip: string): Promise<IpRegistryData>
  getStoredData(ip: string): Promise<IpRegistryData | null>
  isDataFresh(ip: string): Promise<boolean>
  cleanupExpiredData(): Promise<number>
  
  // Request Logging
  logRequest(ip: string, requestData: any, responseData: any): Promise<void>
  
  // Validation
  isValidPublicIp(ip: string): boolean
  isPrivateOrReservedIp(ip: string): boolean
}
```

#### Enhanced API Endpoints

##### WordPress Plugin API
```typescript
// POST /api/v1/analyze
interface AnalyzeRequest {
  ip: string;
  visitor_hash: string;
  plugin_id: number;
  install_id: number;
  url: string;
}

interface AnalyzeResponse {
  success: boolean;
  data?: IpRegistryApiResponse; // Complete ipRegistry response
  error?: string;
  request_body: {
    ip: string;
    visitor_hash: string;
    plugin_id: number;
    install_id: number;
    url: string;
    }
  request_id: string;
}
```

##### Admin Web Interface APIs
```typescript
// GET /api/v1/admin/dashboard
interface DashboardResponse {
  success: boolean;
  data: {
    stats: {
      totalRequests: number;
      activeInstallations: number;
      revenue: number;
    };
    recentActivity: ActivityLog[];
    systemHealth: HealthStatus;
  };
}

// GET /api/v1/admin/freemius/installations
interface InstallationsResponse {
  success: boolean;
  data: {
    installations: FreemiusInstallation[];
    pagination: PaginationInfo;
    filters: FilterOptions;
  };
}

// GET /api/v1/admin/ip-requests/history
interface IpRequestHistoryResponse {
  success: boolean;
  data: {
    requests: IpAnalysisRequest[];
    pagination: PaginationInfo;
  };
}
```

### Frontend Component Architecture

#### Enhanced Page Components
```typescript
// Dashboard Page
interface DashboardPageProps {
  user: AuthenticatedUser;
}

interface DashboardState {
  stats: DashboardStats;
  recentActivity: ActivityLog[];
  loading: boolean;
  error: string | null;
}

// Freemius Management Pages
interface FreemiusInstallationsPageProps {
  user: AuthenticatedUser;
}

interface FreemiusInstallationsState {
  installations: FreemiusInstallation[];
  filters: InstallationFilters;
  pagination: PaginationState;
  loading: boolean;
}

// IP Request History Pages
interface IpRequestHistoryPageProps {
  user: AuthenticatedUser;
}

interface IpRequestHistoryState {
  requestHistory: IpAnalysisRequest[];
  filters: RequestFilters;
  pagination: PaginationState;
  loading: boolean;
}
```

#### API Client Services
```typescript
interface ApiClientInterface {
  // Authentication
  login(credentials: LoginCredentials): Promise<AuthResponse>
  logout(): Promise<void>
  refreshSession(): Promise<AuthResponse>
  
  // Dashboard
  getDashboardData(): Promise<DashboardResponse>
  
  // Freemius Management
  getInstallations(filters?: InstallationFilters): Promise<InstallationsResponse>
  syncFreemiusData(): Promise<SyncResponse>
  getFreemiusStats(): Promise<StatsResponse>
  
  // IP Data Requests
  requestIpData(ip: string): Promise<AnalyzeResponse>
  getRequestHistory(filters?: RequestFilters): Promise<IpRequestHistoryResponse>
  manualIpLookup(ip: string): Promise<IpDataResponse>
  
  // User Management
  getUsers(): Promise<UsersResponse>
  createUser(userData: CreateUserRequest): Promise<UserResponse>
  updateUser(userId: string, userData: UpdateUserRequest): Promise<UserResponse>
  
  // System Management
  getSystemHealth(): Promise<HealthResponse>
  getAuditLogs(filters?: AuditFilters): Promise<AuditLogsResponse>
}
```

## Data Models

### Enhanced Prisma Schema Updates

The existing Prisma schema already contains most required models, but needs enhancements for complete Freemius and ipRegistry mapping:

#### Freemius Models Enhancement
```prisma
model FreemiusProduct {
  // Add missing fields from API specification
  refund_policy            String?  // "flexible" | "moderate" | "strict"
  renewals_discount_type   String?  // "percentage" | "dollar"
  
  // Enhanced tracking
  api_last_synced_at       DateTime?
  api_sync_errors          Json?    // Store sync error details
  
  // Existing fields remain unchanged
}

model FreemiusInstallation {
  // Add missing fields from API specification
  programming_language_version String?
  last_served_update_version   String?
  
  // Enhanced validation tracking
  validation_status        String   @default("pending") // "valid" | "invalid" | "pending"
  last_validated_at        DateTime?
  validation_errors        Json?
  
  // Existing fields remain unchanged
}

model FreemiusEvent {
  // Enhanced processing tracking
  retry_count             Int      @default(0)
  retry_errors            Json?
  
  // Existing fields remain unchanged
}
```

#### IP Intelligence Models Enhancement
```prisma
model IpRegistryData {
  // Enhanced geolocation from ipRegistry
  continent_code          String?
  continent_name          String?
  
  // ISP details from ipRegistry
  isp_domain              String?
  route_prefix            String?
  
  // Store complete ipRegistry response
  raw_response            Json     // Complete API response
  
  // Existing fields remain unchanged
}

model IpAnalysisRequest {
  // Enhanced request tracking
  client_ip               String?  // IP of the requesting client
  request_headers         Json?    // Store relevant headers
  response_time_ms        Int?     // Total response time
  
  // Store request and response data
  request_body            Json?    // Original request from plugin
  response_body           Json?    // Response sent to plugin
  
  // Existing fields remain unchanged
}
```

### New Models for Enhanced Functionality

#### System Configuration Model
```prisma
model SystemConfiguration {
  id                      String   @id @default(cuid())
  key                     String   @unique
  value                   Json
  category                String   // "freemius" | "ipregistry" | "security" | "general"
  description             String?
  is_sensitive            Boolean  @default(false)
  requires_restart        Boolean  @default(false)
  
  created_at              DateTime @default(now())
  updated_at              DateTime @updatedAt
  updated_by              String?
  
  @@map("system_configuration")
}
```

#### API Request Logging Model
```prisma
model ApiRequestLog {
  id                      String   @id @default(cuid())
  method                  String
  url                     String
  status_code             Int
  response_time_ms        Int
  request_size_bytes      Int?
  response_size_bytes     Int?
  
  user_id                 String?
  ip_address              String
  user_agent              String?
  
  request_headers         Json?
  request_body            Json?
  response_headers        Json?
  response_body           Json?
  
  error_message           String?
  
  created_at              DateTime @default(now())
  
  @@index([created_at(sort: Desc), status_code])
  @@index([user_id, created_at(sort: Desc)])
  @@map("api_request_logs")
}
```

## Session Management

### Database-Based Session Storage

The platform uses database-based session storage with the existing UserSession model in Prisma:

```typescript
// Session Configuration
interface SessionConfig {
  secret: string;
  resave: boolean;
  saveUninitialized: boolean;
  cookie: {
    secure: boolean;
    httpOnly: boolean;
    maxAge: number;
    sameSite: 'strict' | 'lax' | 'none';
  };
  store: PrismaSessionStore;
}

// Session Store Implementation
class PrismaSessionStore {
  async get(sessionId: string): Promise<SessionData | null> {
    const session = await prisma.userSession.findUnique({
      where: { session_token: sessionId, is_active: true },
      include: { user: true }
    });
    
    if (!session || session.expires_at < new Date()) {
      return null;
    }
    
    return {
      userId: session.user_id,
      user: session.user,
      lastActivity: session.last_activity
    };
  }
  
  async set(sessionId: string, sessionData: SessionData): Promise<void> {
    await prisma.userSession.upsert({
      where: { session_token: sessionId },
      update: {
        last_activity: new Date(),
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      },
      create: {
        session_token: sessionId,
        user_id: sessionData.userId,
        ip_address: sessionData.ipAddress,
        user_agent: sessionData.userAgent,
        expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000),
        is_active: true
      }
    });
  }
  
  async destroy(sessionId: string): Promise<void> {
    await prisma.userSession.updateMany({
      where: { session_token: sessionId },
      data: { is_active: false }
    });
  }
}
```

### Session Validation and Security

```typescript
// Session Validation Middleware
interface SessionValidationResult {
  isValid: boolean;
  user?: User;
  session?: UserSession;
  error?: string;
}

class SessionValidator {
  static async validateSession(sessionToken: string, ipAddress: string): Promise<SessionValidationResult> {
    if (!sessionToken) {
      return { isValid: false, error: 'No session token provided' };
    }
    
    const session = await prisma.userSession.findUnique({
      where: { session_token: sessionToken, is_active: true },
      include: { user: true }
    });
    
    if (!session) {
      return { isValid: false, error: 'Session not found' };
    }
    
    if (session.expires_at < new Date()) {
      await this.destroySession(sessionToken);
      return { isValid: false, error: 'Session expired' };
    }
    
    // IP validation (optional - can be disabled for mobile users)
    if (session.ip_address !== ipAddress) {
      await this.logSecurityEvent('session_ip_mismatch', session.user_id, ipAddress);
      // Could either destroy session or just log - depends on security requirements
    }
    
    // Update last activity
    await prisma.userSession.update({
      where: { id: session.id },
      data: { last_activity: new Date() }
    });
    
    return {
      isValid: true,
      user: session.user,
      session
    };
  }
  
  static async destroySession(sessionToken: string): Promise<void> {
    await prisma.userSession.updateMany({
      where: { session_token: sessionToken },
      data: { is_active: false }
    });
  }
}
```

### Country Detection Integration

Using country.is API for enhanced geolocation when needed:

```typescript
interface CountryService {
  getCountryByIp(ip: string): Promise<CountryInfo | null>;
}

class CountryService implements CountryService {
  private readonly apiUrl = 'https://api.country.is';
  
  async getCountryByIp(ip: string): Promise<CountryInfo | null> {
    try {
      if (this.isPrivateIp(ip)) {
        return null;
      }
      
      const response = await fetch(`${this.apiUrl}/${ip}`);
      if (!response.ok) {
        throw new Error(`Country API error: ${response.status}`);
      }
      
      const data = await response.json();
      return {
        country: data.country || null,
        ip: data.ip || ip
      };
    } catch (error) {
      Sentry.captureException(error, {
        tags: { service: 'country-detection' },
        extra: { ip }
      });
      return null;
    }
  }
  
  private isPrivateIp(ip: string): boolean {
    // Check for private IP ranges
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[01])\./,
      /^192\.168\./,
      /^127\./,
      /^169\.254\./,
      /^::1$/,
      /^fc00:/,
      /^fe80:/
    ];
    
    return privateRanges.some(range => range.test(ip));
  }
}
```

## Error Handling

### Centralized Error Handling Strategy

#### Backend Error Handling
```typescript
interface ApiError {
  code: string;
  message: string;
  details?: any;
  statusCode: number;
  timestamp: string;
  requestId: string;
}

class ErrorHandler {
  static handleApiError(error: Error, req: Request, res: Response): void {
    const apiError: ApiError = {
      code: this.getErrorCode(error),
      message: this.getErrorMessage(error),
      details: this.getErrorDetails(error),
      statusCode: this.getStatusCode(error),
      timestamp: new Date().toISOString(),
      requestId: req.headers['x-request-id'] as string || 'unknown'
    };
    
    // Log to Sentry
    Sentry.captureException(error, {
      tags: {
        component: 'api',
        endpoint: req.path,
        method: req.method
      },
      extra: {
        requestId: apiError.requestId,
        userId: req.user?.id,
        ipAddress: req.ip
      }
    });
    
    res.status(apiError.statusCode).json({
      success: false,
      error: apiError
    });
  }
}
```

#### Frontend Error Handling
```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class GlobalErrorBoundary extends Component<Props, ErrorBoundaryState> {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to Sentry
    Sentry.captureException(error, {
      contexts: {
        react: {
          componentStack: errorInfo.componentStack
        }
      }
    });
    
    this.setState({
      hasError: true,
      error,
      errorInfo
    });
  }
}
```

### Sentry Integration Strategy

#### Backend Sentry Configuration
```typescript
import * as Sentry from '@sentry/node';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Express({ app }),
  ],
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  beforeSend(event) {
    // Filter sensitive data
    if (event.request?.data) {
      event.request.data = filterSensitiveData(event.request.data);
    }
    return event;
  }
});
```

#### Replace Console Logging
```typescript
// Replace all console.log, console.error, console.warn with Sentry
class Logger {
  static info(message: string, extra?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.log(`[INFO] ${message}`, extra);
    }
    Sentry.addBreadcrumb({
      message,
      level: 'info',
      data: extra
    });
  }
  
  static error(message: string, error?: Error, extra?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.error(`[ERROR] ${message}`, error, extra);
    }
    Sentry.captureException(error || new Error(message), {
      tags: { level: 'error' },
      extra
    });
  }
  
  static warn(message: string, extra?: any): void {
    if (process.env.NODE_ENV === 'development') {
      console.warn(`[WARN] ${message}`, extra);
    }
    Sentry.captureMessage(message, 'warning');
    Sentry.addBreadcrumb({
      message,
      level: 'warning',
      data: extra
    });
  }
}

// IP Validation Utilities
class IpValidator {
  static isValidIp(ip: string): boolean {
    if (!ip || typeof ip !== 'string') return false;
    
    // IPv4 regex
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    // IPv6 regex (simplified)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$/;
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip);
  }
  
  static isPrivateOrReservedIp(ip: string): boolean {
    if (!this.isValidIp(ip)) return true;
    
    const privateRanges = [
      /^10\./,                    // 10.0.0.0/8
      /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
      /^192\.168\./,              // ***********/16
      /^127\./,                   // *********/8 (loopback)
      /^169\.254\./,              // ***********/16 (link-local)
      /^0\./,                     // 0.0.0.0/8
      /^224\./,                   // *********/4 (multicast)
      /^240\./,                   // 240.0.0.0/4 (reserved)
      /^::1$/,                    // IPv6 loopback
      /^fc00:/,                   // IPv6 unique local
      /^fe80:/,                   // IPv6 link-local
      /^ff00:/                    // IPv6 multicast
    ];
    
    return privateRanges.some(range => range.test(ip));
  }
  
  static sanitizeIp(ip: any): string | null {
    if (!ip || typeof ip !== 'string') return null;
    
    // Remove any whitespace
    ip = ip.trim();
    
    // Basic validation
    if (!this.isValidIp(ip)) return null;
    
    return ip;
  }
}
```

## Testing Strategy

### Backend Testing Approach

#### Unit Testing
- Service layer unit tests using Jest
- Mock external API calls (Freemius, ipRegistry)
- Test error handling and edge cases
- Validate data transformation and proxy logic
- Test IP validation and filtering

#### Integration Testing
- API endpoint testing with Supertest
- Database integration tests with test database
- Session management testing
- Authentication and authorization flow testing
- Webhook processing testing

#### Security Testing
- Input validation testing
- SQL injection prevention testing
- XSS prevention testing
- Rate limiting testing
- CSRF protection testing
- Session security testing

### Frontend Testing Approach

#### Component Testing
- React component unit tests with React Testing Library
- User interaction testing
- State management testing
- Error boundary testing

#### Integration Testing
- API integration testing with mock backend
- Authentication flow testing
- Route protection testing
- Form submission testing

#### End-to-End Testing
- Critical user journey testing
- Cross-browser compatibility testing
- Performance testing
- Accessibility testing

### Testing Infrastructure

#### Test Database Setup
```typescript
// Test database configuration
const testConfig = {
  provider: 'sqlite',
  url: 'file:./test.db'
};

// Test data seeding
async function seedTestData() {
  await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password: await bcrypt.hash('testpassword', 10),
      role: 'SUPER_ADMIN',
      first_name: 'Test',
      last_name: 'User'
    }
  });
}
```

#### Mock Services
```typescript
// Mock Freemius API responses
const mockFreemiusService = {
  getProduct: jest.fn().mockResolvedValue(mockProductData),
  getInstallation: jest.fn().mockResolvedValue(mockInstallationData),
  validateWebhookSignature: jest.fn().mockReturnValue({ isValid: true })
};

// Mock ipRegistry API responses
const mockIpIntelligenceService = {
  getIpData: jest.fn().mockResolvedValue(mockIpRegistryData),
  refreshIpData: jest.fn().mockResolvedValue(mockIpData),
  isValidPublicIp: jest.fn().mockReturnValue(true),
  isPrivateOrReservedIp: jest.fn().mockReturnValue(false)
};
```

This design provides a comprehensive approach to realigning the GuardGeo platform as a simple proxy service between ipRegistry and the WordPress plugin, while maintaining the existing technology stack and ensuring proper separation of concerns, security, session management, and Sentry logging practices. The platform focuses on data retrieval and admin monitoring rather than decision-making or risk assessment.