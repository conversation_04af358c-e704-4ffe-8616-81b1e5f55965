# Project Structure

## Root Level
- **Configuration files**: `package.json`, `tsconfig.json`, `vite.config.ts`, `tailwind.config.js`, `eslint.config.js`
- **Environment**: `.env.example` template, `.env` for local config
- **Docker**: `Dockerfile`, `docker-compose.yml`, `nginx.conf`
- **Database**: `prisma/` directory with schema and seed files

## Source Code Organization (`src/`)

### Core Application
- `main.tsx` - Application entry point
- `App.tsx` - Main app component with routing setup
- `index.css` - Global styles and Tailwind imports
- `vite-env.d.ts` - Vite type definitions

### Components (`src/components/`)
- `AdminLayout.tsx` - Main admin dashboard layout
- `ProtectedRoute.tsx` - Route protection with role-based access

### Pages (`src/pages/`)
All page components follow `*Page.tsx` naming convention:
- Authentication: `LoginPage.tsx`, `TwoFactorAuthPage.tsx`
- Core admin: `DashboardPage.tsx`, `UserProfilePage.tsx`
- Management: `UserManagementPage.tsx`, `SystemSettingsPage.tsx`
- Monitoring: `AdminActivitiesPage.tsx`, `ApiLogsPage.tsx`
- Operations: `ManualTasksPage.tsx`, `QueueManagementPage.tsx`

### Business Logic (`src/lib/`)
- `auth.ts` - Authentication utilities and session management
- `prisma.ts` - Database client configuration
- `profile-manager.ts` - User profile operations
- `settings-manager.ts` - System settings operations

### Data Layer
- `src/services/api.ts` - API client and HTTP utilities
- `src/types/index.ts` - TypeScript type definitions
- `src/contexts/AuthContext.tsx` - Authentication state management

### Utilities (`src/utils/`)
- `formatters.ts` - Data formatting utilities
- `validation.ts` - Input validation helpers

## Database Schema (`prisma/`)
- `schema.prisma` - Prisma schema with multi-database support
- `seed.ts` - Database seeding script for super admin creation

## Naming Conventions
- **Components**: PascalCase with descriptive suffixes (`AdminLayout`, `ProtectedRoute`)
- **Pages**: PascalCase ending with `Page` (`DashboardPage`)
- **Utilities**: camelCase with descriptive names (`formatters.ts`)
- **Types**: Interfaces in PascalCase, enums in SCREAMING_SNAKE_CASE
- **Database**: snake_case for table and column names

## Import Patterns
- Relative imports for local modules
- Absolute imports from `src/` root
- External libraries imported by package name
- Type-only imports use `import type` syntax