# Technology Stack

## Frontend
- **React 18** with TypeScript for type safety
- **Vite** as build tool and development server
- **Tai<PERSON>windCSS** for styling with utility-first approach
- **React Router DOM v7** for client-side routing
- **Axios** for HTTP requests

## Backend/Database
- **Prisma ORM** with multi-database support (SQLite, PostgreSQL, MySQL)
- **bcryptjs** for password hashing
- **date-fns** for date manipulation

## Icons & UI
- **Lucide React** - Primary icon library
- **Heroicons** - Secondary icon library

## Development Tools
- **TypeScript** with strict configuration
- **ESLint** with React hooks and TypeScript rules
- **PostCSS** with Autoprefixer
- **Docker** support with nginx configuration

## Build System

### Development Commands
```bash
npm run dev          # Start Vite development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run lint         # Run ESLint
```

### Database Commands
```bash
npm run db:generate  # Generate Prisma client
npm run db:push      # Push schema to database (development)
npm run db:migrate   # Create and run migrations (production)
npm run db:studio    # Open Prisma Studio
npm run db:seed      # Seed database with super admin
```

## Environment Configuration

- Uses `.env` files for configuration
- Supports multiple database providers via `DATABASE_PROVIDER` env var
- Requires super admin credentials setup for initial deployment
- Prisma client generates to `src/generated/prisma`

## Deployment

- Docker support with multi-stage builds
- nginx configuration for production serving
- Environment-based database configuration