# Product Overview

This is a comprehensive admin platform designed for managing multi-database applications with role-based access control. The platform provides administrative capabilities for user management, system monitoring, and configuration management.

## Core Features

- **Multi-database support**: SQLite, PostgreSQL, and MySQL via Prisma ORM
- **Role-based authentication**: Super Admin, Admin, and Moderator roles with granular permissions
- **Session management**: Secure session handling with IP tracking and device fingerprinting
- **Activity logging**: Comprehensive audit trail for all administrative actions
- **User profile management**: Customizable user preferences and settings
- **Manual task management**: Task assignment and tracking system
- **System settings**: Centralized configuration management with change logging

## Target Users

- System administrators managing multi-tenant applications
- Development teams requiring administrative oversight
- Organizations needing audit trails and user management capabilities

## Security Focus

The platform emphasizes security with features like password hashing, session management, IP tracking, and comprehensive activity logging for compliance and monitoring purposes.