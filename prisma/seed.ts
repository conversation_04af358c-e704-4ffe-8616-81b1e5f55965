import { PrismaClient } from '../src/generated/prisma';
import * as dotenv from 'dotenv';
import { createSuperAdminIfNotExists } from '../src/lib/auth';

dotenv.config();

const prisma = new PrismaClient();

async function main() {
  console.log('Starting database seed...');

  try {
    // Use the enhanced createSuperAdminIfNotExists function with security controls
    const superAdmin = await createSuperAdminIfNotExists('seed-script', 'database-seed');

    if (superAdmin) {
      console.log('Super admin initialization completed successfully:');
      console.log(`Email: ${superAdmin.email}`);
      console.log('Super admin is ready for use with enhanced security controls.');
    }
  } catch (error) {
    console.error('Error during super admin initialization:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
