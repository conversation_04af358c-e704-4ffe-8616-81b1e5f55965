# Env: production | development
NODE_ENV=development

# Database Configuration
# Note: Use npm run db:switch <provider> to switch between database providers
# Supported providers: sqlite, postgresql, mysql

# SQLite (default)
DATABASE_URL=file:./dev.db

# PostgreSQL example:
# DATABASE_URL=postgresql://user:password@localhost:5432/database_name

# MySQL example:
# DATABASE_URL=mysql://user:password@localhost:3306/database_name

# Default Super Admin Configuration
# These credentials will be used to create the initial super admin account
# After first login, you can change these credentials from the platform
SUPERADMIN_FIRST_NAME=Super
SUPERADMIN_LAST_NAME=Admin
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_USERNAME=superadmin
SUPERADMIN_PASSWORD=ChangeMe123!

# Freemius API Configuration
FREEMIUS_API_URL=https://api.freemius.com/v1
FREEMIUS_API_KEY=your_api_key_here
FREEMIUS_PRODUCT_ID=your_product_id_here
FREEMIUS_PRODUCT_SECRET_KEY=your_product_secret_key_here
FREEMIUS_TIMEOUT=30000
FREEMIUS_RETRY_ATTEMPTS=3
FREEMIUS_RETRY_DELAY=1000

# ipRegistry API Configuration
IPREGISTRY_API_URL=https://api.ipregistry.co
IPREGISTRY_API_KEY=your_api_key_here
IPREGISTRY_TIMEOUT=10000
IPREGISTRY_RETRY_ATTEMPTS=2
IPREGISTRY_RETRY_DELAY=1000

# IP Data Configuration
IP_DATA_FRESHNESS_DAYS=3

# Sentry.io Error Tracking and Performance Monitoring
SENTRY_DSN=https://<EMAIL>/****************

# API Configuration
VITE_API_BASE_URL=http://localhost:3001/api