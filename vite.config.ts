import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// Server-side modules that should be excluded from frontend build
const getServerExternals = () => [
  // Database and ORM
  '@prisma/client',
  'prisma',

  // Server-side dependencies
  'express',
  'bcryptjs',
  'helmet',
  'cors',
  'cookie-parser',
  'dotenv',
  'tsx',
  'concurrently',
  'nodemon',
  'express-rate-limit',
  'express-session',
  'jsonwebtoken',
  'multer',
  'compression',
  'morgan',

  // Node.js built-ins
  'crypto',
  'fs',
  'path',
  'os',
  'util',
  'stream',
  'events',
  'buffer',
  'querystring',
  'url',
  'http',
  'https',
  'net',
  'tls',
  'zlib',
  'child_process',
  'cluster',
  'dns',
  'readline',
  'worker_threads',
  'perf_hooks',
  'inspector',
  'async_hooks',

  // Server-side services and lib modules
  'src/lib/prisma',
  'src/lib/auth',
  'src/lib/session-manager',
  'src/lib/security-logger',
  'src/lib/audit-system',
  'src/lib/rate-limiter',
  'src/lib/password-manager',
  'src/lib/admin-manager',
  'src/lib/profile-manager',
  'src/lib/settings-manager',
  'src/lib/data-validation',
  'src/lib/data-retention',
  'src/lib/database-optimizer',
  'src/lib/query-optimizer',
  'src/lib/performance-monitor',
  'src/lib/security-monitoring',
  'src/lib/security-alerting',
  'src/lib/maintenance-scheduler',
  'src/lib/migration-utils',
  'src/lib/external-api-rate-limiter',
  'src/lib/password-reset-workflow',
  'src/services/freemius-service',
  'src/services/ip-intelligence-service',

  // API and middleware modules
  'src/api/server',
  'src/api/routes',
  'src/api/auth',
  'src/api/sessions',
  'src/middleware/auth-middleware',
  'src/middleware/authorization-middleware',
  'src/middleware/csrf-middleware',
  'src/middleware/input-validation-middleware',
  'src/middleware/rate-limit-middleware',
  'src/middleware/route-protection',
  'src/middleware/security-headers-middleware',
  'src/middleware/security-stack',
  'src/middleware/index',

  // Generated Prisma client
  'src/generated/prisma',
  '../generated/prisma',

  // Additional server-side patterns
  /^node:/,  // Node.js protocol imports (node:fs, node:path, etc.)
  /^@prisma\//,  // Any Prisma-related imports
  /^src\/api\//,  // Any API module imports
  /^src\/lib\//,  // Any lib module imports (server-side utilities)
  /^src\/middleware\//,  // Any middleware imports
  /^src\/services\/.*-service$/,  // Service modules ending with -service
];

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
  define: {
    // Ensure Node.js globals are not available in browser
    global: 'globalThis',
  },
  server: {
    proxy: {
      '/api/v1': {
        target: 'http://localhost:3001',
        changeOrigin: true,
        secure: false
      }
    }
  },
  build: {
    rollupOptions: {
      external: getServerExternals()
    }
  }
});
