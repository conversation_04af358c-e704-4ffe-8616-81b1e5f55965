{"name": "vite-react-typescript-starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "server": "tsx src/api/server.ts", "dev:full": "concurrently \"npm run server\" \"npm run dev\"", "test": "vitest --run", "test:watch": "vitest", "test:ui": "vitest --ui", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:switch": "node scripts/switch-db.js"}, "dependencies": {"@heroicons/react": "^2.2.0", "@prisma/client": "^6.16.3", "@sentry/node": "^10.17.0", "@sentry/profiling-node": "^10.17.0", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^10.0.0", "axios": "^1.12.2", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "date-fns": "^4.1.0", "express": "^5.1.0", "helmet": "^8.1.0", "lucide-react": "^0.344.0", "prisma": "^6.16.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.9.2", "uuid": "^13.0.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.9.1", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/mocha": "^10.0.10", "@types/node": "^24.6.2", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "concurrently": "^9.2.1", "dotenv": "^17.2.3", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jsdom": "^27.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tsx": "^4.20.6", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^3.2.4"}}